<?xml version="1.0" encoding="utf-8"?>
<policyDefinitionResources revision="1.0" schemaVersion="1.0">
	<displayName />
	<description />
	<resources>
		<stringTable>
			<string id="Application">Visual Studio Code</string>
			<string id="Supported_1_101">Visual Studio Code &gt;= 1.101</string>
			<string id="Supported_1_67">Visual Studio Code &gt;= 1.67</string>
			<string id="Supported_1_96">Visual Studio Code &gt;= 1.96</string>
			<string id="Supported_1_99">Visual Studio Code &gt;= 1.99</string>
			<string id="Category_extensionsConfigurationTitle">Расширения</string>
			<string id="Category_interactiveSessionConfigurationTitle">Чат</string>
			<string id="Category_updateConfigurationTitle">Обновить</string>
			<string id="Category_telemetryConfigurationTitle">Телеметрия</string>
			<string id="ExtensionGalleryServiceUrl">ExtensionGalleryServiceUrl</string>
			<string id="ExtensionGalleryServiceUrl_extensions_gallery_serviceUrl">Настройте URL-адрес службы Marketplace для подключения</string>
			<string id="ChatToolsAutoApprove">ChatToolsAutoApprove</string>
			<string id="ChatToolsAutoApprove_chat_tools_autoApprove">Определяет, следует ли автоматически утверждать использование инструмента.</string>
			<string id="ChatMCP">ChatMCP</string>
			<string id="ChatMCP_chat_mcp_enabled">Включает интеграцию с серверами протокола контекста модели для предоставления дополнительных инструментов и функций.</string>
			<string id="ChatAgentExtensionTools">ChatAgentExtensionTools</string>
			<string id="ChatAgentExtensionTools_chat_extensionToolsPolicy">Включите использование инструментов, предоставленных сторонними расширениями.</string>
			<string id="ChatAgentMode">ChatAgentMode</string>
			<string id="ChatAgentMode_chat_agent_enabled_description">Включить режим агента для {0}. Если включено, режим агента можно активировать через раскрывающееся меню в представлении.</string>
			<string id="McpGalleryServiceUrl">McpGalleryServiceUrl</string>
			<string id="McpGalleryServiceUrl_mcp_gallery_serviceUrl">Настройте URL-адрес службы галереи MCP для подключения</string>
			<string id="ChatPromptFiles">ChatPromptFiles</string>
			<string id="ChatPromptFiles_chat_promptFiles_policy">Включает файлы запросов многократного использования и инструкций в сеансах чата, Edits и встроенного чата.</string>
			<string id="UpdateMode">UpdateMode</string>
			<string id="UpdateMode_updateMode">Укажите, нужно ли вам получать автоматические обновления. После изменения требуется перезагрузка. Для получения обновлений используется веб-служба Майкрософт.</string>
			<string id="UpdateMode_none">Отключите обновления.</string>
			<string id="UpdateMode_manual">Отключение автоматических фоновых проверок на наличие обновлений. Обновления будут доступны, если вы вручную проверите их наличие.</string>
			<string id="UpdateMode_start">Проверять наличие обновлений только при запуске. Отключить автоматическую проверку обновлений в фоновом режиме.</string>
			<string id="UpdateMode_default">Включение автоматических проверок обновлений. Code будет периодически проверять наличие обновлений в автоматическом режиме.</string>
			<string id="TelemetryLevel">TelemetryLevel</string>
			<string id="TelemetryLevel_telemetry_telemetryLevel_policyDescription">Управляет уровнем телеметрии.</string>
			<string id="EnableFeedback">EnableFeedback</string>
			<string id="EnableFeedback_telemetry_feedback_enabled">Включите механизмы обратной связи, такие как средство сообщения о проблемах, опросы и возможности обратной связи в таких функциях, как Copilot Chat.</string>
			<string id="AllowedExtensions">AllowedExtensions</string>
			<string id="AllowedExtensions_extensions_allowed_policy">Укажите список расширений, которые можно использовать. Это помогает поддерживать безопасную и согласованную среду разработки, запрещая использование несанкционированных расширений. Подробнее: https://code.visualstudio.com/docs/setup/enterprise#_configure-allowed-extensions</string>
		</stringTable>
		<presentationTable>
			<presentation id="ExtensionGalleryServiceUrl"><textBox refId="ExtensionGalleryServiceUrl"><label>ExtensionGalleryServiceUrl:</label></textBox></presentation>
			<presentation id="ChatToolsAutoApprove"><checkBox refId="ChatToolsAutoApprove">ChatToolsAutoApprove</checkBox></presentation>
			<presentation id="ChatMCP"><checkBox refId="ChatMCP">ChatMCP</checkBox></presentation>
			<presentation id="ChatAgentExtensionTools"><checkBox refId="ChatAgentExtensionTools">ChatAgentExtensionTools</checkBox></presentation>
			<presentation id="ChatAgentMode"><checkBox refId="ChatAgentMode">ChatAgentMode</checkBox></presentation>
			<presentation id="McpGalleryServiceUrl"><textBox refId="McpGalleryServiceUrl"><label>McpGalleryServiceUrl:</label></textBox></presentation>
			<presentation id="ChatPromptFiles"><checkBox refId="ChatPromptFiles">ChatPromptFiles</checkBox></presentation>
			<presentation id="UpdateMode"><dropdownList refId="UpdateMode" /></presentation>
			<presentation id="TelemetryLevel"><textBox refId="TelemetryLevel"><label>TelemetryLevel:</label></textBox></presentation>
			<presentation id="EnableFeedback"><checkBox refId="EnableFeedback">EnableFeedback</checkBox></presentation>
			<presentation id="AllowedExtensions"><multiTextBox refId="AllowedExtensions" /></presentation>
		</presentationTable>
	</resources>
</policyDefinitionResources>
