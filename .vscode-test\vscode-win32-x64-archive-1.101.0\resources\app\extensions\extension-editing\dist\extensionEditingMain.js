(()=>{var e={5887:(e,t,n)=>{"use strict";function r(e,t=!1){const n=e.length;let r=0,a="",c=0,l=16,u=0,p=0,h=0,f=0,T=0;function d(t,n){let s=0,i=0;for(;s<t||!n;){let t=e.charCodeAt(r);if(t>=48&&t<=57)i=16*i+t-48;else if(t>=65&&t<=70)i=16*i+t-65+10;else{if(!(t>=97&&t<=102))break;i=16*i+t-97+10}r++,s++}return s<t&&(i=-1),i}function m(){if(a="",T=0,c=r,p=u,f=h,r>=n)return c=n,l=17;let t=e.charCodeAt(r);if(s(t)){do{r++,a+=String.fromCharCode(t),t=e.charCodeAt(r)}while(s(t));return l=15}if(i(t))return r++,a+=String.fromCharCode(t),13===t&&10===e.charCodeAt(r)&&(r++,a+="\n"),u++,h=r,l=14;switch(t){case 123:return r++,l=1;case 125:return r++,l=2;case 91:return r++,l=3;case 93:return r++,l=4;case 58:return r++,l=6;case 44:return r++,l=5;case 34:return r++,a=function(){let t="",s=r;for(;;){if(r>=n){t+=e.substring(s,r),T=2;break}const o=e.charCodeAt(r);if(34===o){t+=e.substring(s,r),r++;break}if(92!==o){if(o>=0&&o<=31){if(i(o)){t+=e.substring(s,r),T=2;break}T=6}r++}else{if(t+=e.substring(s,r),r++,r>=n){T=2;break}switch(e.charCodeAt(r++)){case 34:t+='"';break;case 92:t+="\\";break;case 47:t+="/";break;case 98:t+="\b";break;case 102:t+="\f";break;case 110:t+="\n";break;case 114:t+="\r";break;case 116:t+="\t";break;case 117:const e=d(4,!0);e>=0?t+=String.fromCharCode(e):T=4;break;default:T=5}s=r}}return t}(),l=10;case 47:const s=r-1;if(47===e.charCodeAt(r+1)){for(r+=2;r<n&&!i(e.charCodeAt(r));)r++;return a=e.substring(s,r),l=12}if(42===e.charCodeAt(r+1)){r+=2;const t=n-1;let o=!1;for(;r<t;){const t=e.charCodeAt(r);if(42===t&&47===e.charCodeAt(r+1)){r+=2,o=!0;break}r++,i(t)&&(13===t&&10===e.charCodeAt(r)&&r++,u++,h=r)}return o||(r++,T=1),a=e.substring(s,r),l=13}return a+=String.fromCharCode(t),r++,l=16;case 45:if(a+=String.fromCharCode(t),r++,r===n||!o(e.charCodeAt(r)))return l=16;case 48:case 49:case 50:case 51:case 52:case 53:case 54:case 55:case 56:case 57:return a+=function(){let t=r;if(48===e.charCodeAt(r))r++;else for(r++;r<e.length&&o(e.charCodeAt(r));)r++;if(r<e.length&&46===e.charCodeAt(r)){if(r++,!(r<e.length&&o(e.charCodeAt(r))))return T=3,e.substring(t,r);for(r++;r<e.length&&o(e.charCodeAt(r));)r++}let n=r;if(r<e.length&&(69===e.charCodeAt(r)||101===e.charCodeAt(r)))if(r++,(r<e.length&&43===e.charCodeAt(r)||45===e.charCodeAt(r))&&r++,r<e.length&&o(e.charCodeAt(r))){for(r++;r<e.length&&o(e.charCodeAt(r));)r++;n=r}else T=3;return e.substring(t,n)}(),l=11;default:for(;r<n&&E(t);)r++,t=e.charCodeAt(r);if(c!==r){switch(a=e.substring(c,r),a){case"true":return l=8;case"false":return l=9;case"null":return l=7}return l=16}return a+=String.fromCharCode(t),r++,l=16}}function E(e){if(s(e)||i(e))return!1;switch(e){case 125:case 93:case 123:case 91:case 34:case 58:case 44:case 47:return!1}return!0}return{setPosition:function(e){r=e,a="",c=0,l=16,T=0},getPosition:()=>r,scan:t?function(){let e;do{e=m()}while(e>=12&&e<=15);return e}:m,getToken:()=>l,getTokenValue:()=>a,getTokenOffset:()=>c,getTokenLength:()=>r-c,getTokenStartLine:()=>p,getTokenStartCharacter:()=>c-f,getTokenError:()=>T}}function s(e){return 32===e||9===e}function i(e){return 10===e||13===e}function o(e){return e>=48&&e<=57}var a,c;function l(e,t,n){let s,i,o,a,c;if(t){for(a=t.offset,c=a+t.length,o=a;o>0&&!p(e,o-1);)o--;let r=c;for(;r<e.length&&!p(e,r);)r++;i=e.substring(o,r),s=function(e,t){let n=0,r=0;const s=t.tabSize||4;for(;n<e.length;){let t=e.charAt(n);if(" "===t)r++;else{if("\t"!==t)break;r+=s}n++}return Math.floor(r/s)}(i,n)}else i=e,s=0,o=0,a=0,c=e.length;const l=function(e,t){for(let e=0;e<t.length;e++){const n=t.charAt(e);if("\r"===n)return e+1<t.length&&"\n"===t.charAt(e+1)?"\r\n":"\r";if("\n"===n)return"\n"}return e&&e.eol||"\n"}(n,e);let h,f=0,T=0;h=n.insertSpaces?u(" ",n.tabSize||4):"\t";let d=r(i,!1),m=!1;function E(){return f>1?u(l,f)+u(h,s+T):l+u(h,s+T)}function _(){let e=d.scan();for(f=0;15===e||14===e;)14===e&&n.keepLines?f+=1:14===e&&(f=1),e=d.scan();return m=16===e||0!==d.getTokenError(),e}const g=[];function A(n,r,s){m||t&&!(r<c&&s>a)||e.substring(r,s)===n||g.push({offset:r,length:s-r,content:n})}let C=_();if(n.keepLines&&f>0&&A(u(l,f),0,0),17!==C){let e=d.getTokenOffset()+o;A(u(h,s),o,e)}for(;17!==C;){let e=d.getTokenOffset()+d.getTokenLength()+o,t=_(),r="",s=!1;for(;0===f&&(12===t||13===t);)A(" ",e,d.getTokenOffset()+o),e=d.getTokenOffset()+d.getTokenLength()+o,s=12===t,r=s?E():"",t=_();if(2===t)1!==C&&T--,n.keepLines&&f>0||!n.keepLines&&1!==C?r=E():n.keepLines&&(r=" ");else if(4===t)3!==C&&T--,n.keepLines&&f>0||!n.keepLines&&3!==C?r=E():n.keepLines&&(r=" ");else{switch(C){case 3:case 1:T++,r=n.keepLines&&f>0||!n.keepLines?E():" ";break;case 5:r=n.keepLines&&f>0||!n.keepLines?E():" ";break;case 12:r=E();break;case 13:f>0?r=E():s||(r=" ");break;case 6:n.keepLines&&f>0?r=E():s||(r=" ");break;case 10:n.keepLines&&f>0?r=E():6!==t||s||(r="");break;case 7:case 8:case 9:case 11:case 2:case 4:n.keepLines&&f>0?r=E():12!==t&&13!==t||s?5!==t&&17!==t&&(m=!0):r=" ";break;case 16:m=!0}f>0&&(12===t||13===t)&&(r=E())}17===t&&(r=n.keepLines&&f>0?E():n.insertFinalNewline?l:""),A(r,e,d.getTokenOffset()+o),C=t}return g}function u(e,t){let n="";for(let r=0;r<t;r++)n+=e;return n}function p(e,t){return-1!=="\r\n".indexOf(e.charAt(t))}function h(e,t=[],n=c.DEFAULT){let r={type:"array",offset:-1,length:-1,children:[],parent:void 0};function s(e){"property"===r.type&&(r.length=e-r.offset,r=r.parent)}function i(e){return r.children.push(e),e}T(e,{onObjectBegin:e=>{r=i({type:"object",offset:e,length:-1,parent:r,children:[]})},onObjectProperty:(e,t,n)=>{r=i({type:"property",offset:t,length:-1,parent:r,children:[]}),r.children.push({type:"string",value:e,offset:t,length:n,parent:r})},onObjectEnd:(e,t)=>{s(e+t),r.length=e+t-r.offset,r=r.parent,s(e+t)},onArrayBegin:(e,t)=>{r=i({type:"array",offset:e,length:-1,parent:r,children:[]})},onArrayEnd:(e,t)=>{r.length=e+t-r.offset,r=r.parent,s(e+t)},onLiteralValue:(e,t,n)=>{i({type:d(e),offset:t,length:n,parent:r,value:e}),s(t+n)},onSeparator:(e,t,n)=>{"property"===r.type&&(":"===e?r.colonOffset=t:","===e&&s(t))},onError:(e,n,r)=>{t.push({error:e,offset:n,length:r})}},n);const o=r.children[0];return o&&delete o.parent,o}function f(e,t){if(!e)return;let n=e;for(let e of t)if("string"==typeof e){if("object"!==n.type||!Array.isArray(n.children))return;let t=!1;for(const r of n.children)if(Array.isArray(r.children)&&r.children[0].value===e&&2===r.children.length){n=r.children[1],t=!0;break}if(!t)return}else{const t=e;if("array"!==n.type||t<0||!Array.isArray(n.children)||t>=n.children.length)return;n=n.children[t]}return n}function T(e,t,n=c.DEFAULT){const s=r(e,!1),i=[];function o(e){return e?()=>e(s.getTokenOffset(),s.getTokenLength(),s.getTokenStartLine(),s.getTokenStartCharacter()):()=>!0}function a(e){return e?()=>e(s.getTokenOffset(),s.getTokenLength(),s.getTokenStartLine(),s.getTokenStartCharacter(),(()=>i.slice())):()=>!0}function l(e){return e?t=>e(t,s.getTokenOffset(),s.getTokenLength(),s.getTokenStartLine(),s.getTokenStartCharacter()):()=>!0}function u(e){return e?t=>e(t,s.getTokenOffset(),s.getTokenLength(),s.getTokenStartLine(),s.getTokenStartCharacter(),(()=>i.slice())):()=>!0}const p=a(t.onObjectBegin),h=u(t.onObjectProperty),f=o(t.onObjectEnd),T=a(t.onArrayBegin),d=o(t.onArrayEnd),m=u(t.onLiteralValue),E=l(t.onSeparator),_=o(t.onComment),g=l(t.onError),A=n&&n.disallowComments,C=n&&n.allowTrailingComma;function N(){for(;;){const e=s.scan();switch(s.getTokenError()){case 4:k(14);break;case 5:k(15);break;case 3:k(13);break;case 1:A||k(11);break;case 2:k(12);break;case 6:k(16)}switch(e){case 12:case 13:A?k(10):_();break;case 16:k(1);break;case 15:case 14:break;default:return e}}}function k(e,t=[],n=[]){if(g(e),t.length+n.length>0){let e=s.getToken();for(;17!==e;){if(-1!==t.indexOf(e)){N();break}if(-1!==n.indexOf(e))break;e=N()}}}function O(e){const t=s.getTokenValue();return e?m(t):(h(t),i.push(t)),N(),!0}return N(),17===s.getToken()?!!n.allowEmptyContent||(k(4,[],[]),!1):function e(){switch(s.getToken()){case 3:return function(){T(),N();let t=!0,n=!1;for(;4!==s.getToken()&&17!==s.getToken();){if(5===s.getToken()){if(n||k(4,[],[]),E(","),N(),4===s.getToken()&&C)break}else n&&k(6,[],[]);t?(i.push(0),t=!1):i[i.length-1]++,e()||k(4,[],[4,5]),n=!0}return d(),t||i.pop(),4!==s.getToken()?k(8,[4],[]):N(),!0}();case 1:return function(){p(),N();let t=!1;for(;2!==s.getToken()&&17!==s.getToken();){if(5===s.getToken()){if(t||k(4,[],[]),E(","),N(),2===s.getToken()&&C)break}else t&&k(6,[],[]);(10!==s.getToken()?(k(3,[],[2,5]),0):(O(!1),6===s.getToken()?(E(":"),N(),e()||k(4,[],[2,5])):k(5,[],[2,5]),i.pop(),1))||k(4,[],[2,5]),t=!0}return f(),2!==s.getToken()?k(7,[2],[]):N(),!0}();case 10:return O(!0);default:return function(){switch(s.getToken()){case 11:const e=s.getTokenValue();let t=Number(e);isNaN(t)&&(k(2),t=0),m(t);break;case 7:m(null);break;case 8:m(!0);break;case 9:m(!1);break;default:return!1}return N(),!0}()}}()?(17!==s.getToken()&&k(9,[],[]),!0):(k(4,[],[]),!1)}function d(e){switch(typeof e){case"boolean":return"boolean";case"number":return"number";case"string":return"string";case"object":return e?Array.isArray(e)?"array":"object":"null";default:return"null"}}function m(e,t,n){if(!n.formattingOptions)return[t];let r=E(e,t),s=t.offset,i=t.offset+t.content.length;if(0===t.length||0===t.content.length){for(;s>0&&!p(r,s-1);)s--;for(;i<r.length&&!p(r,i);)i++}const o=l(r,{offset:s,length:i-s},{...n.formattingOptions,keepLines:!1});for(let e=o.length-1;e>=0;e--){const t=o[e];r=E(r,t),s=Math.min(s,t.offset),i=Math.max(i,t.offset+t.length),i+=t.content.length-t.length}return[{offset:s,length:e.length-(r.length-i)-s,content:r.substring(s,i)}]}function E(e,t){return e.substring(0,t.offset)+t.content+e.substring(t.offset+t.length)}n.r(t),n.d(t,{ParseErrorCode:()=>I,ScanError:()=>g,SyntaxKind:()=>A,applyEdits:()=>P,createScanner:()=>_,findNodeAtLocation:()=>O,findNodeAtOffset:()=>S,format:()=>y,getLocation:()=>C,getNodePath:()=>L,getNodeValue:()=>v,modify:()=>D,parse:()=>N,parseTree:()=>k,printParseErrorCode:()=>M,stripComments:()=>R,visit:()=>b}),function(e){e[e.lineFeed=10]="lineFeed",e[e.carriageReturn=13]="carriageReturn",e[e.space=32]="space",e[e._0=48]="_0",e[e._1=49]="_1",e[e._2=50]="_2",e[e._3=51]="_3",e[e._4=52]="_4",e[e._5=53]="_5",e[e._6=54]="_6",e[e._7=55]="_7",e[e._8=56]="_8",e[e._9=57]="_9",e[e.a=97]="a",e[e.b=98]="b",e[e.c=99]="c",e[e.d=100]="d",e[e.e=101]="e",e[e.f=102]="f",e[e.g=103]="g",e[e.h=104]="h",e[e.i=105]="i",e[e.j=106]="j",e[e.k=107]="k",e[e.l=108]="l",e[e.m=109]="m",e[e.n=110]="n",e[e.o=111]="o",e[e.p=112]="p",e[e.q=113]="q",e[e.r=114]="r",e[e.s=115]="s",e[e.t=116]="t",e[e.u=117]="u",e[e.v=118]="v",e[e.w=119]="w",e[e.x=120]="x",e[e.y=121]="y",e[e.z=122]="z",e[e.A=65]="A",e[e.B=66]="B",e[e.C=67]="C",e[e.D=68]="D",e[e.E=69]="E",e[e.F=70]="F",e[e.G=71]="G",e[e.H=72]="H",e[e.I=73]="I",e[e.J=74]="J",e[e.K=75]="K",e[e.L=76]="L",e[e.M=77]="M",e[e.N=78]="N",e[e.O=79]="O",e[e.P=80]="P",e[e.Q=81]="Q",e[e.R=82]="R",e[e.S=83]="S",e[e.T=84]="T",e[e.U=85]="U",e[e.V=86]="V",e[e.W=87]="W",e[e.X=88]="X",e[e.Y=89]="Y",e[e.Z=90]="Z",e[e.asterisk=42]="asterisk",e[e.backslash=92]="backslash",e[e.closeBrace=125]="closeBrace",e[e.closeBracket=93]="closeBracket",e[e.colon=58]="colon",e[e.comma=44]="comma",e[e.dot=46]="dot",e[e.doubleQuote=34]="doubleQuote",e[e.minus=45]="minus",e[e.openBrace=123]="openBrace",e[e.openBracket=91]="openBracket",e[e.plus=43]="plus",e[e.slash=47]="slash",e[e.formFeed=12]="formFeed",e[e.tab=9]="tab"}(a||(a={})),function(e){e.DEFAULT={allowTrailingComma:!1}}(c||(c={}));const _=r;var g,A;!function(e){e[e.None=0]="None",e[e.UnexpectedEndOfComment=1]="UnexpectedEndOfComment",e[e.UnexpectedEndOfString=2]="UnexpectedEndOfString",e[e.UnexpectedEndOfNumber=3]="UnexpectedEndOfNumber",e[e.InvalidUnicode=4]="InvalidUnicode",e[e.InvalidEscapeCharacter=5]="InvalidEscapeCharacter",e[e.InvalidCharacter=6]="InvalidCharacter"}(g||(g={})),function(e){e[e.OpenBraceToken=1]="OpenBraceToken",e[e.CloseBraceToken=2]="CloseBraceToken",e[e.OpenBracketToken=3]="OpenBracketToken",e[e.CloseBracketToken=4]="CloseBracketToken",e[e.CommaToken=5]="CommaToken",e[e.ColonToken=6]="ColonToken",e[e.NullKeyword=7]="NullKeyword",e[e.TrueKeyword=8]="TrueKeyword",e[e.FalseKeyword=9]="FalseKeyword",e[e.StringLiteral=10]="StringLiteral",e[e.NumericLiteral=11]="NumericLiteral",e[e.LineCommentTrivia=12]="LineCommentTrivia",e[e.BlockCommentTrivia=13]="BlockCommentTrivia",e[e.LineBreakTrivia=14]="LineBreakTrivia",e[e.Trivia=15]="Trivia",e[e.Unknown=16]="Unknown",e[e.EOF=17]="EOF"}(A||(A={}));const C=function(e,t){const n=[],r=new Object;let s;const i={value:{},offset:0,length:0,type:"object",parent:void 0};let o=!1;function a(e,t,n,r){i.value=e,i.offset=t,i.length=n,i.type=r,i.colonOffset=void 0,s=i}try{T(e,{onObjectBegin:(e,i)=>{if(t<=e)throw r;s=void 0,o=t>e,n.push("")},onObjectProperty:(e,s,i)=>{if(t<s)throw r;if(a(e,s,i,"property"),n[n.length-1]=e,t<=s+i)throw r},onObjectEnd:(e,i)=>{if(t<=e)throw r;s=void 0,n.pop()},onArrayBegin:(e,i)=>{if(t<=e)throw r;s=void 0,n.push(0)},onArrayEnd:(e,i)=>{if(t<=e)throw r;s=void 0,n.pop()},onLiteralValue:(e,n,s)=>{if(t<n)throw r;if(a(e,n,s,d(e)),t<=n+s)throw r},onSeparator:(e,i,a)=>{if(t<=i)throw r;if(":"===e&&s&&"property"===s.type)s.colonOffset=i,o=!1,s=void 0;else if(","===e){const e=n[n.length-1];"number"==typeof e?n[n.length-1]=e+1:(o=!0,n[n.length-1]=""),s=void 0}}})}catch(e){if(e!==r)throw e}return{path:n,previousNode:s,isAtPropertyKey:o,matches:e=>{let t=0;for(let r=0;t<e.length&&r<n.length;r++)if(e[t]===n[r]||"*"===e[t])t++;else if("**"!==e[t])return!1;return t===e.length}}},N=function(e,t=[],n=c.DEFAULT){let r=null,s=[];const i=[];function o(e){Array.isArray(s)?s.push(e):null!==r&&(s[r]=e)}return T(e,{onObjectBegin:()=>{const e={};o(e),i.push(s),s=e,r=null},onObjectProperty:e=>{r=e},onObjectEnd:()=>{s=i.pop()},onArrayBegin:()=>{const e=[];o(e),i.push(s),s=e,r=null},onArrayEnd:()=>{s=i.pop()},onLiteralValue:o,onError:(e,n,r)=>{t.push({error:e,offset:n,length:r})}},n),s[0]},k=h,O=f,S=function e(t,n,r=!1){if(function(e,t,n=!1){return t>=e.offset&&t<e.offset+e.length||n&&t===e.offset+e.length}(t,n,r)){const s=t.children;if(Array.isArray(s))for(let t=0;t<s.length&&s[t].offset<=n;t++){const i=e(s[t],n,r);if(i)return i}return t}},L=function e(t){if(!t.parent||!t.parent.children)return[];const n=e(t.parent);if("property"===t.parent.type){const e=t.parent.children[0].value;n.push(e)}else if("array"===t.parent.type){const e=t.parent.children.indexOf(t);-1!==e&&n.push(e)}return n},v=function e(t){switch(t.type){case"array":return t.children.map(e);case"object":const n=Object.create(null);for(let r of t.children){const t=r.children[1];t&&(n[r.children[0].value]=e(t))}return n;case"null":case"string":case"number":case"boolean":return t.value;default:return}},b=T,R=function(e,t){let n,s,i=r(e),o=[],a=0;do{switch(s=i.getPosition(),n=i.scan(),n){case 12:case 13:case 17:a!==s&&o.push(e.substring(a,s)),void 0!==t&&o.push(i.getTokenValue().replace(/[^\r\n]/g,t)),a=i.getPosition()}}while(17!==n);return o.join("")};var I;function M(e){switch(e){case 1:return"InvalidSymbol";case 2:return"InvalidNumberFormat";case 3:return"PropertyNameExpected";case 4:return"ValueExpected";case 5:return"ColonExpected";case 6:return"CommaExpected";case 7:return"CloseBraceExpected";case 8:return"CloseBracketExpected";case 9:return"EndOfFileExpected";case 10:return"InvalidCommentToken";case 11:return"UnexpectedEndOfComment";case 12:return"UnexpectedEndOfString";case 13:return"UnexpectedEndOfNumber";case 14:return"InvalidUnicode";case 15:return"InvalidEscapeCharacter";case 16:return"InvalidCharacter"}return"<unknown ParseErrorCode>"}function y(e,t,n){return l(e,t,n)}function D(e,t,n,r){return function(e,t,n,r){const s=t.slice(),i=h(e,[]);let o,a;for(;s.length>0&&(a=s.pop(),o=f(i,s),void 0===o&&void 0!==n);)n="string"==typeof a?{[a]:n}:[n];if(o){if("object"===o.type&&"string"==typeof a&&Array.isArray(o.children)){const t=f(o,[a]);if(void 0!==t){if(void 0===n){if(!t.parent)throw new Error("Malformed AST");const n=o.children.indexOf(t.parent);let s,i=t.parent.offset+t.parent.length;if(n>0){let e=o.children[n-1];s=e.offset+e.length}else s=o.offset+1,o.children.length>1&&(i=o.children[1].offset);return m(e,{offset:s,length:i-s,content:""},r)}return m(e,{offset:t.offset,length:t.length,content:JSON.stringify(n)},r)}{if(void 0===n)return[];const t=`${JSON.stringify(a)}: ${JSON.stringify(n)}`,s=r.getInsertionIndex?r.getInsertionIndex(o.children.map((e=>e.children[0].value))):o.children.length;let i;if(s>0){let e=o.children[s-1];i={offset:e.offset+e.length,length:0,content:","+t}}else i=0===o.children.length?{offset:o.offset+1,length:0,content:t}:{offset:o.offset+1,length:0,content:t+","};return m(e,i,r)}}if("array"===o.type&&"number"==typeof a&&Array.isArray(o.children)){const t=a;if(-1===t){const t=`${JSON.stringify(n)}`;let s;if(0===o.children.length)s={offset:o.offset+1,length:0,content:t};else{const e=o.children[o.children.length-1];s={offset:e.offset+e.length,length:0,content:","+t}}return m(e,s,r)}if(void 0===n&&o.children.length>=0){const t=a,n=o.children[t];let s;if(1===o.children.length)s={offset:o.offset+1,length:o.length-2,content:""};else if(o.children.length-1===t){let e=o.children[t-1],n=e.offset+e.length;s={offset:n,length:o.offset+o.length-2-n,content:""}}else s={offset:n.offset,length:o.children[t+1].offset-n.offset,content:""};return m(e,s,r)}if(void 0!==n){let t;const s=`${JSON.stringify(n)}`;if(!r.isArrayInsertion&&o.children.length>a){const e=o.children[a];t={offset:e.offset,length:e.length,content:s}}else if(0===o.children.length||0===a)t={offset:o.offset+1,length:0,content:0===o.children.length?s:s+","};else{const e=a>o.children.length?o.children.length:a,n=o.children[e-1];t={offset:n.offset+n.length,length:0,content:","+s}}return m(e,t,r)}throw new Error(`Can not ${void 0===n?"remove":r.isArrayInsertion?"insert":"modify"} Array index ${t} as length is not sufficient`)}throw new Error(`Can not add ${"number"!=typeof a?"index":"property"} to parent of type ${o.type}`)}if(void 0===n)throw new Error("Can not delete in empty document");return m(e,{offset:i?i.offset:0,length:i?i.length:0,content:JSON.stringify(n)},r)}(e,t,n,r)}function P(e,t){let n=t.slice(0).sort(((e,t)=>{const n=e.offset-t.offset;return 0===n?e.length-t.length:n})),r=e.length;for(let t=n.length-1;t>=0;t--){let s=n[t];if(!(s.offset+s.length<=r))throw new Error("Overlapping edit");e=E(e,s),r=s.offset}return e}!function(e){e[e.InvalidSymbol=1]="InvalidSymbol",e[e.InvalidNumberFormat=2]="InvalidNumberFormat",e[e.PropertyNameExpected=3]="PropertyNameExpected",e[e.ValueExpected=4]="ValueExpected",e[e.ColonExpected=5]="ColonExpected",e[e.CommaExpected=6]="CommaExpected",e[e.CloseBraceExpected=7]="CloseBraceExpected",e[e.CloseBracketExpected=8]="CloseBracketExpected",e[e.EndOfFileExpected=9]="EndOfFileExpected",e[e.InvalidCommentToken=10]="InvalidCommentToken",e[e.UnexpectedEndOfComment=11]="UnexpectedEndOfComment",e[e.UnexpectedEndOfString=12]="UnexpectedEndOfString",e[e.UnexpectedEndOfNumber=13]="UnexpectedEndOfNumber",e[e.InvalidUnicode=14]="InvalidUnicode",e[e.InvalidEscapeCharacter=15]="InvalidEscapeCharacter",e[e.InvalidCharacter=16]="InvalidCharacter"}(I||(I={}))},2833:(e,t,n)=>{"use strict";function r(e){return Array.prototype.slice.call(arguments,1).forEach((function(t){t&&Object.keys(t).forEach((function(n){e[n]=t[n]}))})),e}function s(e){return Object.prototype.toString.call(e)}function i(e){return"[object Function]"===s(e)}function o(e){return e.replace(/[.?*+^$[\]\\(){}|-]/g,"\\$&")}var a={fuzzyLink:!0,fuzzyEmail:!0,fuzzyIP:!1},c={"http:":{validate:function(e,t,n){var r=e.slice(t);return n.re.http||(n.re.http=new RegExp("^\\/\\/"+n.re.src_auth+n.re.src_host_port_strict+n.re.src_path,"i")),n.re.http.test(r)?r.match(n.re.http)[0].length:0}},"https:":"http:","ftp:":"http:","//":{validate:function(e,t,n){var r=e.slice(t);return n.re.no_http||(n.re.no_http=new RegExp("^"+n.re.src_auth+"(?:localhost|(?:(?:"+n.re.src_domain+")\\.)+"+n.re.src_domain_root+")"+n.re.src_port+n.re.src_host_terminator+n.re.src_path,"i")),n.re.no_http.test(r)?t>=3&&":"===e[t-3]||t>=3&&"/"===e[t-3]?0:r.match(n.re.no_http)[0].length:0}},"mailto:":{validate:function(e,t,n){var r=e.slice(t);return n.re.mailto||(n.re.mailto=new RegExp("^"+n.re.src_email_name+"@"+n.re.src_host_strict,"i")),n.re.mailto.test(r)?r.match(n.re.mailto)[0].length:0}}},l="a[cdefgilmnoqrstuwxz]|b[abdefghijmnorstvwyz]|c[acdfghiklmnoruvwxyz]|d[ejkmoz]|e[cegrstu]|f[ijkmor]|g[abdefghilmnpqrstuwy]|h[kmnrtu]|i[delmnoqrst]|j[emop]|k[eghimnprwyz]|l[abcikrstuvy]|m[acdeghklmnopqrstuvwxyz]|n[acefgilopruz]|om|p[aefghklmnrstwy]|qa|r[eosuw]|s[abcdeghijklmnortuvxyz]|t[cdfghjklmnortvwz]|u[agksyz]|v[aceginu]|w[fs]|y[et]|z[amw]",u="biz|com|edu|gov|net|org|pro|web|xxx|aero|asia|coop|info|museum|name|shop|рф".split("|");function p(e){var t=e.re=n(2879)(e.__opts__),r=e.__tlds__.slice();function a(e){return e.replace("%TLDS%",t.src_tlds)}e.onCompile(),e.__tlds_replaced__||r.push(l),r.push(t.src_xn),t.src_tlds=r.join("|"),t.email_fuzzy=RegExp(a(t.tpl_email_fuzzy),"i"),t.link_fuzzy=RegExp(a(t.tpl_link_fuzzy),"i"),t.link_no_ip_fuzzy=RegExp(a(t.tpl_link_no_ip_fuzzy),"i"),t.host_fuzzy_test=RegExp(a(t.tpl_host_fuzzy_test),"i");var c=[];function u(e,t){throw new Error('(LinkifyIt) Invalid schema "'+e+'": '+t)}e.__compiled__={},Object.keys(e.__schemas__).forEach((function(t){var n=e.__schemas__[t];if(null!==n){var r={validate:null,link:null};if(e.__compiled__[t]=r,"[object Object]"===s(n))return"[object RegExp]"!==s(n.validate)?i(n.validate)?r.validate=n.validate:u(t,n):r.validate=function(e){return function(t,n){var r=t.slice(n);return e.test(r)?r.match(e)[0].length:0}}(n.validate),void(i(n.normalize)?r.normalize=n.normalize:n.normalize?u(t,n):r.normalize=function(e,t){t.normalize(e)});!function(e){return"[object String]"===s(e)}(n)?u(t,n):c.push(t)}})),c.forEach((function(t){e.__compiled__[e.__schemas__[t]]&&(e.__compiled__[t].validate=e.__compiled__[e.__schemas__[t]].validate,e.__compiled__[t].normalize=e.__compiled__[e.__schemas__[t]].normalize)})),e.__compiled__[""]={validate:null,normalize:function(e,t){t.normalize(e)}};var p=Object.keys(e.__compiled__).filter((function(t){return t.length>0&&e.__compiled__[t]})).map(o).join("|");e.re.schema_test=RegExp("(^|(?!_)(?:[><｜]|"+t.src_ZPCc+"))("+p+")","i"),e.re.schema_search=RegExp("(^|(?!_)(?:[><｜]|"+t.src_ZPCc+"))("+p+")","ig"),e.re.pretest=RegExp("("+e.re.schema_test.source+")|("+e.re.host_fuzzy_test.source+")|@","i"),function(e){e.__index__=-1,e.__text_cache__=""}(e)}function h(e,t){var n=e.__index__,r=e.__last_index__,s=e.__text_cache__.slice(n,r);this.schema=e.__schema__.toLowerCase(),this.index=n+t,this.lastIndex=r+t,this.raw=s,this.text=s,this.url=s}function f(e,t){var n=new h(e,t);return e.__compiled__[n.schema].normalize(n,e),n}function T(e,t){if(!(this instanceof T))return new T(e,t);var n;t||(n=e,Object.keys(n||{}).reduce((function(e,t){return e||a.hasOwnProperty(t)}),!1)&&(t=e,e={})),this.__opts__=r({},a,t),this.__index__=-1,this.__last_index__=-1,this.__schema__="",this.__text_cache__="",this.__schemas__=r({},c,e),this.__compiled__={},this.__tlds__=u,this.__tlds_replaced__=!1,this.re={},p(this)}T.prototype.add=function(e,t){return this.__schemas__[e]=t,p(this),this},T.prototype.set=function(e){return this.__opts__=r(this.__opts__,e),this},T.prototype.test=function(e){if(this.__text_cache__=e,this.__index__=-1,!e.length)return!1;var t,n,r,s,i,o,a,c;if(this.re.schema_test.test(e))for((a=this.re.schema_search).lastIndex=0;null!==(t=a.exec(e));)if(s=this.testSchemaAt(e,t[2],a.lastIndex)){this.__schema__=t[2],this.__index__=t.index+t[1].length,this.__last_index__=t.index+t[0].length+s;break}return this.__opts__.fuzzyLink&&this.__compiled__["http:"]&&(c=e.search(this.re.host_fuzzy_test))>=0&&(this.__index__<0||c<this.__index__)&&null!==(n=e.match(this.__opts__.fuzzyIP?this.re.link_fuzzy:this.re.link_no_ip_fuzzy))&&(i=n.index+n[1].length,(this.__index__<0||i<this.__index__)&&(this.__schema__="",this.__index__=i,this.__last_index__=n.index+n[0].length)),this.__opts__.fuzzyEmail&&this.__compiled__["mailto:"]&&e.indexOf("@")>=0&&null!==(r=e.match(this.re.email_fuzzy))&&(i=r.index+r[1].length,o=r.index+r[0].length,(this.__index__<0||i<this.__index__||i===this.__index__&&o>this.__last_index__)&&(this.__schema__="mailto:",this.__index__=i,this.__last_index__=o)),this.__index__>=0},T.prototype.pretest=function(e){return this.re.pretest.test(e)},T.prototype.testSchemaAt=function(e,t,n){return this.__compiled__[t.toLowerCase()]?this.__compiled__[t.toLowerCase()].validate(e,n,this):0},T.prototype.match=function(e){var t=0,n=[];this.__index__>=0&&this.__text_cache__===e&&(n.push(f(this,t)),t=this.__last_index__);for(var r=t?e.slice(t):e;this.test(r);)n.push(f(this,t)),r=r.slice(this.__last_index__),t+=this.__last_index__;return n.length?n:null},T.prototype.tlds=function(e,t){return e=Array.isArray(e)?e:[e],t?(this.__tlds__=this.__tlds__.concat(e).sort().filter((function(e,t,n){return e!==n[t-1]})).reverse(),p(this),this):(this.__tlds__=e.slice(),this.__tlds_replaced__=!0,p(this),this)},T.prototype.normalize=function(e){e.schema||(e.url="http://"+e.url),"mailto:"!==e.schema||/^mailto:/i.test(e.url)||(e.url="mailto:"+e.url)},T.prototype.onCompile=function(){},e.exports=T},2879:(e,t,n)=>{"use strict";e.exports=function(e){var t={};t.src_Any=n(6027).source,t.src_Cc=n(592).source,t.src_Z=n(3978).source,t.src_P=n(2828).source,t.src_ZPCc=[t.src_Z,t.src_P,t.src_Cc].join("|"),t.src_ZCc=[t.src_Z,t.src_Cc].join("|");return t.src_pseudo_letter="(?:(?![><｜]|"+t.src_ZPCc+")"+t.src_Any+")",t.src_ip4="(?:(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)",t.src_auth="(?:(?:(?!"+t.src_ZCc+"|[@/\\[\\]()]).)+@)?",t.src_port="(?::(?:6(?:[0-4]\\d{3}|5(?:[0-4]\\d{2}|5(?:[0-2]\\d|3[0-5])))|[1-5]?\\d{1,4}))?",t.src_host_terminator="(?=$|[><｜]|"+t.src_ZPCc+")(?!-|_|:\\d|\\.-|\\.(?!$|"+t.src_ZPCc+"))",t.src_path="(?:[/?#](?:(?!"+t.src_ZCc+"|[><｜]|[()[\\]{}.,\"'?!\\-;]).|\\[(?:(?!"+t.src_ZCc+"|\\]).)*\\]|\\((?:(?!"+t.src_ZCc+"|[)]).)*\\)|\\{(?:(?!"+t.src_ZCc+'|[}]).)*\\}|\\"(?:(?!'+t.src_ZCc+'|["]).)+\\"|\\\'(?:(?!'+t.src_ZCc+"|[']).)+\\'|\\'(?="+t.src_pseudo_letter+"|[-]).|\\.{2,}[a-zA-Z0-9%/&]|\\.(?!"+t.src_ZCc+"|[.]).|"+(e&&e["---"]?"\\-(?!--(?:[^-]|$))(?:-*)|":"\\-+|")+",(?!"+t.src_ZCc+").|;(?!"+t.src_ZCc+").|\\!+(?!"+t.src_ZCc+"|[!]).|\\?(?!"+t.src_ZCc+"|[?]).)+|\\/)?",t.src_email_name='[\\-;:&=\\+\\$,\\.a-zA-Z0-9_][\\-;:&=\\+\\$,\\"\\.a-zA-Z0-9_]*',t.src_xn="xn--[a-z0-9\\-]{1,59}",t.src_domain_root="(?:"+t.src_xn+"|"+t.src_pseudo_letter+"{1,63})",t.src_domain="(?:"+t.src_xn+"|(?:"+t.src_pseudo_letter+")|(?:"+t.src_pseudo_letter+"(?:-|"+t.src_pseudo_letter+"){0,61}"+t.src_pseudo_letter+"))",t.src_host="(?:(?:(?:(?:"+t.src_domain+")\\.)*"+t.src_domain+"))",t.tpl_host_fuzzy="(?:"+t.src_ip4+"|(?:(?:(?:"+t.src_domain+")\\.)+(?:%TLDS%)))",t.tpl_host_no_ip_fuzzy="(?:(?:(?:"+t.src_domain+")\\.)+(?:%TLDS%))",t.src_host_strict=t.src_host+t.src_host_terminator,t.tpl_host_fuzzy_strict=t.tpl_host_fuzzy+t.src_host_terminator,t.src_host_port_strict=t.src_host+t.src_port+t.src_host_terminator,t.tpl_host_port_fuzzy_strict=t.tpl_host_fuzzy+t.src_port+t.src_host_terminator,t.tpl_host_port_no_ip_fuzzy_strict=t.tpl_host_no_ip_fuzzy+t.src_port+t.src_host_terminator,t.tpl_host_fuzzy_test="localhost|www\\.|\\.\\d{1,3}\\.|(?:\\.(?:%TLDS%)(?:"+t.src_ZPCc+"|>|$))",t.tpl_email_fuzzy='(^|[><｜]|"|\\(|'+t.src_ZCc+")("+t.src_email_name+"@"+t.tpl_host_fuzzy_strict+")",t.tpl_link_fuzzy="(^|(?![.:/\\-_@])(?:[$+<=>^`|｜]|"+t.src_ZPCc+"))((?![$+<=>^`|｜])"+t.tpl_host_port_fuzzy_strict+t.src_path+")",t.tpl_link_no_ip_fuzzy="(^|(?![.:/\\-_@])(?:[$+<=>^`|｜]|"+t.src_ZPCc+"))((?![$+<=>^`|｜])"+t.tpl_host_port_no_ip_fuzzy_strict+t.src_path+")",t}},2922:(e,t,n)=>{"use strict";e.exports=n(1246)},8359:(e,t,n)=>{"use strict";e.exports=n(4374)},1358:e=>{"use strict";e.exports=["address","article","aside","base","basefont","blockquote","body","caption","center","col","colgroup","dd","details","dialog","dir","div","dl","dt","fieldset","figcaption","figure","footer","form","frame","frameset","h1","h2","h3","h4","h5","h6","head","header","hr","html","iframe","legend","li","link","main","menu","menuitem","nav","noframes","ol","optgroup","option","p","param","section","source","summary","table","tbody","td","tfoot","th","thead","title","tr","track","ul"]},6557:e=>{"use strict";var t="<[A-Za-z][A-Za-z0-9\\-]*(?:\\s+[a-zA-Z_:][a-zA-Z0-9:._-]*(?:\\s*=\\s*(?:[^\"'=<>`\\x00-\\x20]+|'[^']*'|\"[^\"]*\"))?)*\\s*\\/?>",n="<\\/[A-Za-z][A-Za-z0-9\\-]*\\s*>",r=new RegExp("^(?:"+t+"|"+n+"|\x3c!----\x3e|\x3c!--(?:-?[^>-])(?:-?[^-])*--\x3e|<[?][\\s\\S]*?[?]>|<![A-Z]+\\s+[^>]*>|<!\\[CDATA\\[[\\s\\S]*?\\]\\]>)"),s=new RegExp("^(?:"+t+"|"+n+")");e.exports.l=r,e.exports.p=s},9963:(e,t,n)=>{"use strict";var r=Object.prototype.hasOwnProperty;function s(e,t){return r.call(e,t)}function i(e){return!(e>=55296&&e<=57343||e>=64976&&e<=65007||!(65535&~e&&65534!=(65535&e))||e>=0&&e<=8||11===e||e>=14&&e<=31||e>=127&&e<=159||e>1114111)}function o(e){if(e>65535){var t=55296+((e-=65536)>>10),n=56320+(1023&e);return String.fromCharCode(t,n)}return String.fromCharCode(e)}var a=/\\([!"#$%&'()*+,\-.\/:;<=>?@[\\\]^_`{|}~])/g,c=new RegExp(a.source+"|"+/&([a-z#][a-z0-9]{1,31});/gi.source,"gi"),l=/^#((?:x[a-f0-9]{1,8}|[0-9]{1,8}))/i,u=n(8359),p=/[&<>"]/,h=/[&<>"]/g,f={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;"};function T(e){return f[e]}var d=/[.?*+^$[\]\\(){}|-]/g,m=n(2828);t.lib={},t.lib.mdurl=n(6781),t.lib.ucmicro=n(9295),t.assign=function(e){return Array.prototype.slice.call(arguments,1).forEach((function(t){if(t){if("object"!=typeof t)throw new TypeError(t+"must be object");Object.keys(t).forEach((function(n){e[n]=t[n]}))}})),e},t.isString=function(e){return"[object String]"===function(e){return Object.prototype.toString.call(e)}(e)},t.has=s,t.unescapeMd=function(e){return e.indexOf("\\")<0?e:e.replace(a,"$1")},t.unescapeAll=function(e){return e.indexOf("\\")<0&&e.indexOf("&")<0?e:e.replace(c,(function(e,t,n){return t||function(e,t){var n=0;return s(u,t)?u[t]:35===t.charCodeAt(0)&&l.test(t)&&i(n="x"===t[1].toLowerCase()?parseInt(t.slice(2),16):parseInt(t.slice(1),10))?o(n):e}(e,n)}))},t.isValidEntityCode=i,t.fromCodePoint=o,t.escapeHtml=function(e){return p.test(e)?e.replace(h,T):e},t.arrayReplaceAt=function(e,t,n){return[].concat(e.slice(0,t),n,e.slice(t+1))},t.isSpace=function(e){switch(e){case 9:case 32:return!0}return!1},t.isWhiteSpace=function(e){if(e>=8192&&e<=8202)return!0;switch(e){case 9:case 10:case 11:case 12:case 13:case 32:case 160:case 5760:case 8239:case 8287:case 12288:return!0}return!1},t.isMdAsciiPunct=function(e){switch(e){case 33:case 34:case 35:case 36:case 37:case 38:case 39:case 40:case 41:case 42:case 43:case 44:case 45:case 46:case 47:case 58:case 59:case 60:case 61:case 62:case 63:case 64:case 91:case 92:case 93:case 94:case 95:case 96:case 123:case 124:case 125:case 126:return!0;default:return!1}},t.isPunctChar=function(e){return m.test(e)},t.escapeRE=function(e){return e.replace(d,"\\$&")},t.normalizeReference=function(e){return e=e.trim().replace(/\s+/g," "),"Ṿ"==="ẞ".toLowerCase()&&(e=e.replace(/ẞ/g,"ß")),e.toLowerCase().toUpperCase()}},3592:(e,t,n)=>{"use strict";t.parseLinkLabel=n(1947),t.parseLinkDestination=n(8949),t.parseLinkTitle=n(7311)},8949:(e,t,n)=>{"use strict";var r=n(9963).unescapeAll;e.exports=function(e,t,n){var s,i,o=t,a={ok:!1,pos:0,lines:0,str:""};if(60===e.charCodeAt(t)){for(t++;t<n;){if(10===(s=e.charCodeAt(t)))return a;if(60===s)return a;if(62===s)return a.pos=t+1,a.str=r(e.slice(o+1,t)),a.ok=!0,a;92===s&&t+1<n?t+=2:t++}return a}for(i=0;t<n&&32!==(s=e.charCodeAt(t))&&!(s<32||127===s);)if(92===s&&t+1<n){if(32===e.charCodeAt(t+1))break;t+=2}else{if(40===s&&++i>32)return a;if(41===s){if(0===i)break;i--}t++}return o===t||0!==i||(a.str=r(e.slice(o,t)),a.lines=0,a.pos=t,a.ok=!0),a}},1947:e=>{"use strict";e.exports=function(e,t,n){var r,s,i,o,a=-1,c=e.posMax,l=e.pos;for(e.pos=t+1,r=1;e.pos<c;){if(93===(i=e.src.charCodeAt(e.pos))&&0==--r){s=!0;break}if(o=e.pos,e.md.inline.skipToken(e),91===i)if(o===e.pos-1)r++;else if(n)return e.pos=l,-1}return s&&(a=e.pos),e.pos=l,a}},7311:(e,t,n)=>{"use strict";var r=n(9963).unescapeAll;e.exports=function(e,t,n){var s,i,o=0,a=t,c={ok:!1,pos:0,lines:0,str:""};if(t>=n)return c;if(34!==(i=e.charCodeAt(t))&&39!==i&&40!==i)return c;for(t++,40===i&&(i=41);t<n;){if((s=e.charCodeAt(t))===i)return c.pos=t+1,c.lines=o,c.str=r(e.slice(a+1,t)),c.ok=!0,c;if(40===s&&41===i)return c;10===s?o++:92===s&&t+1<n&&(t++,10===e.charCodeAt(t)&&o++),t++}return c}},1246:(e,t,n)=>{"use strict";var r=n(9963),s=n(3592),i=n(4847),o=n(6321),a=n(1525),c=n(3171),l=n(2833),u=n(6781),p=n(4876),h={default:n(5092),zero:n(4719),commonmark:n(73)},f=/^(vbscript|javascript|file|data):/,T=/^data:image\/(gif|png|jpeg|webp);/;function d(e){var t=e.trim().toLowerCase();return!f.test(t)||!!T.test(t)}var m=["http:","https:","mailto:"];function E(e){var t=u.parse(e,!0);if(t.hostname&&(!t.protocol||m.indexOf(t.protocol)>=0))try{t.hostname=p.toASCII(t.hostname)}catch(e){}return u.encode(u.format(t))}function _(e){var t=u.parse(e,!0);if(t.hostname&&(!t.protocol||m.indexOf(t.protocol)>=0))try{t.hostname=p.toUnicode(t.hostname)}catch(e){}return u.decode(u.format(t),u.decode.defaultChars+"%")}function g(e,t){if(!(this instanceof g))return new g(e,t);t||r.isString(e)||(t=e||{},e="default"),this.inline=new c,this.block=new a,this.core=new o,this.renderer=new i,this.linkify=new l,this.validateLink=d,this.normalizeLink=E,this.normalizeLinkText=_,this.utils=r,this.helpers=r.assign({},s),this.options={},this.configure(e),t&&this.set(t)}g.prototype.set=function(e){return r.assign(this.options,e),this},g.prototype.configure=function(e){var t,n=this;if(r.isString(e)&&!(e=h[t=e]))throw new Error('Wrong `markdown-it` preset "'+t+'", check name');if(!e)throw new Error("Wrong `markdown-it` preset, can't be empty");return e.options&&n.set(e.options),e.components&&Object.keys(e.components).forEach((function(t){e.components[t].rules&&n[t].ruler.enableOnly(e.components[t].rules),e.components[t].rules2&&n[t].ruler2.enableOnly(e.components[t].rules2)})),this},g.prototype.enable=function(e,t){var n=[];Array.isArray(e)||(e=[e]),["core","block","inline"].forEach((function(t){n=n.concat(this[t].ruler.enable(e,!0))}),this),n=n.concat(this.inline.ruler2.enable(e,!0));var r=e.filter((function(e){return n.indexOf(e)<0}));if(r.length&&!t)throw new Error("MarkdownIt. Failed to enable unknown rule(s): "+r);return this},g.prototype.disable=function(e,t){var n=[];Array.isArray(e)||(e=[e]),["core","block","inline"].forEach((function(t){n=n.concat(this[t].ruler.disable(e,!0))}),this),n=n.concat(this.inline.ruler2.disable(e,!0));var r=e.filter((function(e){return n.indexOf(e)<0}));if(r.length&&!t)throw new Error("MarkdownIt. Failed to disable unknown rule(s): "+r);return this},g.prototype.use=function(e){var t=[this].concat(Array.prototype.slice.call(arguments,1));return e.apply(e,t),this},g.prototype.parse=function(e,t){if("string"!=typeof e)throw new Error("Input data should be a String");var n=new this.core.State(e,this,t);return this.core.process(n),n.tokens},g.prototype.render=function(e,t){return t=t||{},this.renderer.render(this.parse(e,t),this.options,t)},g.prototype.parseInline=function(e,t){var n=new this.core.State(e,this,t);return n.inlineMode=!0,this.core.process(n),n.tokens},g.prototype.renderInline=function(e,t){return t=t||{},this.renderer.render(this.parseInline(e,t),this.options,t)},e.exports=g},1525:(e,t,n)=>{"use strict";var r=n(2378),s=[["table",n(4752),["paragraph","reference"]],["code",n(5711)],["fence",n(2373),["paragraph","reference","blockquote","list"]],["blockquote",n(2941),["paragraph","reference","blockquote","list"]],["hr",n(8e3),["paragraph","reference","blockquote","list"]],["list",n(6686),["paragraph","reference","blockquote"]],["reference",n(6897)],["html_block",n(1857),["paragraph","reference","blockquote"]],["heading",n(634),["paragraph","reference","blockquote"]],["lheading",n(9648)],["paragraph",n(7046)]];function i(){this.ruler=new r;for(var e=0;e<s.length;e++)this.ruler.push(s[e][0],s[e][1],{alt:(s[e][2]||[]).slice()})}i.prototype.tokenize=function(e,t,n){for(var r,s=this.ruler.getRules(""),i=s.length,o=t,a=!1,c=e.md.options.maxNesting;o<n&&(e.line=o=e.skipEmptyLines(o),!(o>=n))&&!(e.sCount[o]<e.blkIndent);){if(e.level>=c){e.line=n;break}for(r=0;r<i&&!s[r](e,o,n,!1);r++);e.tight=!a,e.isEmpty(e.line-1)&&(a=!0),(o=e.line)<n&&e.isEmpty(o)&&(a=!0,o++,e.line=o)}},i.prototype.parse=function(e,t,n,r){var s;e&&(s=new this.State(e,t,n,r),this.tokenize(s,s.line,s.lineMax))},i.prototype.State=n(7759),e.exports=i},6321:(e,t,n)=>{"use strict";var r=n(2378),s=[["normalize",n(803)],["block",n(3437)],["inline",n(3547)],["linkify",n(986)],["replacements",n(203)],["smartquotes",n(5260)]];function i(){this.ruler=new r;for(var e=0;e<s.length;e++)this.ruler.push(s[e][0],s[e][1])}i.prototype.process=function(e){var t,n,r;for(t=0,n=(r=this.ruler.getRules("")).length;t<n;t++)r[t](e)},i.prototype.State=n(1839),e.exports=i},3171:(e,t,n)=>{"use strict";var r=n(2378),s=[["text",n(2015)],["newline",n(2534)],["escape",n(1231)],["backticks",n(6757)],["strikethrough",n(7141).q],["emphasis",n(3898).q],["link",n(6552)],["image",n(3707)],["autolink",n(6955)],["html_inline",n(961)],["entity",n(8103)]],i=[["balance_pairs",n(5940)],["strikethrough",n(7141).g],["emphasis",n(3898).g],["text_collapse",n(7729)]];function o(){var e;for(this.ruler=new r,e=0;e<s.length;e++)this.ruler.push(s[e][0],s[e][1]);for(this.ruler2=new r,e=0;e<i.length;e++)this.ruler2.push(i[e][0],i[e][1])}o.prototype.skipToken=function(e){var t,n,r=e.pos,s=this.ruler.getRules(""),i=s.length,o=e.md.options.maxNesting,a=e.cache;if(void 0===a[r]){if(e.level<o)for(n=0;n<i&&(e.level++,t=s[n](e,!0),e.level--,!t);n++);else e.pos=e.posMax;t||e.pos++,a[r]=e.pos}else e.pos=a[r]},o.prototype.tokenize=function(e){for(var t,n,r=this.ruler.getRules(""),s=r.length,i=e.posMax,o=e.md.options.maxNesting;e.pos<i;){if(e.level<o)for(n=0;n<s&&!(t=r[n](e,!1));n++);if(t){if(e.pos>=i)break}else e.pending+=e.src[e.pos++]}e.pending&&e.pushPending()},o.prototype.parse=function(e,t,n,r){var s,i,o,a=new this.State(e,t,n,r);for(this.tokenize(a),o=(i=this.ruler2.getRules("")).length,s=0;s<o;s++)i[s](a)},o.prototype.State=n(979),e.exports=o},73:e=>{"use strict";e.exports={options:{html:!0,xhtmlOut:!0,breaks:!1,langPrefix:"language-",linkify:!1,typographer:!1,quotes:"“”‘’",highlight:null,maxNesting:20},components:{core:{rules:["normalize","block","inline"]},block:{rules:["blockquote","code","fence","heading","hr","html_block","lheading","list","reference","paragraph"]},inline:{rules:["autolink","backticks","emphasis","entity","escape","html_inline","image","link","newline","text"],rules2:["balance_pairs","emphasis","text_collapse"]}}}},5092:e=>{"use strict";e.exports={options:{html:!1,xhtmlOut:!1,breaks:!1,langPrefix:"language-",linkify:!1,typographer:!1,quotes:"“”‘’",highlight:null,maxNesting:100},components:{core:{},block:{},inline:{}}}},4719:e=>{"use strict";e.exports={options:{html:!1,xhtmlOut:!1,breaks:!1,langPrefix:"language-",linkify:!1,typographer:!1,quotes:"“”‘’",highlight:null,maxNesting:20},components:{core:{rules:["normalize","block","inline"]},block:{rules:["paragraph"]},inline:{rules:["text"],rules2:["balance_pairs","text_collapse"]}}}},4847:(e,t,n)=>{"use strict";var r=n(9963).assign,s=n(9963).unescapeAll,i=n(9963).escapeHtml,o={};function a(){this.rules=r({},o)}o.code_inline=function(e,t,n,r,s){var o=e[t];return"<code"+s.renderAttrs(o)+">"+i(e[t].content)+"</code>"},o.code_block=function(e,t,n,r,s){var o=e[t];return"<pre"+s.renderAttrs(o)+"><code>"+i(e[t].content)+"</code></pre>\n"},o.fence=function(e,t,n,r,o){var a,c,l,u,p,h=e[t],f=h.info?s(h.info).trim():"",T="",d="";return f&&(T=(l=f.split(/(\s+)/g))[0],d=l.slice(2).join("")),0===(a=n.highlight&&n.highlight(h.content,T,d)||i(h.content)).indexOf("<pre")?a+"\n":f?(c=h.attrIndex("class"),u=h.attrs?h.attrs.slice():[],c<0?u.push(["class",n.langPrefix+T]):(u[c]=u[c].slice(),u[c][1]+=" "+n.langPrefix+T),p={attrs:u},"<pre><code"+o.renderAttrs(p)+">"+a+"</code></pre>\n"):"<pre><code"+o.renderAttrs(h)+">"+a+"</code></pre>\n"},o.image=function(e,t,n,r,s){var i=e[t];return i.attrs[i.attrIndex("alt")][1]=s.renderInlineAsText(i.children,n,r),s.renderToken(e,t,n)},o.hardbreak=function(e,t,n){return n.xhtmlOut?"<br />\n":"<br>\n"},o.softbreak=function(e,t,n){return n.breaks?n.xhtmlOut?"<br />\n":"<br>\n":"\n"},o.text=function(e,t){return i(e[t].content)},o.html_block=function(e,t){return e[t].content},o.html_inline=function(e,t){return e[t].content},a.prototype.renderAttrs=function(e){var t,n,r;if(!e.attrs)return"";for(r="",t=0,n=e.attrs.length;t<n;t++)r+=" "+i(e.attrs[t][0])+'="'+i(e.attrs[t][1])+'"';return r},a.prototype.renderToken=function(e,t,n){var r,s="",i=!1,o=e[t];return o.hidden?"":(o.block&&-1!==o.nesting&&t&&e[t-1].hidden&&(s+="\n"),s+=(-1===o.nesting?"</":"<")+o.tag,s+=this.renderAttrs(o),0===o.nesting&&n.xhtmlOut&&(s+=" /"),o.block&&(i=!0,1===o.nesting&&t+1<e.length&&("inline"===(r=e[t+1]).type||r.hidden||-1===r.nesting&&r.tag===o.tag)&&(i=!1)),s+=i?">\n":">")},a.prototype.renderInline=function(e,t,n){for(var r,s="",i=this.rules,o=0,a=e.length;o<a;o++)void 0!==i[r=e[o].type]?s+=i[r](e,o,t,n,this):s+=this.renderToken(e,o,t);return s},a.prototype.renderInlineAsText=function(e,t,n){for(var r="",s=0,i=e.length;s<i;s++)"text"===e[s].type?r+=e[s].content:"image"===e[s].type?r+=this.renderInlineAsText(e[s].children,t,n):"softbreak"===e[s].type&&(r+="\n");return r},a.prototype.render=function(e,t,n){var r,s,i,o="",a=this.rules;for(r=0,s=e.length;r<s;r++)"inline"===(i=e[r].type)?o+=this.renderInline(e[r].children,t,n):void 0!==a[i]?o+=a[e[r].type](e,r,t,n,this):o+=this.renderToken(e,r,t,n);return o},e.exports=a},2378:e=>{"use strict";function t(){this.__rules__=[],this.__cache__=null}t.prototype.__find__=function(e){for(var t=0;t<this.__rules__.length;t++)if(this.__rules__[t].name===e)return t;return-1},t.prototype.__compile__=function(){var e=this,t=[""];e.__rules__.forEach((function(e){e.enabled&&e.alt.forEach((function(e){t.indexOf(e)<0&&t.push(e)}))})),e.__cache__={},t.forEach((function(t){e.__cache__[t]=[],e.__rules__.forEach((function(n){n.enabled&&(t&&n.alt.indexOf(t)<0||e.__cache__[t].push(n.fn))}))}))},t.prototype.at=function(e,t,n){var r=this.__find__(e),s=n||{};if(-1===r)throw new Error("Parser rule not found: "+e);this.__rules__[r].fn=t,this.__rules__[r].alt=s.alt||[],this.__cache__=null},t.prototype.before=function(e,t,n,r){var s=this.__find__(e),i=r||{};if(-1===s)throw new Error("Parser rule not found: "+e);this.__rules__.splice(s,0,{name:t,enabled:!0,fn:n,alt:i.alt||[]}),this.__cache__=null},t.prototype.after=function(e,t,n,r){var s=this.__find__(e),i=r||{};if(-1===s)throw new Error("Parser rule not found: "+e);this.__rules__.splice(s+1,0,{name:t,enabled:!0,fn:n,alt:i.alt||[]}),this.__cache__=null},t.prototype.push=function(e,t,n){var r=n||{};this.__rules__.push({name:e,enabled:!0,fn:t,alt:r.alt||[]}),this.__cache__=null},t.prototype.enable=function(e,t){Array.isArray(e)||(e=[e]);var n=[];return e.forEach((function(e){var r=this.__find__(e);if(r<0){if(t)return;throw new Error("Rules manager: invalid rule name "+e)}this.__rules__[r].enabled=!0,n.push(e)}),this),this.__cache__=null,n},t.prototype.enableOnly=function(e,t){Array.isArray(e)||(e=[e]),this.__rules__.forEach((function(e){e.enabled=!1})),this.enable(e,t)},t.prototype.disable=function(e,t){Array.isArray(e)||(e=[e]);var n=[];return e.forEach((function(e){var r=this.__find__(e);if(r<0){if(t)return;throw new Error("Rules manager: invalid rule name "+e)}this.__rules__[r].enabled=!1,n.push(e)}),this),this.__cache__=null,n},t.prototype.getRules=function(e){return null===this.__cache__&&this.__compile__(),this.__cache__[e]||[]},e.exports=t},2941:(e,t,n)=>{"use strict";var r=n(9963).isSpace;e.exports=function(e,t,n,s){var i,o,a,c,l,u,p,h,f,T,d,m,E,_,g,A,C,N,k,O,S=e.lineMax,L=e.bMarks[t]+e.tShift[t],v=e.eMarks[t];if(e.sCount[t]-e.blkIndent>=4)return!1;if(62!==e.src.charCodeAt(L++))return!1;if(s)return!0;for(c=f=e.sCount[t]+1,32===e.src.charCodeAt(L)?(L++,c++,f++,i=!1,A=!0):9===e.src.charCodeAt(L)?(A=!0,(e.bsCount[t]+f)%4==3?(L++,c++,f++,i=!1):i=!0):A=!1,T=[e.bMarks[t]],e.bMarks[t]=L;L<v&&(o=e.src.charCodeAt(L),r(o));)9===o?f+=4-(f+e.bsCount[t]+(i?1:0))%4:f++,L++;for(d=[e.bsCount[t]],e.bsCount[t]=e.sCount[t]+1+(A?1:0),u=L>=v,_=[e.sCount[t]],e.sCount[t]=f-c,g=[e.tShift[t]],e.tShift[t]=L-e.bMarks[t],N=e.md.block.ruler.getRules("blockquote"),E=e.parentType,e.parentType="blockquote",h=t+1;h<n&&(O=e.sCount[h]<e.blkIndent,!((L=e.bMarks[h]+e.tShift[h])>=(v=e.eMarks[h])));h++)if(62!==e.src.charCodeAt(L++)||O){if(u)break;for(C=!1,a=0,l=N.length;a<l;a++)if(N[a](e,h,n,!0)){C=!0;break}if(C){e.lineMax=h,0!==e.blkIndent&&(T.push(e.bMarks[h]),d.push(e.bsCount[h]),g.push(e.tShift[h]),_.push(e.sCount[h]),e.sCount[h]-=e.blkIndent);break}T.push(e.bMarks[h]),d.push(e.bsCount[h]),g.push(e.tShift[h]),_.push(e.sCount[h]),e.sCount[h]=-1}else{for(c=f=e.sCount[h]+1,32===e.src.charCodeAt(L)?(L++,c++,f++,i=!1,A=!0):9===e.src.charCodeAt(L)?(A=!0,(e.bsCount[h]+f)%4==3?(L++,c++,f++,i=!1):i=!0):A=!1,T.push(e.bMarks[h]),e.bMarks[h]=L;L<v&&(o=e.src.charCodeAt(L),r(o));)9===o?f+=4-(f+e.bsCount[h]+(i?1:0))%4:f++,L++;u=L>=v,d.push(e.bsCount[h]),e.bsCount[h]=e.sCount[h]+1+(A?1:0),_.push(e.sCount[h]),e.sCount[h]=f-c,g.push(e.tShift[h]),e.tShift[h]=L-e.bMarks[h]}for(m=e.blkIndent,e.blkIndent=0,(k=e.push("blockquote_open","blockquote",1)).markup=">",k.map=p=[t,0],e.md.block.tokenize(e,t,h),(k=e.push("blockquote_close","blockquote",-1)).markup=">",e.lineMax=S,e.parentType=E,p[1]=e.line,a=0;a<g.length;a++)e.bMarks[a+t]=T[a],e.tShift[a+t]=g[a],e.sCount[a+t]=_[a],e.bsCount[a+t]=d[a];return e.blkIndent=m,!0}},5711:e=>{"use strict";e.exports=function(e,t,n){var r,s,i;if(e.sCount[t]-e.blkIndent<4)return!1;for(s=r=t+1;r<n;)if(e.isEmpty(r))r++;else{if(!(e.sCount[r]-e.blkIndent>=4))break;s=++r}return e.line=s,(i=e.push("code_block","code",0)).content=e.getLines(t,s,4+e.blkIndent,!1)+"\n",i.map=[t,e.line],!0}},2373:e=>{"use strict";e.exports=function(e,t,n,r){var s,i,o,a,c,l,u,p=!1,h=e.bMarks[t]+e.tShift[t],f=e.eMarks[t];if(e.sCount[t]-e.blkIndent>=4)return!1;if(h+3>f)return!1;if(126!==(s=e.src.charCodeAt(h))&&96!==s)return!1;if(c=h,(i=(h=e.skipChars(h,s))-c)<3)return!1;if(u=e.src.slice(c,h),o=e.src.slice(h,f),96===s&&o.indexOf(String.fromCharCode(s))>=0)return!1;if(r)return!0;for(a=t;!(++a>=n||(h=c=e.bMarks[a]+e.tShift[a])<(f=e.eMarks[a])&&e.sCount[a]<e.blkIndent);)if(e.src.charCodeAt(h)===s&&!(e.sCount[a]-e.blkIndent>=4||(h=e.skipChars(h,s))-c<i||(h=e.skipSpaces(h))<f)){p=!0;break}return i=e.sCount[t],e.line=a+(p?1:0),(l=e.push("fence","code",0)).info=o,l.content=e.getLines(t+1,a,i,!0),l.markup=u,l.map=[t,e.line],!0}},634:(e,t,n)=>{"use strict";var r=n(9963).isSpace;e.exports=function(e,t,n,s){var i,o,a,c,l=e.bMarks[t]+e.tShift[t],u=e.eMarks[t];if(e.sCount[t]-e.blkIndent>=4)return!1;if(35!==(i=e.src.charCodeAt(l))||l>=u)return!1;for(o=1,i=e.src.charCodeAt(++l);35===i&&l<u&&o<=6;)o++,i=e.src.charCodeAt(++l);return!(o>6||l<u&&!r(i)||(s||(u=e.skipSpacesBack(u,l),(a=e.skipCharsBack(u,35,l))>l&&r(e.src.charCodeAt(a-1))&&(u=a),e.line=t+1,(c=e.push("heading_open","h"+String(o),1)).markup="########".slice(0,o),c.map=[t,e.line],(c=e.push("inline","",0)).content=e.src.slice(l,u).trim(),c.map=[t,e.line],c.children=[],(c=e.push("heading_close","h"+String(o),-1)).markup="########".slice(0,o)),0))}},8e3:(e,t,n)=>{"use strict";var r=n(9963).isSpace;e.exports=function(e,t,n,s){var i,o,a,c,l=e.bMarks[t]+e.tShift[t],u=e.eMarks[t];if(e.sCount[t]-e.blkIndent>=4)return!1;if(42!==(i=e.src.charCodeAt(l++))&&45!==i&&95!==i)return!1;for(o=1;l<u;){if((a=e.src.charCodeAt(l++))!==i&&!r(a))return!1;a===i&&o++}return!(o<3||(s||(e.line=t+1,(c=e.push("hr","hr",0)).map=[t,e.line],c.markup=Array(o+1).join(String.fromCharCode(i))),0))}},1857:(e,t,n)=>{"use strict";var r=n(1358),s=n(6557).p,i=[[/^<(script|pre|style|textarea)(?=(\s|>|$))/i,/<\/(script|pre|style|textarea)>/i,!0],[/^<!--/,/-->/,!0],[/^<\?/,/\?>/,!0],[/^<![A-Z]/,/>/,!0],[/^<!\[CDATA\[/,/\]\]>/,!0],[new RegExp("^</?("+r.join("|")+")(?=(\\s|/?>|$))","i"),/^$/,!0],[new RegExp(s.source+"\\s*$"),/^$/,!1]];e.exports=function(e,t,n,r){var s,o,a,c,l=e.bMarks[t]+e.tShift[t],u=e.eMarks[t];if(e.sCount[t]-e.blkIndent>=4)return!1;if(!e.md.options.html)return!1;if(60!==e.src.charCodeAt(l))return!1;for(c=e.src.slice(l,u),s=0;s<i.length&&!i[s][0].test(c);s++);if(s===i.length)return!1;if(r)return i[s][2];if(o=t+1,!i[s][1].test(c))for(;o<n&&!(e.sCount[o]<e.blkIndent);o++)if(l=e.bMarks[o]+e.tShift[o],u=e.eMarks[o],c=e.src.slice(l,u),i[s][1].test(c)){0!==c.length&&o++;break}return e.line=o,(a=e.push("html_block","",0)).map=[t,o],a.content=e.getLines(t,o,e.blkIndent,!0),!0}},9648:e=>{"use strict";e.exports=function(e,t,n){var r,s,i,o,a,c,l,u,p,h,f=t+1,T=e.md.block.ruler.getRules("paragraph");if(e.sCount[t]-e.blkIndent>=4)return!1;for(h=e.parentType,e.parentType="paragraph";f<n&&!e.isEmpty(f);f++)if(!(e.sCount[f]-e.blkIndent>3)){if(e.sCount[f]>=e.blkIndent&&(c=e.bMarks[f]+e.tShift[f])<(l=e.eMarks[f])&&(45===(p=e.src.charCodeAt(c))||61===p)&&(c=e.skipChars(c,p),(c=e.skipSpaces(c))>=l)){u=61===p?1:2;break}if(!(e.sCount[f]<0)){for(s=!1,i=0,o=T.length;i<o;i++)if(T[i](e,f,n,!0)){s=!0;break}if(s)break}}return!!u&&(r=e.getLines(t,f,e.blkIndent,!1).trim(),e.line=f+1,(a=e.push("heading_open","h"+String(u),1)).markup=String.fromCharCode(p),a.map=[t,e.line],(a=e.push("inline","",0)).content=r,a.map=[t,e.line-1],a.children=[],(a=e.push("heading_close","h"+String(u),-1)).markup=String.fromCharCode(p),e.parentType=h,!0)}},6686:(e,t,n)=>{"use strict";var r=n(9963).isSpace;function s(e,t){var n,s,i,o;return s=e.bMarks[t]+e.tShift[t],i=e.eMarks[t],42!==(n=e.src.charCodeAt(s++))&&45!==n&&43!==n||s<i&&(o=e.src.charCodeAt(s),!r(o))?-1:s}function i(e,t){var n,s=e.bMarks[t]+e.tShift[t],i=s,o=e.eMarks[t];if(i+1>=o)return-1;if((n=e.src.charCodeAt(i++))<48||n>57)return-1;for(;;){if(i>=o)return-1;if(!((n=e.src.charCodeAt(i++))>=48&&n<=57)){if(41===n||46===n)break;return-1}if(i-s>=10)return-1}return i<o&&(n=e.src.charCodeAt(i),!r(n))?-1:i}e.exports=function(e,t,n,r){var o,a,c,l,u,p,h,f,T,d,m,E,_,g,A,C,N,k,O,S,L,v,b,R,I,M,y,D,P=!1,x=!0;if(e.sCount[t]-e.blkIndent>=4)return!1;if(e.listIndent>=0&&e.sCount[t]-e.listIndent>=4&&e.sCount[t]<e.blkIndent)return!1;if(r&&"paragraph"===e.parentType&&e.sCount[t]>=e.blkIndent&&(P=!0),(b=i(e,t))>=0){if(h=!0,I=e.bMarks[t]+e.tShift[t],_=Number(e.src.slice(I,b-1)),P&&1!==_)return!1}else{if(!((b=s(e,t))>=0))return!1;h=!1}if(P&&e.skipSpaces(b)>=e.eMarks[t])return!1;if(E=e.src.charCodeAt(b-1),r)return!0;for(m=e.tokens.length,h?(D=e.push("ordered_list_open","ol",1),1!==_&&(D.attrs=[["start",_]])):D=e.push("bullet_list_open","ul",1),D.map=d=[t,0],D.markup=String.fromCharCode(E),A=t,R=!1,y=e.md.block.ruler.getRules("list"),k=e.parentType,e.parentType="list";A<n;){for(v=b,g=e.eMarks[A],p=C=e.sCount[A]+b-(e.bMarks[t]+e.tShift[t]);v<g;){if(9===(o=e.src.charCodeAt(v)))C+=4-(C+e.bsCount[A])%4;else{if(32!==o)break;C++}v++}if((u=(a=v)>=g?1:C-p)>4&&(u=1),l=p+u,(D=e.push("list_item_open","li",1)).markup=String.fromCharCode(E),D.map=f=[t,0],h&&(D.info=e.src.slice(I,b-1)),L=e.tight,S=e.tShift[t],O=e.sCount[t],N=e.listIndent,e.listIndent=e.blkIndent,e.blkIndent=l,e.tight=!0,e.tShift[t]=a-e.bMarks[t],e.sCount[t]=C,a>=g&&e.isEmpty(t+1)?e.line=Math.min(e.line+2,n):e.md.block.tokenize(e,t,n,!0),e.tight&&!R||(x=!1),R=e.line-t>1&&e.isEmpty(e.line-1),e.blkIndent=e.listIndent,e.listIndent=N,e.tShift[t]=S,e.sCount[t]=O,e.tight=L,(D=e.push("list_item_close","li",-1)).markup=String.fromCharCode(E),A=t=e.line,f[1]=A,a=e.bMarks[t],A>=n)break;if(e.sCount[A]<e.blkIndent)break;if(e.sCount[t]-e.blkIndent>=4)break;for(M=!1,c=0,T=y.length;c<T;c++)if(y[c](e,A,n,!0)){M=!0;break}if(M)break;if(h){if((b=i(e,A))<0)break;I=e.bMarks[A]+e.tShift[A]}else if((b=s(e,A))<0)break;if(E!==e.src.charCodeAt(b-1))break}return(D=h?e.push("ordered_list_close","ol",-1):e.push("bullet_list_close","ul",-1)).markup=String.fromCharCode(E),d[1]=A,e.line=A,e.parentType=k,x&&function(e,t){var n,r,s=e.level+2;for(n=t+2,r=e.tokens.length-2;n<r;n++)e.tokens[n].level===s&&"paragraph_open"===e.tokens[n].type&&(e.tokens[n+2].hidden=!0,e.tokens[n].hidden=!0,n+=2)}(e,m),!0}},7046:e=>{"use strict";e.exports=function(e,t){var n,r,s,i,o,a,c=t+1,l=e.md.block.ruler.getRules("paragraph"),u=e.lineMax;for(a=e.parentType,e.parentType="paragraph";c<u&&!e.isEmpty(c);c++)if(!(e.sCount[c]-e.blkIndent>3||e.sCount[c]<0)){for(r=!1,s=0,i=l.length;s<i;s++)if(l[s](e,c,u,!0)){r=!0;break}if(r)break}return n=e.getLines(t,c,e.blkIndent,!1).trim(),e.line=c,(o=e.push("paragraph_open","p",1)).map=[t,e.line],(o=e.push("inline","",0)).content=n,o.map=[t,e.line],o.children=[],o=e.push("paragraph_close","p",-1),e.parentType=a,!0}},6897:(e,t,n)=>{"use strict";var r=n(9963).normalizeReference,s=n(9963).isSpace;e.exports=function(e,t,n,i){var o,a,c,l,u,p,h,f,T,d,m,E,_,g,A,C,N=0,k=e.bMarks[t]+e.tShift[t],O=e.eMarks[t],S=t+1;if(e.sCount[t]-e.blkIndent>=4)return!1;if(91!==e.src.charCodeAt(k))return!1;for(;++k<O;)if(93===e.src.charCodeAt(k)&&92!==e.src.charCodeAt(k-1)){if(k+1===O)return!1;if(58!==e.src.charCodeAt(k+1))return!1;break}for(l=e.lineMax,A=e.md.block.ruler.getRules("reference"),d=e.parentType,e.parentType="reference";S<l&&!e.isEmpty(S);S++)if(!(e.sCount[S]-e.blkIndent>3||e.sCount[S]<0)){for(g=!1,p=0,h=A.length;p<h;p++)if(A[p](e,S,l,!0)){g=!0;break}if(g)break}for(O=(_=e.getLines(t,S,e.blkIndent,!1).trim()).length,k=1;k<O;k++){if(91===(o=_.charCodeAt(k)))return!1;if(93===o){T=k;break}(10===o||92===o&&++k<O&&10===_.charCodeAt(k))&&N++}if(T<0||58!==_.charCodeAt(T+1))return!1;for(k=T+2;k<O;k++)if(10===(o=_.charCodeAt(k)))N++;else if(!s(o))break;if(!(m=e.md.helpers.parseLinkDestination(_,k,O)).ok)return!1;if(u=e.md.normalizeLink(m.str),!e.md.validateLink(u))return!1;for(a=k=m.pos,c=N+=m.lines,E=k;k<O;k++)if(10===(o=_.charCodeAt(k)))N++;else if(!s(o))break;for(m=e.md.helpers.parseLinkTitle(_,k,O),k<O&&E!==k&&m.ok?(C=m.str,k=m.pos,N+=m.lines):(C="",k=a,N=c);k<O&&(o=_.charCodeAt(k),s(o));)k++;if(k<O&&10!==_.charCodeAt(k)&&C)for(C="",k=a,N=c;k<O&&(o=_.charCodeAt(k),s(o));)k++;return!(k<O&&10!==_.charCodeAt(k)||!(f=r(_.slice(1,T)))||(i||(void 0===e.env.references&&(e.env.references={}),void 0===e.env.references[f]&&(e.env.references[f]={title:C,href:u}),e.parentType=d,e.line=t+N+1),0))}},7759:(e,t,n)=>{"use strict";var r=n(5099),s=n(9963).isSpace;function i(e,t,n,r){var i,o,a,c,l,u,p,h;for(this.src=e,this.md=t,this.env=n,this.tokens=r,this.bMarks=[],this.eMarks=[],this.tShift=[],this.sCount=[],this.bsCount=[],this.blkIndent=0,this.line=0,this.lineMax=0,this.tight=!1,this.ddIndent=-1,this.listIndent=-1,this.parentType="root",this.level=0,this.result="",h=!1,a=c=u=p=0,l=(o=this.src).length;c<l;c++){if(i=o.charCodeAt(c),!h){if(s(i)){u++,9===i?p+=4-p%4:p++;continue}h=!0}10!==i&&c!==l-1||(10!==i&&c++,this.bMarks.push(a),this.eMarks.push(c),this.tShift.push(u),this.sCount.push(p),this.bsCount.push(0),h=!1,u=0,p=0,a=c+1)}this.bMarks.push(o.length),this.eMarks.push(o.length),this.tShift.push(0),this.sCount.push(0),this.bsCount.push(0),this.lineMax=this.bMarks.length-1}i.prototype.push=function(e,t,n){var s=new r(e,t,n);return s.block=!0,n<0&&this.level--,s.level=this.level,n>0&&this.level++,this.tokens.push(s),s},i.prototype.isEmpty=function(e){return this.bMarks[e]+this.tShift[e]>=this.eMarks[e]},i.prototype.skipEmptyLines=function(e){for(var t=this.lineMax;e<t&&!(this.bMarks[e]+this.tShift[e]<this.eMarks[e]);e++);return e},i.prototype.skipSpaces=function(e){for(var t,n=this.src.length;e<n&&(t=this.src.charCodeAt(e),s(t));e++);return e},i.prototype.skipSpacesBack=function(e,t){if(e<=t)return e;for(;e>t;)if(!s(this.src.charCodeAt(--e)))return e+1;return e},i.prototype.skipChars=function(e,t){for(var n=this.src.length;e<n&&this.src.charCodeAt(e)===t;e++);return e},i.prototype.skipCharsBack=function(e,t,n){if(e<=n)return e;for(;e>n;)if(t!==this.src.charCodeAt(--e))return e+1;return e},i.prototype.getLines=function(e,t,n,r){var i,o,a,c,l,u,p,h=e;if(e>=t)return"";for(u=new Array(t-e),i=0;h<t;h++,i++){for(o=0,p=c=this.bMarks[h],l=h+1<t||r?this.eMarks[h]+1:this.eMarks[h];c<l&&o<n;){if(a=this.src.charCodeAt(c),s(a))9===a?o+=4-(o+this.bsCount[h])%4:o++;else{if(!(c-p<this.tShift[h]))break;o++}c++}u[i]=o>n?new Array(o-n+1).join(" ")+this.src.slice(c,l):this.src.slice(c,l)}return u.join("")},i.prototype.Token=r,e.exports=i},4752:(e,t,n)=>{"use strict";var r=n(9963).isSpace;function s(e,t){var n=e.bMarks[t]+e.tShift[t],r=e.eMarks[t];return e.src.substr(n,r-n)}function i(e){var t,n=[],r=0,s=e.length,i=!1,o=0,a="";for(t=e.charCodeAt(r);r<s;)124===t&&(i?(a+=e.substring(o,r-1),o=r):(n.push(a+e.substring(o,r)),a="",o=r+1)),i=92===t,r++,t=e.charCodeAt(r);return n.push(a+e.substring(o)),n}e.exports=function(e,t,n,o){var a,c,l,u,p,h,f,T,d,m,E,_,g,A,C,N,k,O;if(t+2>n)return!1;if(h=t+1,e.sCount[h]<e.blkIndent)return!1;if(e.sCount[h]-e.blkIndent>=4)return!1;if((l=e.bMarks[h]+e.tShift[h])>=e.eMarks[h])return!1;if(124!==(k=e.src.charCodeAt(l++))&&45!==k&&58!==k)return!1;if(l>=e.eMarks[h])return!1;if(124!==(O=e.src.charCodeAt(l++))&&45!==O&&58!==O&&!r(O))return!1;if(45===k&&r(O))return!1;for(;l<e.eMarks[h];){if(124!==(a=e.src.charCodeAt(l))&&45!==a&&58!==a&&!r(a))return!1;l++}for(f=(c=s(e,t+1)).split("|"),m=[],u=0;u<f.length;u++){if(!(E=f[u].trim())){if(0===u||u===f.length-1)continue;return!1}if(!/^:?-+:?$/.test(E))return!1;58===E.charCodeAt(E.length-1)?m.push(58===E.charCodeAt(0)?"center":"right"):58===E.charCodeAt(0)?m.push("left"):m.push("")}if(-1===(c=s(e,t).trim()).indexOf("|"))return!1;if(e.sCount[t]-e.blkIndent>=4)return!1;if((f=i(c)).length&&""===f[0]&&f.shift(),f.length&&""===f[f.length-1]&&f.pop(),0===(T=f.length)||T!==m.length)return!1;if(o)return!0;for(A=e.parentType,e.parentType="table",N=e.md.block.ruler.getRules("blockquote"),(d=e.push("table_open","table",1)).map=_=[t,0],(d=e.push("thead_open","thead",1)).map=[t,t+1],(d=e.push("tr_open","tr",1)).map=[t,t+1],u=0;u<f.length;u++)d=e.push("th_open","th",1),m[u]&&(d.attrs=[["style","text-align:"+m[u]]]),(d=e.push("inline","",0)).content=f[u].trim(),d.children=[],d=e.push("th_close","th",-1);for(d=e.push("tr_close","tr",-1),d=e.push("thead_close","thead",-1),h=t+2;h<n&&!(e.sCount[h]<e.blkIndent);h++){for(C=!1,u=0,p=N.length;u<p;u++)if(N[u](e,h,n,!0)){C=!0;break}if(C)break;if(!(c=s(e,h).trim()))break;if(e.sCount[h]-e.blkIndent>=4)break;for((f=i(c)).length&&""===f[0]&&f.shift(),f.length&&""===f[f.length-1]&&f.pop(),h===t+2&&((d=e.push("tbody_open","tbody",1)).map=g=[t+2,0]),(d=e.push("tr_open","tr",1)).map=[h,h+1],u=0;u<T;u++)d=e.push("td_open","td",1),m[u]&&(d.attrs=[["style","text-align:"+m[u]]]),(d=e.push("inline","",0)).content=f[u]?f[u].trim():"",d.children=[],d=e.push("td_close","td",-1);d=e.push("tr_close","tr",-1)}return g&&(d=e.push("tbody_close","tbody",-1),g[1]=h),d=e.push("table_close","table",-1),_[1]=h,e.parentType=A,e.line=h,!0}},3437:e=>{"use strict";e.exports=function(e){var t;e.inlineMode?((t=new e.Token("inline","",0)).content=e.src,t.map=[0,1],t.children=[],e.tokens.push(t)):e.md.block.parse(e.src,e.md,e.env,e.tokens)}},3547:e=>{"use strict";e.exports=function(e){var t,n,r,s=e.tokens;for(n=0,r=s.length;n<r;n++)"inline"===(t=s[n]).type&&e.md.inline.parse(t.content,e.md,e.env,t.children)}},986:(e,t,n)=>{"use strict";var r=n(9963).arrayReplaceAt;function s(e){return/^<\/a\s*>/i.test(e)}e.exports=function(e){var t,n,i,o,a,c,l,u,p,h,f,T,d,m,E,_,g,A,C=e.tokens;if(e.md.options.linkify)for(n=0,i=C.length;n<i;n++)if("inline"===C[n].type&&e.md.linkify.pretest(C[n].content))for(d=0,t=(o=C[n].children).length-1;t>=0;t--)if("link_close"!==(c=o[t]).type){if("html_inline"===c.type&&(A=c.content,/^<a[>\s]/i.test(A)&&d>0&&d--,s(c.content)&&d++),!(d>0)&&"text"===c.type&&e.md.linkify.test(c.content)){for(p=c.content,g=e.md.linkify.match(p),l=[],T=c.level,f=0,u=0;u<g.length;u++)m=g[u].url,E=e.md.normalizeLink(m),e.md.validateLink(E)&&(_=g[u].text,_=g[u].schema?"mailto:"!==g[u].schema||/^mailto:/i.test(_)?e.md.normalizeLinkText(_):e.md.normalizeLinkText("mailto:"+_).replace(/^mailto:/,""):e.md.normalizeLinkText("http://"+_).replace(/^http:\/\//,""),(h=g[u].index)>f&&((a=new e.Token("text","",0)).content=p.slice(f,h),a.level=T,l.push(a)),(a=new e.Token("link_open","a",1)).attrs=[["href",E]],a.level=T++,a.markup="linkify",a.info="auto",l.push(a),(a=new e.Token("text","",0)).content=_,a.level=T,l.push(a),(a=new e.Token("link_close","a",-1)).level=--T,a.markup="linkify",a.info="auto",l.push(a),f=g[u].lastIndex);f<p.length&&((a=new e.Token("text","",0)).content=p.slice(f),a.level=T,l.push(a)),C[n].children=o=r(o,t,l)}}else for(t--;o[t].level!==c.level&&"link_open"!==o[t].type;)t--}},803:e=>{"use strict";var t=/\r\n?|\n/g,n=/\0/g;e.exports=function(e){var r;r=(r=e.src.replace(t,"\n")).replace(n,"�"),e.src=r}},203:e=>{"use strict";var t=/\+-|\.\.|\?\?\?\?|!!!!|,,|--/,n=/\((c|tm|r|p)\)/i,r=/\((c|tm|r|p)\)/gi,s={c:"©",r:"®",p:"§",tm:"™"};function i(e,t){return s[t.toLowerCase()]}function o(e){var t,n,s=0;for(t=e.length-1;t>=0;t--)"text"!==(n=e[t]).type||s||(n.content=n.content.replace(r,i)),"link_open"===n.type&&"auto"===n.info&&s--,"link_close"===n.type&&"auto"===n.info&&s++}function a(e){var n,r,s=0;for(n=e.length-1;n>=0;n--)"text"!==(r=e[n]).type||s||t.test(r.content)&&(r.content=r.content.replace(/\+-/g,"±").replace(/\.{2,}/g,"…").replace(/([?!])…/g,"$1..").replace(/([?!]){4,}/g,"$1$1$1").replace(/,{2,}/g,",").replace(/(^|[^-])---(?=[^-]|$)/gm,"$1—").replace(/(^|\s)--(?=\s|$)/gm,"$1–").replace(/(^|[^-\s])--(?=[^-\s]|$)/gm,"$1–")),"link_open"===r.type&&"auto"===r.info&&s--,"link_close"===r.type&&"auto"===r.info&&s++}e.exports=function(e){var r;if(e.md.options.typographer)for(r=e.tokens.length-1;r>=0;r--)"inline"===e.tokens[r].type&&(n.test(e.tokens[r].content)&&o(e.tokens[r].children),t.test(e.tokens[r].content)&&a(e.tokens[r].children))}},5260:(e,t,n)=>{"use strict";var r=n(9963).isWhiteSpace,s=n(9963).isPunctChar,i=n(9963).isMdAsciiPunct,o=/['"]/,a=/['"]/g;function c(e,t,n){return e.substr(0,t)+n+e.substr(t+1)}function l(e,t){var n,o,l,u,p,h,f,T,d,m,E,_,g,A,C,N,k,O,S,L,v;for(S=[],n=0;n<e.length;n++){for(o=e[n],f=e[n].level,k=S.length-1;k>=0&&!(S[k].level<=f);k--);if(S.length=k+1,"text"===o.type){p=0,h=(l=o.content).length;e:for(;p<h&&(a.lastIndex=p,u=a.exec(l));){if(C=N=!0,p=u.index+1,O="'"===u[0],d=32,u.index-1>=0)d=l.charCodeAt(u.index-1);else for(k=n-1;k>=0&&"softbreak"!==e[k].type&&"hardbreak"!==e[k].type;k--)if(e[k].content){d=e[k].content.charCodeAt(e[k].content.length-1);break}if(m=32,p<h)m=l.charCodeAt(p);else for(k=n+1;k<e.length&&"softbreak"!==e[k].type&&"hardbreak"!==e[k].type;k++)if(e[k].content){m=e[k].content.charCodeAt(0);break}if(E=i(d)||s(String.fromCharCode(d)),_=i(m)||s(String.fromCharCode(m)),g=r(d),(A=r(m))?C=!1:_&&(g||E||(C=!1)),g?N=!1:E&&(A||_||(N=!1)),34===m&&'"'===u[0]&&d>=48&&d<=57&&(N=C=!1),C&&N&&(C=E,N=_),C||N){if(N)for(k=S.length-1;k>=0&&(T=S[k],!(S[k].level<f));k--)if(T.single===O&&S[k].level===f){T=S[k],O?(L=t.md.options.quotes[2],v=t.md.options.quotes[3]):(L=t.md.options.quotes[0],v=t.md.options.quotes[1]),o.content=c(o.content,u.index,v),e[T.token].content=c(e[T.token].content,T.pos,L),p+=v.length-1,T.token===n&&(p+=L.length-1),h=(l=o.content).length,S.length=k;continue e}C?S.push({token:n,pos:u.index,single:O,level:f}):N&&O&&(o.content=c(o.content,u.index,"’"))}else O&&(o.content=c(o.content,u.index,"’"))}}}}e.exports=function(e){var t;if(e.md.options.typographer)for(t=e.tokens.length-1;t>=0;t--)"inline"===e.tokens[t].type&&o.test(e.tokens[t].content)&&l(e.tokens[t].children,e)}},1839:(e,t,n)=>{"use strict";var r=n(5099);function s(e,t,n){this.src=e,this.env=n,this.tokens=[],this.inlineMode=!1,this.md=t}s.prototype.Token=r,e.exports=s},6955:e=>{"use strict";var t=/^([a-zA-Z0-9.!#$%&'*+\/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*)$/,n=/^([a-zA-Z][a-zA-Z0-9+.\-]{1,31}):([^<>\x00-\x20]*)$/;e.exports=function(e,r){var s,i,o,a,c,l,u=e.pos;if(60!==e.src.charCodeAt(u))return!1;for(c=e.pos,l=e.posMax;;){if(++u>=l)return!1;if(60===(a=e.src.charCodeAt(u)))return!1;if(62===a)break}return s=e.src.slice(c+1,u),n.test(s)?(i=e.md.normalizeLink(s),!!e.md.validateLink(i)&&(r||((o=e.push("link_open","a",1)).attrs=[["href",i]],o.markup="autolink",o.info="auto",(o=e.push("text","",0)).content=e.md.normalizeLinkText(s),(o=e.push("link_close","a",-1)).markup="autolink",o.info="auto"),e.pos+=s.length+2,!0)):!!t.test(s)&&(i=e.md.normalizeLink("mailto:"+s),!!e.md.validateLink(i)&&(r||((o=e.push("link_open","a",1)).attrs=[["href",i]],o.markup="autolink",o.info="auto",(o=e.push("text","",0)).content=e.md.normalizeLinkText(s),(o=e.push("link_close","a",-1)).markup="autolink",o.info="auto"),e.pos+=s.length+2,!0))}},6757:e=>{"use strict";e.exports=function(e,t){var n,r,s,i,o,a,c,l,u=e.pos;if(96!==e.src.charCodeAt(u))return!1;for(n=u,u++,r=e.posMax;u<r&&96===e.src.charCodeAt(u);)u++;if(c=(s=e.src.slice(n,u)).length,e.backticksScanned&&(e.backticks[c]||0)<=n)return t||(e.pending+=s),e.pos+=c,!0;for(o=a=u;-1!==(o=e.src.indexOf("`",a));){for(a=o+1;a<r&&96===e.src.charCodeAt(a);)a++;if((l=a-o)===c)return t||((i=e.push("code_inline","code",0)).markup=s,i.content=e.src.slice(u,o).replace(/\n/g," ").replace(/^ (.+) $/,"$1")),e.pos=a,!0;e.backticks[l]=o}return e.backticksScanned=!0,t||(e.pending+=s),e.pos+=c,!0}},5940:e=>{"use strict";function t(e,t){var n,r,s,i,o,a,c,l,u={},p=t.length;if(p){var h=0,f=-2,T=[];for(n=0;n<p;n++)if(s=t[n],T.push(0),t[h].marker===s.marker&&f===s.token-1||(h=n),f=s.token,s.length=s.length||0,s.close){for(u.hasOwnProperty(s.marker)||(u[s.marker]=[-1,-1,-1,-1,-1,-1]),o=u[s.marker][(s.open?3:0)+s.length%3],a=r=h-T[h]-1;r>o;r-=T[r]+1)if((i=t[r]).marker===s.marker&&i.open&&i.end<0&&(c=!1,(i.close||s.open)&&(i.length+s.length)%3==0&&(i.length%3==0&&s.length%3==0||(c=!0)),!c)){l=r>0&&!t[r-1].open?T[r-1]+1:0,T[n]=n-r+l,T[r]=l,s.open=!1,i.end=n,i.close=!1,a=-1,f=-2;break}-1!==a&&(u[s.marker][(s.open?3:0)+(s.length||0)%3]=a)}}}e.exports=function(e){var n,r=e.tokens_meta,s=e.tokens_meta.length;for(t(0,e.delimiters),n=0;n<s;n++)r[n]&&r[n].delimiters&&t(0,r[n].delimiters)}},3898:e=>{"use strict";function t(e,t){var n,r,s,i,o,a;for(n=t.length-1;n>=0;n--)95!==(r=t[n]).marker&&42!==r.marker||-1!==r.end&&(s=t[r.end],a=n>0&&t[n-1].end===r.end+1&&t[n-1].marker===r.marker&&t[n-1].token===r.token-1&&t[r.end+1].token===s.token+1,o=String.fromCharCode(r.marker),(i=e.tokens[r.token]).type=a?"strong_open":"em_open",i.tag=a?"strong":"em",i.nesting=1,i.markup=a?o+o:o,i.content="",(i=e.tokens[s.token]).type=a?"strong_close":"em_close",i.tag=a?"strong":"em",i.nesting=-1,i.markup=a?o+o:o,i.content="",a&&(e.tokens[t[n-1].token].content="",e.tokens[t[r.end+1].token].content="",n--))}e.exports.q=function(e,t){var n,r,s=e.pos,i=e.src.charCodeAt(s);if(t)return!1;if(95!==i&&42!==i)return!1;for(r=e.scanDelims(e.pos,42===i),n=0;n<r.length;n++)e.push("text","",0).content=String.fromCharCode(i),e.delimiters.push({marker:i,length:r.length,token:e.tokens.length-1,end:-1,open:r.can_open,close:r.can_close});return e.pos+=r.length,!0},e.exports.g=function(e){var n,r=e.tokens_meta,s=e.tokens_meta.length;for(t(e,e.delimiters),n=0;n<s;n++)r[n]&&r[n].delimiters&&t(e,r[n].delimiters)}},8103:(e,t,n)=>{"use strict";var r=n(8359),s=n(9963).has,i=n(9963).isValidEntityCode,o=n(9963).fromCodePoint,a=/^&#((?:x[a-f0-9]{1,6}|[0-9]{1,7}));/i,c=/^&([a-z][a-z0-9]{1,31});/i;e.exports=function(e,t){var n,l,u=e.pos,p=e.posMax;if(38!==e.src.charCodeAt(u))return!1;if(u+1<p)if(35===e.src.charCodeAt(u+1)){if(l=e.src.slice(u).match(a))return t||(n="x"===l[1][0].toLowerCase()?parseInt(l[1].slice(1),16):parseInt(l[1],10),e.pending+=i(n)?o(n):o(65533)),e.pos+=l[0].length,!0}else if((l=e.src.slice(u).match(c))&&s(r,l[1]))return t||(e.pending+=r[l[1]]),e.pos+=l[0].length,!0;return t||(e.pending+="&"),e.pos++,!0}},1231:(e,t,n)=>{"use strict";for(var r=n(9963).isSpace,s=[],i=0;i<256;i++)s.push(0);"\\!\"#$%&'()*+,./:;<=>?@[]^_`{|}~-".split("").forEach((function(e){s[e.charCodeAt(0)]=1})),e.exports=function(e,t){var n,i=e.pos,o=e.posMax;if(92!==e.src.charCodeAt(i))return!1;if(++i<o){if((n=e.src.charCodeAt(i))<256&&0!==s[n])return t||(e.pending+=e.src[i]),e.pos+=2,!0;if(10===n){for(t||e.push("hardbreak","br",0),i++;i<o&&(n=e.src.charCodeAt(i),r(n));)i++;return e.pos=i,!0}}return t||(e.pending+="\\"),e.pos++,!0}},961:(e,t,n)=>{"use strict";var r=n(6557).l;e.exports=function(e,t){var n,s,i,o=e.pos;return!(!e.md.options.html||(i=e.posMax,60!==e.src.charCodeAt(o)||o+2>=i||33!==(n=e.src.charCodeAt(o+1))&&63!==n&&47!==n&&!function(e){var t=32|e;return t>=97&&t<=122}(n)||!(s=e.src.slice(o).match(r))||(t||(e.push("html_inline","",0).content=e.src.slice(o,o+s[0].length)),e.pos+=s[0].length,0)))}},3707:(e,t,n)=>{"use strict";var r=n(9963).normalizeReference,s=n(9963).isSpace;e.exports=function(e,t){var n,i,o,a,c,l,u,p,h,f,T,d,m,E="",_=e.pos,g=e.posMax;if(33!==e.src.charCodeAt(e.pos))return!1;if(91!==e.src.charCodeAt(e.pos+1))return!1;if(l=e.pos+2,(c=e.md.helpers.parseLinkLabel(e,e.pos+1,!1))<0)return!1;if((u=c+1)<g&&40===e.src.charCodeAt(u)){for(u++;u<g&&(i=e.src.charCodeAt(u),s(i)||10===i);u++);if(u>=g)return!1;for(m=u,(h=e.md.helpers.parseLinkDestination(e.src,u,e.posMax)).ok&&(E=e.md.normalizeLink(h.str),e.md.validateLink(E)?u=h.pos:E=""),m=u;u<g&&(i=e.src.charCodeAt(u),s(i)||10===i);u++);if(h=e.md.helpers.parseLinkTitle(e.src,u,e.posMax),u<g&&m!==u&&h.ok)for(f=h.str,u=h.pos;u<g&&(i=e.src.charCodeAt(u),s(i)||10===i);u++);else f="";if(u>=g||41!==e.src.charCodeAt(u))return e.pos=_,!1;u++}else{if(void 0===e.env.references)return!1;if(u<g&&91===e.src.charCodeAt(u)?(m=u+1,(u=e.md.helpers.parseLinkLabel(e,u))>=0?a=e.src.slice(m,u++):u=c+1):u=c+1,a||(a=e.src.slice(l,c)),!(p=e.env.references[r(a)]))return e.pos=_,!1;E=p.href,f=p.title}return t||(o=e.src.slice(l,c),e.md.inline.parse(o,e.md,e.env,d=[]),(T=e.push("image","img",0)).attrs=n=[["src",E],["alt",""]],T.children=d,T.content=o,f&&n.push(["title",f])),e.pos=u,e.posMax=g,!0}},6552:(e,t,n)=>{"use strict";var r=n(9963).normalizeReference,s=n(9963).isSpace;e.exports=function(e,t){var n,i,o,a,c,l,u,p,h="",f="",T=e.pos,d=e.posMax,m=e.pos,E=!0;if(91!==e.src.charCodeAt(e.pos))return!1;if(c=e.pos+1,(a=e.md.helpers.parseLinkLabel(e,e.pos,!0))<0)return!1;if((l=a+1)<d&&40===e.src.charCodeAt(l)){for(E=!1,l++;l<d&&(i=e.src.charCodeAt(l),s(i)||10===i);l++);if(l>=d)return!1;if(m=l,(u=e.md.helpers.parseLinkDestination(e.src,l,e.posMax)).ok){for(h=e.md.normalizeLink(u.str),e.md.validateLink(h)?l=u.pos:h="",m=l;l<d&&(i=e.src.charCodeAt(l),s(i)||10===i);l++);if(u=e.md.helpers.parseLinkTitle(e.src,l,e.posMax),l<d&&m!==l&&u.ok)for(f=u.str,l=u.pos;l<d&&(i=e.src.charCodeAt(l),s(i)||10===i);l++);}(l>=d||41!==e.src.charCodeAt(l))&&(E=!0),l++}if(E){if(void 0===e.env.references)return!1;if(l<d&&91===e.src.charCodeAt(l)?(m=l+1,(l=e.md.helpers.parseLinkLabel(e,l))>=0?o=e.src.slice(m,l++):l=a+1):l=a+1,o||(o=e.src.slice(c,a)),!(p=e.env.references[r(o)]))return e.pos=T,!1;h=p.href,f=p.title}return t||(e.pos=c,e.posMax=a,e.push("link_open","a",1).attrs=n=[["href",h]],f&&n.push(["title",f]),e.md.inline.tokenize(e),e.push("link_close","a",-1)),e.pos=l,e.posMax=d,!0}},2534:(e,t,n)=>{"use strict";var r=n(9963).isSpace;e.exports=function(e,t){var n,s,i,o=e.pos;if(10!==e.src.charCodeAt(o))return!1;if(n=e.pending.length-1,s=e.posMax,!t)if(n>=0&&32===e.pending.charCodeAt(n))if(n>=1&&32===e.pending.charCodeAt(n-1)){for(i=n-1;i>=1&&32===e.pending.charCodeAt(i-1);)i--;e.pending=e.pending.slice(0,i),e.push("hardbreak","br",0)}else e.pending=e.pending.slice(0,-1),e.push("softbreak","br",0);else e.push("softbreak","br",0);for(o++;o<s&&r(e.src.charCodeAt(o));)o++;return e.pos=o,!0}},979:(e,t,n)=>{"use strict";var r=n(5099),s=n(9963).isWhiteSpace,i=n(9963).isPunctChar,o=n(9963).isMdAsciiPunct;function a(e,t,n,r){this.src=e,this.env=n,this.md=t,this.tokens=r,this.tokens_meta=Array(r.length),this.pos=0,this.posMax=this.src.length,this.level=0,this.pending="",this.pendingLevel=0,this.cache={},this.delimiters=[],this._prev_delimiters=[],this.backticks={},this.backticksScanned=!1}a.prototype.pushPending=function(){var e=new r("text","",0);return e.content=this.pending,e.level=this.pendingLevel,this.tokens.push(e),this.pending="",e},a.prototype.push=function(e,t,n){this.pending&&this.pushPending();var s=new r(e,t,n),i=null;return n<0&&(this.level--,this.delimiters=this._prev_delimiters.pop()),s.level=this.level,n>0&&(this.level++,this._prev_delimiters.push(this.delimiters),this.delimiters=[],i={delimiters:this.delimiters}),this.pendingLevel=this.level,this.tokens.push(s),this.tokens_meta.push(i),s},a.prototype.scanDelims=function(e,t){var n,r,a,c,l,u,p,h,f,T=e,d=!0,m=!0,E=this.posMax,_=this.src.charCodeAt(e);for(n=e>0?this.src.charCodeAt(e-1):32;T<E&&this.src.charCodeAt(T)===_;)T++;return a=T-e,r=T<E?this.src.charCodeAt(T):32,p=o(n)||i(String.fromCharCode(n)),f=o(r)||i(String.fromCharCode(r)),u=s(n),(h=s(r))?d=!1:f&&(u||p||(d=!1)),u?m=!1:p&&(h||f||(m=!1)),t?(c=d,l=m):(c=d&&(!m||p),l=m&&(!d||f)),{can_open:c,can_close:l,length:a}},a.prototype.Token=r,e.exports=a},7141:e=>{"use strict";function t(e,t){var n,r,s,i,o,a=[],c=t.length;for(n=0;n<c;n++)126===(s=t[n]).marker&&-1!==s.end&&(i=t[s.end],(o=e.tokens[s.token]).type="s_open",o.tag="s",o.nesting=1,o.markup="~~",o.content="",(o=e.tokens[i.token]).type="s_close",o.tag="s",o.nesting=-1,o.markup="~~",o.content="","text"===e.tokens[i.token-1].type&&"~"===e.tokens[i.token-1].content&&a.push(i.token-1));for(;a.length;){for(r=(n=a.pop())+1;r<e.tokens.length&&"s_close"===e.tokens[r].type;)r++;n!==--r&&(o=e.tokens[r],e.tokens[r]=e.tokens[n],e.tokens[n]=o)}}e.exports.q=function(e,t){var n,r,s,i,o=e.pos,a=e.src.charCodeAt(o);if(t)return!1;if(126!==a)return!1;if(s=(r=e.scanDelims(e.pos,!0)).length,i=String.fromCharCode(a),s<2)return!1;for(s%2&&(e.push("text","",0).content=i,s--),n=0;n<s;n+=2)e.push("text","",0).content=i+i,e.delimiters.push({marker:a,length:0,token:e.tokens.length-1,end:-1,open:r.can_open,close:r.can_close});return e.pos+=r.length,!0},e.exports.g=function(e){var n,r=e.tokens_meta,s=e.tokens_meta.length;for(t(e,e.delimiters),n=0;n<s;n++)r[n]&&r[n].delimiters&&t(e,r[n].delimiters)}},2015:e=>{"use strict";function t(e){switch(e){case 10:case 33:case 35:case 36:case 37:case 38:case 42:case 43:case 45:case 58:case 60:case 61:case 62:case 64:case 91:case 92:case 93:case 94:case 95:case 96:case 123:case 125:case 126:return!0;default:return!1}}e.exports=function(e,n){for(var r=e.pos;r<e.posMax&&!t(e.src.charCodeAt(r));)r++;return r!==e.pos&&(n||(e.pending+=e.src.slice(e.pos,r)),e.pos=r,!0)}},7729:e=>{"use strict";e.exports=function(e){var t,n,r=0,s=e.tokens,i=e.tokens.length;for(t=n=0;t<i;t++)s[t].nesting<0&&r--,s[t].level=r,s[t].nesting>0&&r++,"text"===s[t].type&&t+1<i&&"text"===s[t+1].type?s[t+1].content=s[t].content+s[t+1].content:(t!==n&&(s[n]=s[t]),n++);t!==n&&(s.length=n)}},5099:e=>{"use strict";function t(e,t,n){this.type=e,this.tag=t,this.attrs=null,this.map=null,this.nesting=n,this.level=0,this.children=null,this.content="",this.markup="",this.info="",this.meta=null,this.block=!1,this.hidden=!1}t.prototype.attrIndex=function(e){var t,n,r;if(!this.attrs)return-1;for(n=0,r=(t=this.attrs).length;n<r;n++)if(t[n][0]===e)return n;return-1},t.prototype.attrPush=function(e){this.attrs?this.attrs.push(e):this.attrs=[e]},t.prototype.attrSet=function(e,t){var n=this.attrIndex(e),r=[e,t];n<0?this.attrPush(r):this.attrs[n]=r},t.prototype.attrGet=function(e){var t=this.attrIndex(e),n=null;return t>=0&&(n=this.attrs[t][1]),n},t.prototype.attrJoin=function(e,t){var n=this.attrIndex(e);n<0?this.attrPush([e,t]):this.attrs[n][1]=this.attrs[n][1]+" "+t},e.exports=t},3527:e=>{"use strict";var t={};function n(e,r){var s;return"string"!=typeof r&&(r=n.defaultChars),s=function(e){var n,r,s=t[e];if(s)return s;for(s=t[e]=[],n=0;n<128;n++)r=String.fromCharCode(n),s.push(r);for(n=0;n<e.length;n++)s[r=e.charCodeAt(n)]="%"+("0"+r.toString(16).toUpperCase()).slice(-2);return s}(r),e.replace(/(%[a-f0-9]{2})+/gi,(function(e){var t,n,r,i,o,a,c,l="";for(t=0,n=e.length;t<n;t+=3)(r=parseInt(e.slice(t+1,t+3),16))<128?l+=s[r]:192==(224&r)&&t+3<n&&128==(192&(i=parseInt(e.slice(t+4,t+6),16)))?(l+=(c=r<<6&1984|63&i)<128?"��":String.fromCharCode(c),t+=3):224==(240&r)&&t+6<n&&(i=parseInt(e.slice(t+4,t+6),16),o=parseInt(e.slice(t+7,t+9),16),128==(192&i)&&128==(192&o))?(l+=(c=r<<12&61440|i<<6&4032|63&o)<2048||c>=55296&&c<=57343?"���":String.fromCharCode(c),t+=6):240==(248&r)&&t+9<n&&(i=parseInt(e.slice(t+4,t+6),16),o=parseInt(e.slice(t+7,t+9),16),a=parseInt(e.slice(t+10,t+12),16),128==(192&i)&&128==(192&o)&&128==(192&a))?((c=r<<18&1835008|i<<12&258048|o<<6&4032|63&a)<65536||c>1114111?l+="����":(c-=65536,l+=String.fromCharCode(55296+(c>>10),56320+(1023&c))),t+=9):l+="�";return l}))}n.defaultChars=";/?:@&=+$,#",n.componentChars="",e.exports=n},3331:e=>{"use strict";var t={};function n(e,r,s){var i,o,a,c,l,u="";for("string"!=typeof r&&(s=r,r=n.defaultChars),void 0===s&&(s=!0),l=function(e){var n,r,s=t[e];if(s)return s;for(s=t[e]=[],n=0;n<128;n++)r=String.fromCharCode(n),/^[0-9a-z]$/i.test(r)?s.push(r):s.push("%"+("0"+n.toString(16).toUpperCase()).slice(-2));for(n=0;n<e.length;n++)s[e.charCodeAt(n)]=e[n];return s}(r),i=0,o=e.length;i<o;i++)if(a=e.charCodeAt(i),s&&37===a&&i+2<o&&/^[0-9a-f]{2}$/i.test(e.slice(i+1,i+3)))u+=e.slice(i,i+3),i+=2;else if(a<128)u+=l[a];else if(a>=55296&&a<=57343){if(a>=55296&&a<=56319&&i+1<o&&(c=e.charCodeAt(i+1))>=56320&&c<=57343){u+=encodeURIComponent(e[i]+e[i+1]),i++;continue}u+="%EF%BF%BD"}else u+=encodeURIComponent(e[i]);return u}n.defaultChars=";/?:@&=+$,-_.!~*'()#",n.componentChars="-_.!~*'()",e.exports=n},6998:e=>{"use strict";e.exports=function(e){var t="";return t+=e.protocol||"",t+=e.slashes?"//":"",t+=e.auth?e.auth+"@":"",e.hostname&&-1!==e.hostname.indexOf(":")?t+="["+e.hostname+"]":t+=e.hostname||"",t+=e.port?":"+e.port:"",t+=e.pathname||"",(t+=e.search||"")+(e.hash||"")}},6781:(e,t,n)=>{"use strict";e.exports.encode=n(3331),e.exports.decode=n(3527),e.exports.format=n(6998),e.exports.parse=n(4994)},4994:e=>{"use strict";function t(){this.protocol=null,this.slashes=null,this.auth=null,this.port=null,this.hostname=null,this.hash=null,this.search=null,this.pathname=null}var n=/^([a-z0-9.+-]+:)/i,r=/:[0-9]*$/,s=/^(\/\/?(?!\/)[^\?\s]*)(\?[^\s]*)?$/,i=["{","}","|","\\","^","`"].concat(["<",">",'"',"`"," ","\r","\n","\t"]),o=["'"].concat(i),a=["%","/","?",";","#"].concat(o),c=["/","?","#"],l=/^[+a-z0-9A-Z_-]{0,63}$/,u=/^([+a-z0-9A-Z_-]{0,63})(.*)$/,p={javascript:!0,"javascript:":!0},h={http:!0,https:!0,ftp:!0,gopher:!0,file:!0,"http:":!0,"https:":!0,"ftp:":!0,"gopher:":!0,"file:":!0};t.prototype.parse=function(e,t){var r,i,o,f,T,d=e;if(d=d.trim(),!t&&1===e.split("#").length){var m=s.exec(d);if(m)return this.pathname=m[1],m[2]&&(this.search=m[2]),this}var E=n.exec(d);if(E&&(o=(E=E[0]).toLowerCase(),this.protocol=E,d=d.substr(E.length)),(t||E||d.match(/^\/\/[^@\/]+@[^@\/]+/))&&(!(T="//"===d.substr(0,2))||E&&p[E]||(d=d.substr(2),this.slashes=!0)),!p[E]&&(T||E&&!h[E])){var _,g,A=-1;for(r=0;r<c.length;r++)-1!==(f=d.indexOf(c[r]))&&(-1===A||f<A)&&(A=f);for(-1!==(g=-1===A?d.lastIndexOf("@"):d.lastIndexOf("@",A))&&(_=d.slice(0,g),d=d.slice(g+1),this.auth=_),A=-1,r=0;r<a.length;r++)-1!==(f=d.indexOf(a[r]))&&(-1===A||f<A)&&(A=f);-1===A&&(A=d.length),":"===d[A-1]&&A--;var C=d.slice(0,A);d=d.slice(A),this.parseHost(C),this.hostname=this.hostname||"";var N="["===this.hostname[0]&&"]"===this.hostname[this.hostname.length-1];if(!N){var k=this.hostname.split(/\./);for(r=0,i=k.length;r<i;r++){var O=k[r];if(O&&!O.match(l)){for(var S="",L=0,v=O.length;L<v;L++)O.charCodeAt(L)>127?S+="x":S+=O[L];if(!S.match(l)){var b=k.slice(0,r),R=k.slice(r+1),I=O.match(u);I&&(b.push(I[1]),R.unshift(I[2])),R.length&&(d=R.join(".")+d),this.hostname=b.join(".");break}}}}this.hostname.length>255&&(this.hostname=""),N&&(this.hostname=this.hostname.substr(1,this.hostname.length-2))}var M=d.indexOf("#");-1!==M&&(this.hash=d.substr(M),d=d.slice(0,M));var y=d.indexOf("?");return-1!==y&&(this.search=d.substr(y),d=d.slice(0,y)),d&&(this.pathname=d),h[o]&&this.hostname&&!this.pathname&&(this.pathname=""),this},t.prototype.parseHost=function(e){var t=r.exec(e);t&&(":"!==(t=t[0])&&(this.port=t.substr(1)),e=e.substr(0,e.length-t.length)),e&&(this.hostname=e)},e.exports=function(e,n){if(e&&e instanceof t)return e;var r=new t;return r.parse(e,n),r}},135:(e,t,n)=>{"use strict";var r=n(6584).DOCUMENT_MODE,s=["+//silmaril//dtd html pro v0r11 19970101//en","-//advasoft ltd//dtd html 3.0 aswedit + extensions//en","-//as//dtd html 3.0 aswedit + extensions//en","-//ietf//dtd html 2.0 level 1//en","-//ietf//dtd html 2.0 level 2//en","-//ietf//dtd html 2.0 strict level 1//en","-//ietf//dtd html 2.0 strict level 2//en","-//ietf//dtd html 2.0 strict//en","-//ietf//dtd html 2.0//en","-//ietf//dtd html 2.1e//en","-//ietf//dtd html 3.0//en","-//ietf//dtd html 3.0//en//","-//ietf//dtd html 3.2 final//en","-//ietf//dtd html 3.2//en","-//ietf//dtd html 3//en","-//ietf//dtd html level 0//en","-//ietf//dtd html level 0//en//2.0","-//ietf//dtd html level 1//en","-//ietf//dtd html level 1//en//2.0","-//ietf//dtd html level 2//en","-//ietf//dtd html level 2//en//2.0","-//ietf//dtd html level 3//en","-//ietf//dtd html level 3//en//3.0","-//ietf//dtd html strict level 0//en","-//ietf//dtd html strict level 0//en//2.0","-//ietf//dtd html strict level 1//en","-//ietf//dtd html strict level 1//en//2.0","-//ietf//dtd html strict level 2//en","-//ietf//dtd html strict level 2//en//2.0","-//ietf//dtd html strict level 3//en","-//ietf//dtd html strict level 3//en//3.0","-//ietf//dtd html strict//en","-//ietf//dtd html strict//en//2.0","-//ietf//dtd html strict//en//3.0","-//ietf//dtd html//en","-//ietf//dtd html//en//2.0","-//ietf//dtd html//en//3.0","-//metrius//dtd metrius presentational//en","-//microsoft//dtd internet explorer 2.0 html strict//en","-//microsoft//dtd internet explorer 2.0 html//en","-//microsoft//dtd internet explorer 2.0 tables//en","-//microsoft//dtd internet explorer 3.0 html strict//en","-//microsoft//dtd internet explorer 3.0 html//en","-//microsoft//dtd internet explorer 3.0 tables//en","-//netscape comm. corp.//dtd html//en","-//netscape comm. corp.//dtd strict html//en","-//o'reilly and associates//dtd html 2.0//en","-//o'reilly and associates//dtd html extended 1.0//en","-//spyglass//dtd html 2.0 extended//en","-//sq//dtd html 2.0 hotmetal + extensions//en","-//sun microsystems corp.//dtd hotjava html//en","-//sun microsystems corp.//dtd hotjava strict html//en","-//w3c//dtd html 3 1995-03-24//en","-//w3c//dtd html 3.2 draft//en","-//w3c//dtd html 3.2 final//en","-//w3c//dtd html 3.2//en","-//w3c//dtd html 3.2s draft//en","-//w3c//dtd html 4.0 frameset//en","-//w3c//dtd html 4.0 transitional//en","-//w3c//dtd html experimental 19960712//en","-//w3c//dtd html experimental 970421//en","-//w3c//dtd w3 html//en","-//w3o//dtd w3 html 3.0//en","-//w3o//dtd w3 html 3.0//en//","-//webtechs//dtd mozilla html 2.0//en","-//webtechs//dtd mozilla html//en"],i=s.concat(["-//w3c//dtd html 4.01 frameset//","-//w3c//dtd html 4.01 transitional//"]),o=["-//w3o//dtd w3 html strict 3.0//en//","-/w3c/dtd html 4.0 transitional/en","html"],a=["-//W3C//DTD XHTML 1.0 Frameset//","-//W3C//DTD XHTML 1.0 Transitional//"],c=a.concat(["-//W3C//DTD HTML 4.01 Frameset//","-//W3C//DTD HTML 4.01 Transitional//"]);function l(e){var t=-1!==e.indexOf('"')?"'":'"';return t+e+t}function u(e,t){for(var n=0;n<t.length;n++)if(0===e.indexOf(t[n]))return!0;return!1}t.getDocumentMode=function(e,t,n){if("html"!==e)return r.QUIRKS;if(n&&"http://www.ibm.com/data/dtd/v11/ibmxhtml1-transitional.dtd"===n.toLowerCase())return r.QUIRKS;if(null!==t){if(t=t.toLowerCase(),o.indexOf(t)>-1)return r.QUIRKS;var l=null===n?i:s;if(u(t,l))return r.QUIRKS;if(u(t,l=null===n?a:c))return r.LIMITED_QUIRKS}return r.NO_QUIRKS},t.serializeContent=function(e,t,n){var r="!DOCTYPE ";return e&&(r+=e),null!==t?r+=" PUBLIC "+l(t):null!==n&&(r+=" SYSTEM"),null!==n&&(r+=" "+l(n)),r}},1723:(e,t,n)=>{"use strict";var r=n(7881),s=n(6584),i=s.TAG_NAMES,o=s.NAMESPACES,a=s.ATTRS,c={attributename:"attributeName",attributetype:"attributeType",basefrequency:"baseFrequency",baseprofile:"baseProfile",calcmode:"calcMode",clippathunits:"clipPathUnits",diffuseconstant:"diffuseConstant",edgemode:"edgeMode",filterunits:"filterUnits",glyphref:"glyphRef",gradienttransform:"gradientTransform",gradientunits:"gradientUnits",kernelmatrix:"kernelMatrix",kernelunitlength:"kernelUnitLength",keypoints:"keyPoints",keysplines:"keySplines",keytimes:"keyTimes",lengthadjust:"lengthAdjust",limitingconeangle:"limitingConeAngle",markerheight:"markerHeight",markerunits:"markerUnits",markerwidth:"markerWidth",maskcontentunits:"maskContentUnits",maskunits:"maskUnits",numoctaves:"numOctaves",pathlength:"pathLength",patterncontentunits:"patternContentUnits",patterntransform:"patternTransform",patternunits:"patternUnits",pointsatx:"pointsAtX",pointsaty:"pointsAtY",pointsatz:"pointsAtZ",preservealpha:"preserveAlpha",preserveaspectratio:"preserveAspectRatio",primitiveunits:"primitiveUnits",refx:"refX",refy:"refY",repeatcount:"repeatCount",repeatdur:"repeatDur",requiredextensions:"requiredExtensions",requiredfeatures:"requiredFeatures",specularconstant:"specularConstant",specularexponent:"specularExponent",spreadmethod:"spreadMethod",startoffset:"startOffset",stddeviation:"stdDeviation",stitchtiles:"stitchTiles",surfacescale:"surfaceScale",systemlanguage:"systemLanguage",tablevalues:"tableValues",targetx:"targetX",targety:"targetY",textlength:"textLength",viewbox:"viewBox",viewtarget:"viewTarget",xchannelselector:"xChannelSelector",ychannelselector:"yChannelSelector",zoomandpan:"zoomAndPan"},l={"xlink:actuate":{prefix:"xlink",name:"actuate",namespace:o.XLINK},"xlink:arcrole":{prefix:"xlink",name:"arcrole",namespace:o.XLINK},"xlink:href":{prefix:"xlink",name:"href",namespace:o.XLINK},"xlink:role":{prefix:"xlink",name:"role",namespace:o.XLINK},"xlink:show":{prefix:"xlink",name:"show",namespace:o.XLINK},"xlink:title":{prefix:"xlink",name:"title",namespace:o.XLINK},"xlink:type":{prefix:"xlink",name:"type",namespace:o.XLINK},"xml:base":{prefix:"xml",name:"base",namespace:o.XML},"xml:lang":{prefix:"xml",name:"lang",namespace:o.XML},"xml:space":{prefix:"xml",name:"space",namespace:o.XML},xmlns:{prefix:"",name:"xmlns",namespace:o.XMLNS},"xmlns:xlink":{prefix:"xmlns",name:"xlink",namespace:o.XMLNS}},u=t.SVG_TAG_NAMES_ADJUSTMENT_MAP={altglyph:"altGlyph",altglyphdef:"altGlyphDef",altglyphitem:"altGlyphItem",animatecolor:"animateColor",animatemotion:"animateMotion",animatetransform:"animateTransform",clippath:"clipPath",feblend:"feBlend",fecolormatrix:"feColorMatrix",fecomponenttransfer:"feComponentTransfer",fecomposite:"feComposite",feconvolvematrix:"feConvolveMatrix",fediffuselighting:"feDiffuseLighting",fedisplacementmap:"feDisplacementMap",fedistantlight:"feDistantLight",feflood:"feFlood",fefunca:"feFuncA",fefuncb:"feFuncB",fefuncg:"feFuncG",fefuncr:"feFuncR",fegaussianblur:"feGaussianBlur",feimage:"feImage",femerge:"feMerge",femergenode:"feMergeNode",femorphology:"feMorphology",feoffset:"feOffset",fepointlight:"fePointLight",fespecularlighting:"feSpecularLighting",fespotlight:"feSpotLight",fetile:"feTile",feturbulence:"feTurbulence",foreignobject:"foreignObject",glyphref:"glyphRef",lineargradient:"linearGradient",radialgradient:"radialGradient",textpath:"textPath"},p=Object.create(null);p[i.B]=!0,p[i.BIG]=!0,p[i.BLOCKQUOTE]=!0,p[i.BODY]=!0,p[i.BR]=!0,p[i.CENTER]=!0,p[i.CODE]=!0,p[i.DD]=!0,p[i.DIV]=!0,p[i.DL]=!0,p[i.DT]=!0,p[i.EM]=!0,p[i.EMBED]=!0,p[i.H1]=!0,p[i.H2]=!0,p[i.H3]=!0,p[i.H4]=!0,p[i.H5]=!0,p[i.H6]=!0,p[i.HEAD]=!0,p[i.HR]=!0,p[i.I]=!0,p[i.IMG]=!0,p[i.LI]=!0,p[i.LISTING]=!0,p[i.MENU]=!0,p[i.META]=!0,p[i.NOBR]=!0,p[i.OL]=!0,p[i.P]=!0,p[i.PRE]=!0,p[i.RUBY]=!0,p[i.S]=!0,p[i.SMALL]=!0,p[i.SPAN]=!0,p[i.STRONG]=!0,p[i.STRIKE]=!0,p[i.SUB]=!0,p[i.SUP]=!0,p[i.TABLE]=!0,p[i.TT]=!0,p[i.U]=!0,p[i.UL]=!0,p[i.VAR]=!0,t.causesExit=function(e){var t=e.tagName;return!(t!==i.FONT||null===r.getTokenAttr(e,a.COLOR)&&null===r.getTokenAttr(e,a.SIZE)&&null===r.getTokenAttr(e,a.FACE))||p[t]},t.adjustTokenMathMLAttrs=function(e){for(var t=0;t<e.attrs.length;t++)if("definitionurl"===e.attrs[t].name){e.attrs[t].name="definitionURL";break}},t.adjustTokenSVGAttrs=function(e){for(var t=0;t<e.attrs.length;t++){var n=c[e.attrs[t].name];n&&(e.attrs[t].name=n)}},t.adjustTokenXMLAttrs=function(e){for(var t=0;t<e.attrs.length;t++){var n=l[e.attrs[t].name];n&&(e.attrs[t].prefix=n.prefix,e.attrs[t].name=n.name,e.attrs[t].namespace=n.namespace)}},t.adjustTokenSVGTagName=function(e){var t=u[e.tagName];t&&(e.tagName=t)},t.isIntegrationPoint=function(e,t,n,r){return!(r&&r!==o.HTML||!function(e,t,n){if(t===o.MATHML&&e===i.ANNOTATION_XML)for(var r=0;r<n.length;r++)if(n[r].name===a.ENCODING){var s=n[r].value.toLowerCase();return"text/html"===s||"application/xhtml+xml"===s}return t===o.SVG&&(e===i.FOREIGN_OBJECT||e===i.DESC||e===i.TITLE)}(e,t,n))||!(r&&r!==o.MATHML||!function(e,t){return t===o.MATHML&&(e===i.MI||e===i.MO||e===i.MN||e===i.MS||e===i.MTEXT)}(e,t))}},6584:(e,t)=>{"use strict";var n=t.NAMESPACES={HTML:"http://www.w3.org/1999/xhtml",MATHML:"http://www.w3.org/1998/Math/MathML",SVG:"http://www.w3.org/2000/svg",XLINK:"http://www.w3.org/1999/xlink",XML:"http://www.w3.org/XML/1998/namespace",XMLNS:"http://www.w3.org/2000/xmlns/"};t.ATTRS={TYPE:"type",ACTION:"action",ENCODING:"encoding",PROMPT:"prompt",NAME:"name",COLOR:"color",FACE:"face",SIZE:"size"},t.DOCUMENT_MODE={NO_QUIRKS:"no-quirks",QUIRKS:"quirks",LIMITED_QUIRKS:"limited-quirks"};var r=t.TAG_NAMES={A:"a",ADDRESS:"address",ANNOTATION_XML:"annotation-xml",APPLET:"applet",AREA:"area",ARTICLE:"article",ASIDE:"aside",B:"b",BASE:"base",BASEFONT:"basefont",BGSOUND:"bgsound",BIG:"big",BLOCKQUOTE:"blockquote",BODY:"body",BR:"br",BUTTON:"button",CAPTION:"caption",CENTER:"center",CODE:"code",COL:"col",COLGROUP:"colgroup",DD:"dd",DESC:"desc",DETAILS:"details",DIALOG:"dialog",DIR:"dir",DIV:"div",DL:"dl",DT:"dt",EM:"em",EMBED:"embed",FIELDSET:"fieldset",FIGCAPTION:"figcaption",FIGURE:"figure",FONT:"font",FOOTER:"footer",FOREIGN_OBJECT:"foreignObject",FORM:"form",FRAME:"frame",FRAMESET:"frameset",H1:"h1",H2:"h2",H3:"h3",H4:"h4",H5:"h5",H6:"h6",HEAD:"head",HEADER:"header",HGROUP:"hgroup",HR:"hr",HTML:"html",I:"i",IMG:"img",IMAGE:"image",INPUT:"input",IFRAME:"iframe",KEYGEN:"keygen",LABEL:"label",LI:"li",LINK:"link",LISTING:"listing",MAIN:"main",MALIGNMARK:"malignmark",MARQUEE:"marquee",MATH:"math",MENU:"menu",MENUITEM:"menuitem",META:"meta",MGLYPH:"mglyph",MI:"mi",MO:"mo",MN:"mn",MS:"ms",MTEXT:"mtext",NAV:"nav",NOBR:"nobr",NOFRAMES:"noframes",NOEMBED:"noembed",NOSCRIPT:"noscript",OBJECT:"object",OL:"ol",OPTGROUP:"optgroup",OPTION:"option",P:"p",PARAM:"param",PLAINTEXT:"plaintext",PRE:"pre",RB:"rb",RP:"rp",RT:"rt",RTC:"rtc",RUBY:"ruby",S:"s",SCRIPT:"script",SECTION:"section",SELECT:"select",SOURCE:"source",SMALL:"small",SPAN:"span",STRIKE:"strike",STRONG:"strong",STYLE:"style",SUB:"sub",SUMMARY:"summary",SUP:"sup",TABLE:"table",TBODY:"tbody",TEMPLATE:"template",TEXTAREA:"textarea",TFOOT:"tfoot",TD:"td",TH:"th",THEAD:"thead",TITLE:"title",TR:"tr",TRACK:"track",TT:"tt",U:"u",UL:"ul",SVG:"svg",VAR:"var",WBR:"wbr",XMP:"xmp"},s=t.SPECIAL_ELEMENTS=Object.create(null);s[n.HTML]=Object.create(null),s[n.HTML][r.ADDRESS]=!0,s[n.HTML][r.APPLET]=!0,s[n.HTML][r.AREA]=!0,s[n.HTML][r.ARTICLE]=!0,s[n.HTML][r.ASIDE]=!0,s[n.HTML][r.BASE]=!0,s[n.HTML][r.BASEFONT]=!0,s[n.HTML][r.BGSOUND]=!0,s[n.HTML][r.BLOCKQUOTE]=!0,s[n.HTML][r.BODY]=!0,s[n.HTML][r.BR]=!0,s[n.HTML][r.BUTTON]=!0,s[n.HTML][r.CAPTION]=!0,s[n.HTML][r.CENTER]=!0,s[n.HTML][r.COL]=!0,s[n.HTML][r.COLGROUP]=!0,s[n.HTML][r.DD]=!0,s[n.HTML][r.DETAILS]=!0,s[n.HTML][r.DIR]=!0,s[n.HTML][r.DIV]=!0,s[n.HTML][r.DL]=!0,s[n.HTML][r.DT]=!0,s[n.HTML][r.EMBED]=!0,s[n.HTML][r.FIELDSET]=!0,s[n.HTML][r.FIGCAPTION]=!0,s[n.HTML][r.FIGURE]=!0,s[n.HTML][r.FOOTER]=!0,s[n.HTML][r.FORM]=!0,s[n.HTML][r.FRAME]=!0,s[n.HTML][r.FRAMESET]=!0,s[n.HTML][r.H1]=!0,s[n.HTML][r.H2]=!0,s[n.HTML][r.H3]=!0,s[n.HTML][r.H4]=!0,s[n.HTML][r.H5]=!0,s[n.HTML][r.H6]=!0,s[n.HTML][r.HEAD]=!0,s[n.HTML][r.HEADER]=!0,s[n.HTML][r.HGROUP]=!0,s[n.HTML][r.HR]=!0,s[n.HTML][r.HTML]=!0,s[n.HTML][r.IFRAME]=!0,s[n.HTML][r.IMG]=!0,s[n.HTML][r.INPUT]=!0,s[n.HTML][r.LI]=!0,s[n.HTML][r.LINK]=!0,s[n.HTML][r.LISTING]=!0,s[n.HTML][r.MAIN]=!0,s[n.HTML][r.MARQUEE]=!0,s[n.HTML][r.MENU]=!0,s[n.HTML][r.META]=!0,s[n.HTML][r.NAV]=!0,s[n.HTML][r.NOEMBED]=!0,s[n.HTML][r.NOFRAMES]=!0,s[n.HTML][r.NOSCRIPT]=!0,s[n.HTML][r.OBJECT]=!0,s[n.HTML][r.OL]=!0,s[n.HTML][r.P]=!0,s[n.HTML][r.PARAM]=!0,s[n.HTML][r.PLAINTEXT]=!0,s[n.HTML][r.PRE]=!0,s[n.HTML][r.SCRIPT]=!0,s[n.HTML][r.SECTION]=!0,s[n.HTML][r.SELECT]=!0,s[n.HTML][r.SOURCE]=!0,s[n.HTML][r.STYLE]=!0,s[n.HTML][r.SUMMARY]=!0,s[n.HTML][r.TABLE]=!0,s[n.HTML][r.TBODY]=!0,s[n.HTML][r.TD]=!0,s[n.HTML][r.TEMPLATE]=!0,s[n.HTML][r.TEXTAREA]=!0,s[n.HTML][r.TFOOT]=!0,s[n.HTML][r.TH]=!0,s[n.HTML][r.THEAD]=!0,s[n.HTML][r.TITLE]=!0,s[n.HTML][r.TR]=!0,s[n.HTML][r.TRACK]=!0,s[n.HTML][r.UL]=!0,s[n.HTML][r.WBR]=!0,s[n.HTML][r.XMP]=!0,s[n.MATHML]=Object.create(null),s[n.MATHML][r.MI]=!0,s[n.MATHML][r.MO]=!0,s[n.MATHML][r.MN]=!0,s[n.MATHML][r.MS]=!0,s[n.MATHML][r.MTEXT]=!0,s[n.MATHML][r.ANNOTATION_XML]=!0,s[n.SVG]=Object.create(null),s[n.SVG][r.TITLE]=!0,s[n.SVG][r.FOREIGN_OBJECT]=!0,s[n.SVG][r.DESC]=!0},5580:e=>{"use strict";e.exports=function(e,t){return[e,t=t||Object.create(null)].reduce((function(e,t){return Object.keys(t).forEach((function(n){e[n]=t[n]})),e}),Object.create(null))}},3306:(e,t)=>{"use strict";t.REPLACEMENT_CHARACTER="�",t.CODE_POINTS={EOF:-1,NULL:0,TABULATION:9,CARRIAGE_RETURN:13,LINE_FEED:10,FORM_FEED:12,SPACE:32,EXCLAMATION_MARK:33,QUOTATION_MARK:34,NUMBER_SIGN:35,AMPERSAND:38,APOSTROPHE:39,HYPHEN_MINUS:45,SOLIDUS:47,DIGIT_0:48,DIGIT_9:57,SEMICOLON:59,LESS_THAN_SIGN:60,EQUALS_SIGN:61,GREATER_THAN_SIGN:62,QUESTION_MARK:63,LATIN_CAPITAL_A:65,LATIN_CAPITAL_F:70,LATIN_CAPITAL_X:88,LATIN_CAPITAL_Z:90,GRAVE_ACCENT:96,LATIN_SMALL_A:97,LATIN_SMALL_F:102,LATIN_SMALL_X:120,LATIN_SMALL_Z:122,REPLACEMENT_CHARACTER:65533},t.CODE_POINT_SEQUENCES={DASH_DASH_STRING:[45,45],DOCTYPE_STRING:[68,79,67,84,89,80,69],CDATA_START_STRING:[91,67,68,65,84,65,91],CDATA_END_STRING:[93,93,62],SCRIPT_STRING:[115,99,114,105,112,116],PUBLIC_STRING:[80,85,66,76,73,67],SYSTEM_STRING:[83,89,83,84,69,77]}},63:(e,t,n)=>{"use strict";var r=n(3367),s=n(3272);t.parse=function(e,t){return new r(t).parse(e)},t.parseFragment=function(e,t,n){return"string"==typeof e&&(n=t,t=e,e=null),new r(n).parseFragment(t,e)},t.serialize=function(e,t){return new s(e,t).serialize()},t.treeAdapters={default:n(7988),htmlparser2:n(8537)},t.ParserStream=n(4603),t.PlainTextConversionStream=n(5707),t.SerializerStream=n(8261),t.SAXParser=n(8564)},4491:(e,t,n)=>{"use strict";var r=n(6371),s=n(7881),i=n(6584).TAG_NAMES;t.assign=function(e){var t=Object.getPrototypeOf(e),n=e.treeAdapter,o=null,a=null,c=null;function l(t,r){var i=t.__location;if(i)if(i.startTag||(i.startTag={line:i.line,col:i.col,startOffset:i.startOffset,endOffset:i.endOffset},i.attrs&&(i.startTag.attrs=i.attrs)),r.location){var o=r.location,a=n.getTagName(t),c=r.type===s.END_TAG_TOKEN&&a===r.tagName;c&&(i.endTag={line:o.line,col:o.col,startOffset:o.startOffset,endOffset:o.endOffset}),i.endOffset=c?o.endOffset:o.startOffset}else r.type===s.EOF_TOKEN&&(i.endOffset=e.tokenizer.preprocessor.sourcePos)}e._bootstrap=function(n,s){t._bootstrap.call(this,n,s),o=null,a=null,c=null,e.openElements.pop=function(){l(this.current,c),r.prototype.pop.call(this)},e.openElements.popAllUpToHtmlElement=function(){for(var e=this.stackTop;e>0;e--)l(this.items[e],c);r.prototype.popAllUpToHtmlElement.call(this)},e.openElements.remove=function(e){l(e,c),r.prototype.remove.call(this,e)}},e._runParsingLoop=function(n){t._runParsingLoop.call(this,n);for(var r=e.openElements.stackTop;r>=0;r--)l(e.openElements.items[r],c)},e._processTokenInForeignContent=function(e){c=e,t._processTokenInForeignContent.call(this,e)},e._processToken=function(e){if(c=e,t._processToken.call(this,e),e.type===s.END_TAG_TOKEN&&(e.tagName===i.HTML||e.tagName===i.BODY&&this.openElements.hasInScope(i.BODY)))for(var n=this.openElements.stackTop;n>=0;n--){var r=this.openElements.items[n];if(this.treeAdapter.getTagName(r)===e.tagName){l(r,e);break}}},e._setDocumentType=function(e){t._setDocumentType.call(this,e);for(var n=this.treeAdapter.getChildNodes(this.document),r=n.length,s=0;s<r;s++){var i=n[s];if(this.treeAdapter.isDocumentTypeNode(i)){i.__location=e.location;break}}},e._attachElementToTree=function(e){e.__location=o||null,o=null,t._attachElementToTree.call(this,e)},e._appendElement=function(e,n){o=e.location,t._appendElement.call(this,e,n)},e._insertElement=function(e,n){o=e.location,t._insertElement.call(this,e,n)},e._insertTemplate=function(e){o=e.location,t._insertTemplate.call(this,e),this.treeAdapter.getTemplateContent(this.openElements.current).__location=null},e._insertFakeRootElement=function(){t._insertFakeRootElement.call(this),this.openElements.current.__location=null},e._appendCommentNode=function(e,n){t._appendCommentNode.call(this,e,n);var r=this.treeAdapter.getChildNodes(n);r[r.length-1].__location=e.location},e._findFosterParentingLocation=function(){return a=t._findFosterParentingLocation.call(this)},e._insertCharacters=function(e){t._insertCharacters.call(this,e);var n=this._shouldFosterParentOnInsertion(),r=n&&a.parent||this.openElements.currentTmplContent||this.openElements.current,s=this.treeAdapter.getChildNodes(r),i=n&&a.beforeElement?s.indexOf(a.beforeElement)-1:s.length-1,o=s[i];o.__location?o.__location.endOffset=e.location.endOffset:o.__location=e.location}}},3555:(e,t,n)=>{"use strict";var r=n(3306).CODE_POINTS;t.assign=function(e){var t=Object.getPrototypeOf(e),n=-1,s=-1,i=1,o=!1,a=0,c=-1,l=1;function u(e){e.location={line:i,col:s,startOffset:n,endOffset:-1}}e._consume=function(){var e=t._consume.call(this);return o&&(o=!1,l++,a=this.preprocessor.sourcePos),e===r.LINE_FEED&&(o=!0),c=this.preprocessor.sourcePos-a+1,e},e._unconsume=function(){t._unconsume.call(this),o=!1,c=this.preprocessor.sourcePos-a+1},e._createStartTagToken=function(){t._createStartTagToken.call(this),u(this.currentToken)},e._createEndTagToken=function(){t._createEndTagToken.call(this),u(this.currentToken)},e._createCommentToken=function(){t._createCommentToken.call(this),u(this.currentToken)},e._createDoctypeToken=function(e){t._createDoctypeToken.call(this,e),u(this.currentToken)},e._createCharacterToken=function(e,n){t._createCharacterToken.call(this,e,n),u(this.currentCharacterToken)},e._createAttr=function(e){t._createAttr.call(this,e),this.currentAttrLocation={line:l,col:c,startOffset:this.preprocessor.sourcePos,endOffset:-1}},e._leaveAttrName=function(e){t._leaveAttrName.call(this,e),this._attachCurrentAttrLocationInfo()},e._leaveAttrValue=function(e){t._leaveAttrValue.call(this,e),this._attachCurrentAttrLocationInfo()},e._attachCurrentAttrLocationInfo=function(){this.currentAttrLocation.endOffset=this.preprocessor.sourcePos,this.currentToken.location.attrs||(this.currentToken.location.attrs=Object.create(null)),this.currentToken.location.attrs[this.currentAttr.name]=this.currentAttrLocation},e._emitCurrentToken=function(){this.currentCharacterToken&&(this.currentCharacterToken.location.endOffset=this.currentToken.location.startOffset),this.currentToken.location.endOffset=this.preprocessor.sourcePos+1,t._emitCurrentToken.call(this)},e._emitCurrentCharacterToken=function(){this.currentCharacterToken&&-1===this.currentCharacterToken.location.endOffset&&(this.currentCharacterToken.location.endOffset=this.preprocessor.sourcePos),t._emitCurrentCharacterToken.call(this)},Object.keys(t.MODE).map((function(e){return t.MODE[e]})).forEach((function(r){e[r]=function(e){n=this.preprocessor.sourcePos,i=l,s=c,t[r].call(this,e)}}))}},2850:e=>{"use strict";var t=e.exports=function(e){this.length=0,this.entries=[],this.treeAdapter=e,this.bookmark=null};t.MARKER_ENTRY="MARKER_ENTRY",t.ELEMENT_ENTRY="ELEMENT_ENTRY",t.prototype._getNoahArkConditionCandidates=function(e){var n=[];if(this.length>=3)for(var r=this.treeAdapter.getAttrList(e).length,s=this.treeAdapter.getTagName(e),i=this.treeAdapter.getNamespaceURI(e),o=this.length-1;o>=0;o--){var a=this.entries[o];if(a.type===t.MARKER_ENTRY)break;var c=a.element,l=this.treeAdapter.getAttrList(c);this.treeAdapter.getTagName(c)===s&&this.treeAdapter.getNamespaceURI(c)===i&&l.length===r&&n.push({idx:o,attrs:l})}return n.length<3?[]:n},t.prototype._ensureNoahArkCondition=function(e){var t=this._getNoahArkConditionCandidates(e),n=t.length;if(n){for(var r=this.treeAdapter.getAttrList(e),s=r.length,i=Object.create(null),o=0;o<s;o++){var a=r[o];i[a.name]=a.value}for(o=0;o<s;o++)for(var c=0;c<n;c++){var l=t[c].attrs[o];if(i[l.name]!==l.value&&(t.splice(c,1),n--),t.length<3)return}for(o=n-1;o>=2;o--)this.entries.splice(t[o].idx,1),this.length--}},t.prototype.insertMarker=function(){this.entries.push({type:t.MARKER_ENTRY}),this.length++},t.prototype.pushElement=function(e,n){this._ensureNoahArkCondition(e),this.entries.push({type:t.ELEMENT_ENTRY,element:e,token:n}),this.length++},t.prototype.insertElementAfterBookmark=function(e,n){for(var r=this.length-1;r>=0&&this.entries[r]!==this.bookmark;r--);this.entries.splice(r+1,0,{type:t.ELEMENT_ENTRY,element:e,token:n}),this.length++},t.prototype.removeEntry=function(e){for(var t=this.length-1;t>=0;t--)if(this.entries[t]===e){this.entries.splice(t,1),this.length--;break}},t.prototype.clearToLastMarker=function(){for(;this.length;){var e=this.entries.pop();if(this.length--,e.type===t.MARKER_ENTRY)break}},t.prototype.getElementEntryInScopeWithTagName=function(e){for(var n=this.length-1;n>=0;n--){var r=this.entries[n];if(r.type===t.MARKER_ENTRY)return null;if(this.treeAdapter.getTagName(r.element)===e)return r}return null},t.prototype.getElementEntry=function(e){for(var n=this.length-1;n>=0;n--){var r=this.entries[n];if(r.type===t.ELEMENT_ENTRY&&r.element===e)return r}return null}},3367:(e,t,n)=>{"use strict";var r=n(7881),s=n(6371),i=n(2850),o=n(4491),a=n(7988),c=n(135),l=n(1723),u=n(5580),p=n(3306),h=n(6584),f=h.TAG_NAMES,T=h.NAMESPACES,d=h.ATTRS,m={locationInfo:!1,treeAdapter:a},E="hidden",_=8,g=3,A="INITIAL_MODE",C="BEFORE_HTML_MODE",N="BEFORE_HEAD_MODE",k="IN_HEAD_MODE",O="AFTER_HEAD_MODE",S="IN_BODY_MODE",L="TEXT_MODE",v="IN_TABLE_MODE",b="IN_TABLE_TEXT_MODE",R="IN_CAPTION_MODE",I="IN_COLUMN_GROUP_MODE",M="IN_TABLE_BODY_MODE",y="IN_ROW_MODE",D="IN_CELL_MODE",P="IN_SELECT_MODE",x="IN_SELECT_IN_TABLE_MODE",H="IN_TEMPLATE_MODE",w="AFTER_BODY_MODE",F="IN_FRAMESET_MODE",U="AFTER_FRAMESET_MODE",B="AFTER_AFTER_BODY_MODE",G="AFTER_AFTER_FRAMESET_MODE",K=Object.create(null);K[f.TR]=y,K[f.TBODY]=K[f.THEAD]=K[f.TFOOT]=M,K[f.CAPTION]=R,K[f.COLGROUP]=I,K[f.TABLE]=v,K[f.BODY]=S,K[f.FRAMESET]=F;var q=Object.create(null);q[f.CAPTION]=q[f.COLGROUP]=q[f.TBODY]=q[f.TFOOT]=q[f.THEAD]=v,q[f.COL]=I,q[f.TR]=M,q[f.TD]=q[f.TH]=y;var z=Object.create(null);z[A]=Object.create(null),z[A][r.CHARACTER_TOKEN]=z[A][r.NULL_CHARACTER_TOKEN]=se,z[A][r.WHITESPACE_CHARACTER_TOKEN]=J,z[A][r.COMMENT_TOKEN]=ee,z[A][r.DOCTYPE_TOKEN]=function(e,t){e._setDocumentType(t);var n=t.forceQuirks?h.DOCUMENT_MODE.QUIRKS:c.getDocumentMode(t.name,t.publicId,t.systemId);e.treeAdapter.setDocumentMode(e.document,n),e.insertionMode=C},z[A][r.START_TAG_TOKEN]=z[A][r.END_TAG_TOKEN]=z[A][r.EOF_TOKEN]=se,z[C]=Object.create(null),z[C][r.CHARACTER_TOKEN]=z[C][r.NULL_CHARACTER_TOKEN]=ie,z[C][r.WHITESPACE_CHARACTER_TOKEN]=J,z[C][r.COMMENT_TOKEN]=ee,z[C][r.DOCTYPE_TOKEN]=J,z[C][r.START_TAG_TOKEN]=function(e,t){t.tagName===f.HTML?(e._insertElement(t,T.HTML),e.insertionMode=N):ie(e,t)},z[C][r.END_TAG_TOKEN]=function(e,t){var n=t.tagName;n!==f.HTML&&n!==f.HEAD&&n!==f.BODY&&n!==f.BR||ie(e,t)},z[C][r.EOF_TOKEN]=ie,z[N]=Object.create(null),z[N][r.CHARACTER_TOKEN]=z[N][r.NULL_CHARACTER_TOKEN]=oe,z[N][r.WHITESPACE_CHARACTER_TOKEN]=J,z[N][r.COMMENT_TOKEN]=ee,z[N][r.DOCTYPE_TOKEN]=J,z[N][r.START_TAG_TOKEN]=function(e,t){var n=t.tagName;n===f.HTML?ke(e,t):n===f.HEAD?(e._insertElement(t,T.HTML),e.headElement=e.openElements.current,e.insertionMode=k):oe(e,t)},z[N][r.END_TAG_TOKEN]=function(e,t){var n=t.tagName;n!==f.HEAD&&n!==f.BODY&&n!==f.HTML&&n!==f.BR||oe(e,t)},z[N][r.EOF_TOKEN]=oe,z[k]=Object.create(null),z[k][r.CHARACTER_TOKEN]=z[k][r.NULL_CHARACTER_TOKEN]=le,z[k][r.WHITESPACE_CHARACTER_TOKEN]=ne,z[k][r.COMMENT_TOKEN]=ee,z[k][r.DOCTYPE_TOKEN]=J,z[k][r.START_TAG_TOKEN]=ae,z[k][r.END_TAG_TOKEN]=ce,z[k][r.EOF_TOKEN]=le,z[O]=Object.create(null),z[O][r.CHARACTER_TOKEN]=z[O][r.NULL_CHARACTER_TOKEN]=ue,z[O][r.WHITESPACE_CHARACTER_TOKEN]=ne,z[O][r.COMMENT_TOKEN]=ee,z[O][r.DOCTYPE_TOKEN]=J,z[O][r.START_TAG_TOKEN]=function(e,t){var n=t.tagName;n===f.HTML?ke(e,t):n===f.BODY?(e._insertElement(t,T.HTML),e.framesetOk=!1,e.insertionMode=S):n===f.FRAMESET?(e._insertElement(t,T.HTML),e.insertionMode=F):n===f.BASE||n===f.BASEFONT||n===f.BGSOUND||n===f.LINK||n===f.META||n===f.NOFRAMES||n===f.SCRIPT||n===f.STYLE||n===f.TEMPLATE||n===f.TITLE?(e.openElements.push(e.headElement),ae(e,t),e.openElements.remove(e.headElement)):n!==f.HEAD&&ue(e,t)},z[O][r.END_TAG_TOKEN]=function(e,t){var n=t.tagName;n===f.BODY||n===f.HTML||n===f.BR?ue(e,t):n===f.TEMPLATE&&ce(e,t)},z[O][r.EOF_TOKEN]=ue,z[S]=Object.create(null),z[S][r.CHARACTER_TOKEN]=he,z[S][r.NULL_CHARACTER_TOKEN]=J,z[S][r.WHITESPACE_CHARACTER_TOKEN]=pe,z[S][r.COMMENT_TOKEN]=ee,z[S][r.DOCTYPE_TOKEN]=J,z[S][r.START_TAG_TOKEN]=ke,z[S][r.END_TAG_TOKEN]=ve,z[S][r.EOF_TOKEN]=be,z[L]=Object.create(null),z[L][r.CHARACTER_TOKEN]=z[L][r.NULL_CHARACTER_TOKEN]=z[L][r.WHITESPACE_CHARACTER_TOKEN]=ne,z[L][r.COMMENT_TOKEN]=z[L][r.DOCTYPE_TOKEN]=z[L][r.START_TAG_TOKEN]=J,z[L][r.END_TAG_TOKEN]=function(e,t){t.tagName===f.SCRIPT&&(e.pendingScript=e.openElements.current),e.openElements.pop(),e.insertionMode=e.originalInsertionMode},z[L][r.EOF_TOKEN]=function(e,t){e.openElements.pop(),e.insertionMode=e.originalInsertionMode,e._processToken(t)},z[v]=Object.create(null),z[v][r.CHARACTER_TOKEN]=z[v][r.NULL_CHARACTER_TOKEN]=z[v][r.WHITESPACE_CHARACTER_TOKEN]=Re,z[v][r.COMMENT_TOKEN]=ee,z[v][r.DOCTYPE_TOKEN]=J,z[v][r.START_TAG_TOKEN]=Ie,z[v][r.END_TAG_TOKEN]=Me,z[v][r.EOF_TOKEN]=be,z[b]=Object.create(null),z[b][r.CHARACTER_TOKEN]=function(e,t){e.pendingCharacterTokens.push(t),e.hasNonWhitespacePendingCharacterToken=!0},z[b][r.NULL_CHARACTER_TOKEN]=J,z[b][r.WHITESPACE_CHARACTER_TOKEN]=function(e,t){e.pendingCharacterTokens.push(t)},z[b][r.COMMENT_TOKEN]=z[b][r.DOCTYPE_TOKEN]=z[b][r.START_TAG_TOKEN]=z[b][r.END_TAG_TOKEN]=z[b][r.EOF_TOKEN]=function(e,t){var n=0;if(e.hasNonWhitespacePendingCharacterToken)for(;n<e.pendingCharacterTokens.length;n++)ye(e,e.pendingCharacterTokens[n]);else for(;n<e.pendingCharacterTokens.length;n++)e._insertCharacters(e.pendingCharacterTokens[n]);e.insertionMode=e.originalInsertionMode,e._processToken(t)},z[R]=Object.create(null),z[R][r.CHARACTER_TOKEN]=he,z[R][r.NULL_CHARACTER_TOKEN]=J,z[R][r.WHITESPACE_CHARACTER_TOKEN]=pe,z[R][r.COMMENT_TOKEN]=ee,z[R][r.DOCTYPE_TOKEN]=J,z[R][r.START_TAG_TOKEN]=function(e,t){var n=t.tagName;n===f.CAPTION||n===f.COL||n===f.COLGROUP||n===f.TBODY||n===f.TD||n===f.TFOOT||n===f.TH||n===f.THEAD||n===f.TR?e.openElements.hasInTableScope(f.CAPTION)&&(e.openElements.generateImpliedEndTags(),e.openElements.popUntilTagNamePopped(f.CAPTION),e.activeFormattingElements.clearToLastMarker(),e.insertionMode=v,e._processToken(t)):ke(e,t)},z[R][r.END_TAG_TOKEN]=function(e,t){var n=t.tagName;n===f.CAPTION||n===f.TABLE?e.openElements.hasInTableScope(f.CAPTION)&&(e.openElements.generateImpliedEndTags(),e.openElements.popUntilTagNamePopped(f.CAPTION),e.activeFormattingElements.clearToLastMarker(),e.insertionMode=v,n===f.TABLE&&e._processToken(t)):n!==f.BODY&&n!==f.COL&&n!==f.COLGROUP&&n!==f.HTML&&n!==f.TBODY&&n!==f.TD&&n!==f.TFOOT&&n!==f.TH&&n!==f.THEAD&&n!==f.TR&&ve(e,t)},z[R][r.EOF_TOKEN]=be,z[I]=Object.create(null),z[I][r.CHARACTER_TOKEN]=z[I][r.NULL_CHARACTER_TOKEN]=De,z[I][r.WHITESPACE_CHARACTER_TOKEN]=ne,z[I][r.COMMENT_TOKEN]=ee,z[I][r.DOCTYPE_TOKEN]=J,z[I][r.START_TAG_TOKEN]=function(e,t){var n=t.tagName;n===f.HTML?ke(e,t):n===f.COL?e._appendElement(t,T.HTML):n===f.TEMPLATE?ae(e,t):De(e,t)},z[I][r.END_TAG_TOKEN]=function(e,t){var n=t.tagName;n===f.COLGROUP?e.openElements.currentTagName===f.COLGROUP&&(e.openElements.pop(),e.insertionMode=v):n===f.TEMPLATE?ce(e,t):n!==f.COL&&De(e,t)},z[I][r.EOF_TOKEN]=be,z[M]=Object.create(null),z[M][r.CHARACTER_TOKEN]=z[M][r.NULL_CHARACTER_TOKEN]=z[M][r.WHITESPACE_CHARACTER_TOKEN]=Re,z[M][r.COMMENT_TOKEN]=ee,z[M][r.DOCTYPE_TOKEN]=J,z[M][r.START_TAG_TOKEN]=function(e,t){var n=t.tagName;n===f.TR?(e.openElements.clearBackToTableBodyContext(),e._insertElement(t,T.HTML),e.insertionMode=y):n===f.TH||n===f.TD?(e.openElements.clearBackToTableBodyContext(),e._insertFakeElement(f.TR),e.insertionMode=y,e._processToken(t)):n===f.CAPTION||n===f.COL||n===f.COLGROUP||n===f.TBODY||n===f.TFOOT||n===f.THEAD?e.openElements.hasTableBodyContextInTableScope()&&(e.openElements.clearBackToTableBodyContext(),e.openElements.pop(),e.insertionMode=v,e._processToken(t)):Ie(e,t)},z[M][r.END_TAG_TOKEN]=function(e,t){var n=t.tagName;n===f.TBODY||n===f.TFOOT||n===f.THEAD?e.openElements.hasInTableScope(n)&&(e.openElements.clearBackToTableBodyContext(),e.openElements.pop(),e.insertionMode=v):n===f.TABLE?e.openElements.hasTableBodyContextInTableScope()&&(e.openElements.clearBackToTableBodyContext(),e.openElements.pop(),e.insertionMode=v,e._processToken(t)):(n!==f.BODY&&n!==f.CAPTION&&n!==f.COL&&n!==f.COLGROUP||n!==f.HTML&&n!==f.TD&&n!==f.TH&&n!==f.TR)&&Me(e,t)},z[M][r.EOF_TOKEN]=be,z[y]=Object.create(null),z[y][r.CHARACTER_TOKEN]=z[y][r.NULL_CHARACTER_TOKEN]=z[y][r.WHITESPACE_CHARACTER_TOKEN]=Re,z[y][r.COMMENT_TOKEN]=ee,z[y][r.DOCTYPE_TOKEN]=J,z[y][r.START_TAG_TOKEN]=function(e,t){var n=t.tagName;n===f.TH||n===f.TD?(e.openElements.clearBackToTableRowContext(),e._insertElement(t,T.HTML),e.insertionMode=D,e.activeFormattingElements.insertMarker()):n===f.CAPTION||n===f.COL||n===f.COLGROUP||n===f.TBODY||n===f.TFOOT||n===f.THEAD||n===f.TR?e.openElements.hasInTableScope(f.TR)&&(e.openElements.clearBackToTableRowContext(),e.openElements.pop(),e.insertionMode=M,e._processToken(t)):Ie(e,t)},z[y][r.END_TAG_TOKEN]=function(e,t){var n=t.tagName;n===f.TR?e.openElements.hasInTableScope(f.TR)&&(e.openElements.clearBackToTableRowContext(),e.openElements.pop(),e.insertionMode=M):n===f.TABLE?e.openElements.hasInTableScope(f.TR)&&(e.openElements.clearBackToTableRowContext(),e.openElements.pop(),e.insertionMode=M,e._processToken(t)):n===f.TBODY||n===f.TFOOT||n===f.THEAD?(e.openElements.hasInTableScope(n)||e.openElements.hasInTableScope(f.TR))&&(e.openElements.clearBackToTableRowContext(),e.openElements.pop(),e.insertionMode=M,e._processToken(t)):(n!==f.BODY&&n!==f.CAPTION&&n!==f.COL&&n!==f.COLGROUP||n!==f.HTML&&n!==f.TD&&n!==f.TH)&&Me(e,t)},z[y][r.EOF_TOKEN]=be,z[D]=Object.create(null),z[D][r.CHARACTER_TOKEN]=he,z[D][r.NULL_CHARACTER_TOKEN]=J,z[D][r.WHITESPACE_CHARACTER_TOKEN]=pe,z[D][r.COMMENT_TOKEN]=ee,z[D][r.DOCTYPE_TOKEN]=J,z[D][r.START_TAG_TOKEN]=function(e,t){var n=t.tagName;n===f.CAPTION||n===f.COL||n===f.COLGROUP||n===f.TBODY||n===f.TD||n===f.TFOOT||n===f.TH||n===f.THEAD||n===f.TR?(e.openElements.hasInTableScope(f.TD)||e.openElements.hasInTableScope(f.TH))&&(e._closeTableCell(),e._processToken(t)):ke(e,t)},z[D][r.END_TAG_TOKEN]=function(e,t){var n=t.tagName;n===f.TD||n===f.TH?e.openElements.hasInTableScope(n)&&(e.openElements.generateImpliedEndTags(),e.openElements.popUntilTagNamePopped(n),e.activeFormattingElements.clearToLastMarker(),e.insertionMode=y):n===f.TABLE||n===f.TBODY||n===f.TFOOT||n===f.THEAD||n===f.TR?e.openElements.hasInTableScope(n)&&(e._closeTableCell(),e._processToken(t)):n!==f.BODY&&n!==f.CAPTION&&n!==f.COL&&n!==f.COLGROUP&&n!==f.HTML&&ve(e,t)},z[D][r.EOF_TOKEN]=be,z[P]=Object.create(null),z[P][r.CHARACTER_TOKEN]=ne,z[P][r.NULL_CHARACTER_TOKEN]=J,z[P][r.WHITESPACE_CHARACTER_TOKEN]=ne,z[P][r.COMMENT_TOKEN]=ee,z[P][r.DOCTYPE_TOKEN]=J,z[P][r.START_TAG_TOKEN]=Pe,z[P][r.END_TAG_TOKEN]=xe,z[P][r.EOF_TOKEN]=be,z[x]=Object.create(null),z[x][r.CHARACTER_TOKEN]=ne,z[x][r.NULL_CHARACTER_TOKEN]=J,z[x][r.WHITESPACE_CHARACTER_TOKEN]=ne,z[x][r.COMMENT_TOKEN]=ee,z[x][r.DOCTYPE_TOKEN]=J,z[x][r.START_TAG_TOKEN]=function(e,t){var n=t.tagName;n===f.CAPTION||n===f.TABLE||n===f.TBODY||n===f.TFOOT||n===f.THEAD||n===f.TR||n===f.TD||n===f.TH?(e.openElements.popUntilTagNamePopped(f.SELECT),e._resetInsertionMode(),e._processToken(t)):Pe(e,t)},z[x][r.END_TAG_TOKEN]=function(e,t){var n=t.tagName;n===f.CAPTION||n===f.TABLE||n===f.TBODY||n===f.TFOOT||n===f.THEAD||n===f.TR||n===f.TD||n===f.TH?e.openElements.hasInTableScope(n)&&(e.openElements.popUntilTagNamePopped(f.SELECT),e._resetInsertionMode(),e._processToken(t)):xe(e,t)},z[x][r.EOF_TOKEN]=be,z[H]=Object.create(null),z[H][r.CHARACTER_TOKEN]=he,z[H][r.NULL_CHARACTER_TOKEN]=J,z[H][r.WHITESPACE_CHARACTER_TOKEN]=pe,z[H][r.COMMENT_TOKEN]=ee,z[H][r.DOCTYPE_TOKEN]=J,z[H][r.START_TAG_TOKEN]=function(e,t){var n=t.tagName;if(n===f.BASE||n===f.BASEFONT||n===f.BGSOUND||n===f.LINK||n===f.META||n===f.NOFRAMES||n===f.SCRIPT||n===f.STYLE||n===f.TEMPLATE||n===f.TITLE)ae(e,t);else{var r=q[n]||S;e._popTmplInsertionMode(),e._pushTmplInsertionMode(r),e.insertionMode=r,e._processToken(t)}},z[H][r.END_TAG_TOKEN]=function(e,t){t.tagName===f.TEMPLATE&&ce(e,t)},z[H][r.EOF_TOKEN]=He,z[w]=Object.create(null),z[w][r.CHARACTER_TOKEN]=z[w][r.NULL_CHARACTER_TOKEN]=we,z[w][r.WHITESPACE_CHARACTER_TOKEN]=pe,z[w][r.COMMENT_TOKEN]=function(e,t){e._appendCommentNode(t,e.openElements.items[0])},z[w][r.DOCTYPE_TOKEN]=J,z[w][r.START_TAG_TOKEN]=function(e,t){t.tagName===f.HTML?ke(e,t):we(e,t)},z[w][r.END_TAG_TOKEN]=function(e,t){t.tagName===f.HTML?e.fragmentContext||(e.insertionMode=B):we(e,t)},z[w][r.EOF_TOKEN]=re,z[F]=Object.create(null),z[F][r.CHARACTER_TOKEN]=z[F][r.NULL_CHARACTER_TOKEN]=J,z[F][r.WHITESPACE_CHARACTER_TOKEN]=ne,z[F][r.COMMENT_TOKEN]=ee,z[F][r.DOCTYPE_TOKEN]=J,z[F][r.START_TAG_TOKEN]=function(e,t){var n=t.tagName;n===f.HTML?ke(e,t):n===f.FRAMESET?e._insertElement(t,T.HTML):n===f.FRAME?e._appendElement(t,T.HTML):n===f.NOFRAMES&&ae(e,t)},z[F][r.END_TAG_TOKEN]=function(e,t){t.tagName!==f.FRAMESET||e.openElements.isRootHtmlElementCurrent()||(e.openElements.pop(),e.fragmentContext||e.openElements.currentTagName===f.FRAMESET||(e.insertionMode=U))},z[F][r.EOF_TOKEN]=re,z[U]=Object.create(null),z[U][r.CHARACTER_TOKEN]=z[U][r.NULL_CHARACTER_TOKEN]=J,z[U][r.WHITESPACE_CHARACTER_TOKEN]=ne,z[U][r.COMMENT_TOKEN]=ee,z[U][r.DOCTYPE_TOKEN]=J,z[U][r.START_TAG_TOKEN]=function(e,t){var n=t.tagName;n===f.HTML?ke(e,t):n===f.NOFRAMES&&ae(e,t)},z[U][r.END_TAG_TOKEN]=function(e,t){t.tagName===f.HTML&&(e.insertionMode=G)},z[U][r.EOF_TOKEN]=re,z[B]=Object.create(null),z[B][r.CHARACTER_TOKEN]=Fe,z[B][r.NULL_CHARACTER_TOKEN]=Fe,z[B][r.WHITESPACE_CHARACTER_TOKEN]=pe,z[B][r.COMMENT_TOKEN]=te,z[B][r.DOCTYPE_TOKEN]=J,z[B][r.START_TAG_TOKEN]=function(e,t){t.tagName===f.HTML?ke(e,t):Fe(e,t)},z[B][r.END_TAG_TOKEN]=Fe,z[B][r.EOF_TOKEN]=re,z[G]=Object.create(null),z[G][r.CHARACTER_TOKEN]=z[G][r.NULL_CHARACTER_TOKEN]=J,z[G][r.WHITESPACE_CHARACTER_TOKEN]=pe,z[G][r.COMMENT_TOKEN]=te,z[G][r.DOCTYPE_TOKEN]=J,z[G][r.START_TAG_TOKEN]=function(e,t){var n=t.tagName;n===f.HTML?ke(e,t):n===f.NOFRAMES&&ae(e,t)},z[G][r.END_TAG_TOKEN]=J,z[G][r.EOF_TOKEN]=re;var j=e.exports=function(e){this.options=u(m,e),this.treeAdapter=this.options.treeAdapter,this.pendingScript=null,this.options.locationInfo&&o.assign(this)};function Y(e,t){var n=e.activeFormattingElements.getElementEntryInScopeWithTagName(t.tagName);return n?e.openElements.contains(n.element)?e.openElements.hasInScope(t.tagName)||(n=null):(e.activeFormattingElements.removeEntry(n),n=null):Le(e,t),n}function V(e,t){for(var n=null,r=e.openElements.stackTop;r>=0;r--){var s=e.openElements.items[r];if(s===t.element)break;e._isSpecialElement(s)&&(n=s)}return n||(e.openElements.popUntilElementPopped(t.element),e.activeFormattingElements.removeEntry(t)),n}function W(e,t,n){for(var r=t,s=e.openElements.getCommonAncestor(t),i=0,o=s;o!==n;i++,o=s){s=e.openElements.getCommonAncestor(o);var a=e.activeFormattingElements.getElementEntry(o),c=a&&i>=g;!a||c?(c&&e.activeFormattingElements.removeEntry(a),e.openElements.remove(o)):(o=Q(e,a),r===t&&(e.activeFormattingElements.bookmark=a),e.treeAdapter.detachNode(r),e.treeAdapter.appendChild(o,r),r=o)}return r}function Q(e,t){var n=e.treeAdapter.getNamespaceURI(t.element),r=e.treeAdapter.createElement(t.token.tagName,n,t.token.attrs);return e.openElements.replace(t.element,r),t.element=r,r}function X(e,t,n){if(e._isElementCausesFosterParenting(t))e._fosterParentElement(n);else{var r=e.treeAdapter.getTagName(t),s=e.treeAdapter.getNamespaceURI(t);r===f.TEMPLATE&&s===T.HTML&&(t=e.treeAdapter.getTemplateContent(t)),e.treeAdapter.appendChild(t,n)}}function $(e,t,n){var r=e.treeAdapter.getNamespaceURI(n.element),s=n.token,i=e.treeAdapter.createElement(s.tagName,r,s.attrs);e._adoptNodes(t,i),e.treeAdapter.appendChild(t,i),e.activeFormattingElements.insertElementAfterBookmark(i,n.token),e.activeFormattingElements.removeEntry(n),e.openElements.remove(n.element),e.openElements.insertAfter(t,i)}function Z(e,t){for(var n,r=0;r<_&&(n=Y(e,t));r++){var s=V(e,n);if(!s)break;e.activeFormattingElements.bookmark=n;var i=W(e,s,n.element),o=e.openElements.getCommonAncestor(n.element);e.treeAdapter.detachNode(i),X(e,o,i),$(e,s,n)}}function J(){}function ee(e,t){e._appendCommentNode(t,e.openElements.currentTmplContent||e.openElements.current)}function te(e,t){e._appendCommentNode(t,e.document)}function ne(e,t){e._insertCharacters(t)}function re(e){e.stopped=!0}function se(e,t){e.treeAdapter.setDocumentMode(e.document,h.DOCUMENT_MODE.QUIRKS),e.insertionMode=C,e._processToken(t)}function ie(e,t){e._insertFakeRootElement(),e.insertionMode=N,e._processToken(t)}function oe(e,t){e._insertFakeElement(f.HEAD),e.headElement=e.openElements.current,e.insertionMode=k,e._processToken(t)}function ae(e,t){var n=t.tagName;n===f.HTML?ke(e,t):n===f.BASE||n===f.BASEFONT||n===f.BGSOUND||n===f.LINK||n===f.META?e._appendElement(t,T.HTML):n===f.TITLE?e._switchToTextParsing(t,r.MODE.RCDATA):n===f.NOSCRIPT||n===f.NOFRAMES||n===f.STYLE?e._switchToTextParsing(t,r.MODE.RAWTEXT):n===f.SCRIPT?e._switchToTextParsing(t,r.MODE.SCRIPT_DATA):n===f.TEMPLATE?(e._insertTemplate(t,T.HTML),e.activeFormattingElements.insertMarker(),e.framesetOk=!1,e.insertionMode=H,e._pushTmplInsertionMode(H)):n!==f.HEAD&&le(e,t)}function ce(e,t){var n=t.tagName;n===f.HEAD?(e.openElements.pop(),e.insertionMode=O):n===f.BODY||n===f.BR||n===f.HTML?le(e,t):n===f.TEMPLATE&&e.openElements.tmplCount>0&&(e.openElements.generateImpliedEndTags(),e.openElements.popUntilTagNamePopped(f.TEMPLATE),e.activeFormattingElements.clearToLastMarker(),e._popTmplInsertionMode(),e._resetInsertionMode())}function le(e,t){e.openElements.pop(),e.insertionMode=O,e._processToken(t)}function ue(e,t){e._insertFakeElement(f.BODY),e.insertionMode=S,e._processToken(t)}function pe(e,t){e._reconstructActiveFormattingElements(),e._insertCharacters(t)}function he(e,t){e._reconstructActiveFormattingElements(),e._insertCharacters(t),e.framesetOk=!1}function fe(e,t){e.openElements.hasInButtonScope(f.P)&&e._closePElement(),e._insertElement(t,T.HTML)}function Te(e,t){e.openElements.hasInButtonScope(f.P)&&e._closePElement(),e._insertElement(t,T.HTML),e.skipNextNewLine=!0,e.framesetOk=!1}function de(e,t){e._reconstructActiveFormattingElements(),e._insertElement(t,T.HTML),e.activeFormattingElements.pushElement(e.openElements.current,t)}function me(e,t){e._reconstructActiveFormattingElements(),e._insertElement(t,T.HTML),e.activeFormattingElements.insertMarker(),e.framesetOk=!1}function Ee(e,t){e._reconstructActiveFormattingElements(),e._appendElement(t,T.HTML),e.framesetOk=!1}function _e(e,t){e._appendElement(t,T.HTML)}function ge(e,t){e._switchToTextParsing(t,r.MODE.RAWTEXT)}function Ae(e,t){e.openElements.currentTagName===f.OPTION&&e.openElements.pop(),e._reconstructActiveFormattingElements(),e._insertElement(t,T.HTML)}function Ce(e,t){e.openElements.hasInScope(f.RUBY)&&e.openElements.generateImpliedEndTags(),e._insertElement(t,T.HTML)}function Ne(e,t){e._reconstructActiveFormattingElements(),e._insertElement(t,T.HTML)}function ke(e,t){var n=t.tagName;switch(n.length){case 1:n===f.I||n===f.S||n===f.B||n===f.U?de(e,t):n===f.P?fe(e,t):n===f.A?function(e,t){var n=e.activeFormattingElements.getElementEntryInScopeWithTagName(f.A);n&&(Z(e,t),e.openElements.remove(n.element),e.activeFormattingElements.removeEntry(n)),e._reconstructActiveFormattingElements(),e._insertElement(t,T.HTML),e.activeFormattingElements.pushElement(e.openElements.current,t)}(e,t):Ne(e,t);break;case 2:n===f.DL||n===f.OL||n===f.UL?fe(e,t):n===f.H1||n===f.H2||n===f.H3||n===f.H4||n===f.H5||n===f.H6?function(e,t){e.openElements.hasInButtonScope(f.P)&&e._closePElement();var n=e.openElements.currentTagName;n!==f.H1&&n!==f.H2&&n!==f.H3&&n!==f.H4&&n!==f.H5&&n!==f.H6||e.openElements.pop(),e._insertElement(t,T.HTML)}(e,t):n===f.LI||n===f.DD||n===f.DT?function(e,t){e.framesetOk=!1;for(var n=t.tagName,r=e.openElements.stackTop;r>=0;r--){var s=e.openElements.items[r],i=e.treeAdapter.getTagName(s),o=null;if(n===f.LI&&i===f.LI?o=f.LI:n!==f.DD&&n!==f.DT||i!==f.DD&&i!==f.DT||(o=i),o){e.openElements.generateImpliedEndTagsWithExclusion(o),e.openElements.popUntilTagNamePopped(o);break}if(i!==f.ADDRESS&&i!==f.DIV&&i!==f.P&&e._isSpecialElement(s))break}e.openElements.hasInButtonScope(f.P)&&e._closePElement(),e._insertElement(t,T.HTML)}(e,t):n===f.EM||n===f.TT?de(e,t):n===f.BR?Ee(e,t):n===f.HR?function(e,t){e.openElements.hasInButtonScope(f.P)&&e._closePElement(),e.openElements.currentTagName===f.MENUITEM&&e.openElements.pop(),e._appendElement(t,T.HTML),e.framesetOk=!1}(e,t):n===f.RB?Ce(e,t):n===f.RT||n===f.RP?function(e,t){e.openElements.hasInScope(f.RUBY)&&e.openElements.generateImpliedEndTagsWithExclusion(f.RTC),e._insertElement(t,T.HTML)}(e,t):n!==f.TH&&n!==f.TD&&n!==f.TR&&Ne(e,t);break;case 3:n===f.DIV||n===f.DIR||n===f.NAV?fe(e,t):n===f.PRE?Te(e,t):n===f.BIG?de(e,t):n===f.IMG||n===f.WBR?Ee(e,t):n===f.XMP?function(e,t){e.openElements.hasInButtonScope(f.P)&&e._closePElement(),e._reconstructActiveFormattingElements(),e.framesetOk=!1,e._switchToTextParsing(t,r.MODE.RAWTEXT)}(e,t):n===f.SVG?function(e,t){e._reconstructActiveFormattingElements(),l.adjustTokenSVGAttrs(t),l.adjustTokenXMLAttrs(t),t.selfClosing?e._appendElement(t,T.SVG):e._insertElement(t,T.SVG)}(e,t):n===f.RTC?Ce(e,t):n!==f.COL&&Ne(e,t);break;case 4:n===f.HTML?function(e,t){0===e.openElements.tmplCount&&e.treeAdapter.adoptAttributes(e.openElements.items[0],t.attrs)}(e,t):n===f.BASE||n===f.LINK||n===f.META?ae(e,t):n===f.BODY?function(e,t){var n=e.openElements.tryPeekProperlyNestedBodyElement();n&&0===e.openElements.tmplCount&&(e.framesetOk=!1,e.treeAdapter.adoptAttributes(n,t.attrs))}(e,t):n===f.MAIN?fe(e,t):n===f.FORM?function(e,t){var n=e.openElements.tmplCount>0;e.formElement&&!n||(e.openElements.hasInButtonScope(f.P)&&e._closePElement(),e._insertElement(t,T.HTML),n||(e.formElement=e.openElements.current))}(e,t):n===f.CODE||n===f.FONT?de(e,t):n===f.NOBR?function(e,t){e._reconstructActiveFormattingElements(),e.openElements.hasInScope(f.NOBR)&&(Z(e,t),e._reconstructActiveFormattingElements()),e._insertElement(t,T.HTML),e.activeFormattingElements.pushElement(e.openElements.current,t)}(e,t):n===f.AREA?Ee(e,t):n===f.MATH?function(e,t){e._reconstructActiveFormattingElements(),l.adjustTokenMathMLAttrs(t),l.adjustTokenXMLAttrs(t),t.selfClosing?e._appendElement(t,T.MATHML):e._insertElement(t,T.MATHML)}(e,t):n===f.MENU?function(e,t){e.openElements.hasInButtonScope(f.P)&&e._closePElement(),e.openElements.currentTagName===f.MENUITEM&&e.openElements.pop(),e._insertElement(t,T.HTML)}(e,t):n!==f.HEAD&&Ne(e,t);break;case 5:n===f.STYLE||n===f.TITLE?ae(e,t):n===f.ASIDE?fe(e,t):n===f.SMALL?de(e,t):n===f.TABLE?function(e,t){e.treeAdapter.getDocumentMode(e.document)!==h.DOCUMENT_MODE.QUIRKS&&e.openElements.hasInButtonScope(f.P)&&e._closePElement(),e._insertElement(t,T.HTML),e.framesetOk=!1,e.insertionMode=v}(e,t):n===f.EMBED?Ee(e,t):n===f.INPUT?function(e,t){e._reconstructActiveFormattingElements(),e._appendElement(t,T.HTML);var n=r.getTokenAttr(t,d.TYPE);n&&n.toLowerCase()===E||(e.framesetOk=!1)}(e,t):n===f.PARAM||n===f.TRACK?_e(e,t):n===f.IMAGE?function(e,t){t.tagName=f.IMG,Ee(e,t)}(e,t):n!==f.FRAME&&n!==f.TBODY&&n!==f.TFOOT&&n!==f.THEAD&&Ne(e,t);break;case 6:n===f.SCRIPT?ae(e,t):n===f.CENTER||n===f.FIGURE||n===f.FOOTER||n===f.HEADER||n===f.HGROUP?fe(e,t):n===f.BUTTON?function(e,t){e.openElements.hasInScope(f.BUTTON)&&(e.openElements.generateImpliedEndTags(),e.openElements.popUntilTagNamePopped(f.BUTTON)),e._reconstructActiveFormattingElements(),e._insertElement(t,T.HTML),e.framesetOk=!1}(e,t):n===f.STRIKE||n===f.STRONG?de(e,t):n===f.APPLET||n===f.OBJECT?me(e,t):n===f.KEYGEN?Ee(e,t):n===f.SOURCE?_e(e,t):n===f.IFRAME?function(e,t){e.framesetOk=!1,e._switchToTextParsing(t,r.MODE.RAWTEXT)}(e,t):n===f.SELECT?function(e,t){e._reconstructActiveFormattingElements(),e._insertElement(t,T.HTML),e.framesetOk=!1,e.insertionMode===v||e.insertionMode===R||e.insertionMode===M||e.insertionMode===y||e.insertionMode===D?e.insertionMode=x:e.insertionMode=P}(e,t):n===f.OPTION?Ae(e,t):Ne(e,t);break;case 7:n===f.BGSOUND?ae(e,t):n===f.DETAILS||n===f.ADDRESS||n===f.ARTICLE||n===f.SECTION||n===f.SUMMARY?fe(e,t):n===f.LISTING?Te(e,t):n===f.MARQUEE?me(e,t):n===f.NOEMBED?ge(e,t):n!==f.CAPTION&&Ne(e,t);break;case 8:n===f.BASEFONT?ae(e,t):n===f.MENUITEM?function(e,t){e.openElements.currentTagName===f.MENUITEM&&e.openElements.pop(),e._reconstructActiveFormattingElements(),e._insertElement(t,T.HTML)}(e,t):n===f.FRAMESET?function(e,t){var n=e.openElements.tryPeekProperlyNestedBodyElement();e.framesetOk&&n&&(e.treeAdapter.detachNode(n),e.openElements.popAllUpToHtmlElement(),e._insertElement(t,T.HTML),e.insertionMode=F)}(e,t):n===f.FIELDSET?fe(e,t):n===f.TEXTAREA?function(e,t){e._insertElement(t,T.HTML),e.skipNextNewLine=!0,e.tokenizer.state=r.MODE.RCDATA,e.originalInsertionMode=e.insertionMode,e.framesetOk=!1,e.insertionMode=L}(e,t):n===f.TEMPLATE?ae(e,t):n===f.NOSCRIPT?ge(e,t):n===f.OPTGROUP?Ae(e,t):n!==f.COLGROUP&&Ne(e,t);break;case 9:n===f.PLAINTEXT?function(e,t){e.openElements.hasInButtonScope(f.P)&&e._closePElement(),e._insertElement(t,T.HTML),e.tokenizer.state=r.MODE.PLAINTEXT}(e,t):Ne(e,t);break;case 10:n===f.BLOCKQUOTE||n===f.FIGCAPTION?fe(e,t):Ne(e,t);break;default:Ne(e,t)}}function Oe(e,t){var n=t.tagName;e.openElements.hasInScope(n)&&(e.openElements.generateImpliedEndTags(),e.openElements.popUntilTagNamePopped(n))}function Se(e,t){var n=t.tagName;e.openElements.hasInScope(n)&&(e.openElements.generateImpliedEndTags(),e.openElements.popUntilTagNamePopped(n),e.activeFormattingElements.clearToLastMarker())}function Le(e,t){for(var n=t.tagName,r=e.openElements.stackTop;r>0;r--){var s=e.openElements.items[r];if(e.treeAdapter.getTagName(s)===n){e.openElements.generateImpliedEndTagsWithExclusion(n),e.openElements.popUntilElementPopped(s);break}if(e._isSpecialElement(s))break}}function ve(e,t){var n=t.tagName;switch(n.length){case 1:n===f.A||n===f.B||n===f.I||n===f.S||n===f.U?Z(e,t):n===f.P?function(e){e.openElements.hasInButtonScope(f.P)||e._insertFakeElement(f.P),e._closePElement()}(e):Le(e,t);break;case 2:n===f.DL||n===f.UL||n===f.OL?Oe(e,t):n===f.LI?function(e){e.openElements.hasInListItemScope(f.LI)&&(e.openElements.generateImpliedEndTagsWithExclusion(f.LI),e.openElements.popUntilTagNamePopped(f.LI))}(e):n===f.DD||n===f.DT?function(e,t){var n=t.tagName;e.openElements.hasInScope(n)&&(e.openElements.generateImpliedEndTagsWithExclusion(n),e.openElements.popUntilTagNamePopped(n))}(e,t):n===f.H1||n===f.H2||n===f.H3||n===f.H4||n===f.H5||n===f.H6?function(e){e.openElements.hasNumberedHeaderInScope()&&(e.openElements.generateImpliedEndTags(),e.openElements.popUntilNumberedHeaderPopped())}(e):n===f.BR?function(e){e._reconstructActiveFormattingElements(),e._insertFakeElement(f.BR),e.openElements.pop(),e.framesetOk=!1}(e):n===f.EM||n===f.TT?Z(e,t):Le(e,t);break;case 3:n===f.BIG?Z(e,t):n===f.DIR||n===f.DIV||n===f.NAV?Oe(e,t):Le(e,t);break;case 4:n===f.BODY?function(e){e.openElements.hasInScope(f.BODY)&&(e.insertionMode=w)}(e):n===f.HTML?function(e,t){e.openElements.hasInScope(f.BODY)&&(e.insertionMode=w,e._processToken(t))}(e,t):n===f.FORM?function(e){var t=e.openElements.tmplCount>0,n=e.formElement;t||(e.formElement=null),(n||t)&&e.openElements.hasInScope(f.FORM)&&(e.openElements.generateImpliedEndTags(),t?e.openElements.popUntilTagNamePopped(f.FORM):e.openElements.remove(n))}(e):n===f.CODE||n===f.FONT||n===f.NOBR?Z(e,t):n===f.MAIN||n===f.MENU?Oe(e,t):Le(e,t);break;case 5:n===f.ASIDE?Oe(e,t):n===f.SMALL?Z(e,t):Le(e,t);break;case 6:n===f.CENTER||n===f.FIGURE||n===f.FOOTER||n===f.HEADER||n===f.HGROUP?Oe(e,t):n===f.APPLET||n===f.OBJECT?Se(e,t):n===f.STRIKE||n===f.STRONG?Z(e,t):Le(e,t);break;case 7:n===f.ADDRESS||n===f.ARTICLE||n===f.DETAILS||n===f.SECTION||n===f.SUMMARY?Oe(e,t):n===f.MARQUEE?Se(e,t):Le(e,t);break;case 8:n===f.FIELDSET?Oe(e,t):n===f.TEMPLATE?ce(e,t):Le(e,t);break;case 10:n===f.BLOCKQUOTE||n===f.FIGCAPTION?Oe(e,t):Le(e,t);break;default:Le(e,t)}}function be(e,t){e.tmplInsertionModeStackTop>-1?He(e,t):e.stopped=!0}function Re(e,t){var n=e.openElements.currentTagName;n===f.TABLE||n===f.TBODY||n===f.TFOOT||n===f.THEAD||n===f.TR?(e.pendingCharacterTokens=[],e.hasNonWhitespacePendingCharacterToken=!1,e.originalInsertionMode=e.insertionMode,e.insertionMode=b,e._processToken(t)):ye(e,t)}function Ie(e,t){var n=t.tagName;switch(n.length){case 2:n===f.TD||n===f.TH||n===f.TR?function(e,t){e.openElements.clearBackToTableContext(),e._insertFakeElement(f.TBODY),e.insertionMode=M,e._processToken(t)}(e,t):ye(e,t);break;case 3:n===f.COL?function(e,t){e.openElements.clearBackToTableContext(),e._insertFakeElement(f.COLGROUP),e.insertionMode=I,e._processToken(t)}(e,t):ye(e,t);break;case 4:n===f.FORM?function(e,t){e.formElement||0!==e.openElements.tmplCount||(e._insertElement(t,T.HTML),e.formElement=e.openElements.current,e.openElements.pop())}(e,t):ye(e,t);break;case 5:n===f.TABLE?function(e,t){e.openElements.hasInTableScope(f.TABLE)&&(e.openElements.popUntilTagNamePopped(f.TABLE),e._resetInsertionMode(),e._processToken(t))}(e,t):n===f.STYLE?ae(e,t):n===f.TBODY||n===f.TFOOT||n===f.THEAD?function(e,t){e.openElements.clearBackToTableContext(),e._insertElement(t,T.HTML),e.insertionMode=M}(e,t):n===f.INPUT?function(e,t){var n=r.getTokenAttr(t,d.TYPE);n&&n.toLowerCase()===E?e._appendElement(t,T.HTML):ye(e,t)}(e,t):ye(e,t);break;case 6:n===f.SCRIPT?ae(e,t):ye(e,t);break;case 7:n===f.CAPTION?function(e,t){e.openElements.clearBackToTableContext(),e.activeFormattingElements.insertMarker(),e._insertElement(t,T.HTML),e.insertionMode=R}(e,t):ye(e,t);break;case 8:n===f.COLGROUP?function(e,t){e.openElements.clearBackToTableContext(),e._insertElement(t,T.HTML),e.insertionMode=I}(e,t):n===f.TEMPLATE?ae(e,t):ye(e,t);break;default:ye(e,t)}}function Me(e,t){var n=t.tagName;n===f.TABLE?e.openElements.hasInTableScope(f.TABLE)&&(e.openElements.popUntilTagNamePopped(f.TABLE),e._resetInsertionMode()):n===f.TEMPLATE?ce(e,t):n!==f.BODY&&n!==f.CAPTION&&n!==f.COL&&n!==f.COLGROUP&&n!==f.HTML&&n!==f.TBODY&&n!==f.TD&&n!==f.TFOOT&&n!==f.TH&&n!==f.THEAD&&n!==f.TR&&ye(e,t)}function ye(e,t){var n=e.fosterParentingEnabled;e.fosterParentingEnabled=!0,e._processTokenInBodyMode(t),e.fosterParentingEnabled=n}function De(e,t){e.openElements.currentTagName===f.COLGROUP&&(e.openElements.pop(),e.insertionMode=v,e._processToken(t))}function Pe(e,t){var n=t.tagName;n===f.HTML?ke(e,t):n===f.OPTION?(e.openElements.currentTagName===f.OPTION&&e.openElements.pop(),e._insertElement(t,T.HTML)):n===f.OPTGROUP?(e.openElements.currentTagName===f.OPTION&&e.openElements.pop(),e.openElements.currentTagName===f.OPTGROUP&&e.openElements.pop(),e._insertElement(t,T.HTML)):n===f.INPUT||n===f.KEYGEN||n===f.TEXTAREA||n===f.SELECT?e.openElements.hasInSelectScope(f.SELECT)&&(e.openElements.popUntilTagNamePopped(f.SELECT),e._resetInsertionMode(),n!==f.SELECT&&e._processToken(t)):n!==f.SCRIPT&&n!==f.TEMPLATE||ae(e,t)}function xe(e,t){var n=t.tagName;if(n===f.OPTGROUP){var r=e.openElements.items[e.openElements.stackTop-1],s=r&&e.treeAdapter.getTagName(r);e.openElements.currentTagName===f.OPTION&&s===f.OPTGROUP&&e.openElements.pop(),e.openElements.currentTagName===f.OPTGROUP&&e.openElements.pop()}else n===f.OPTION?e.openElements.currentTagName===f.OPTION&&e.openElements.pop():n===f.SELECT&&e.openElements.hasInSelectScope(f.SELECT)?(e.openElements.popUntilTagNamePopped(f.SELECT),e._resetInsertionMode()):n===f.TEMPLATE&&ce(e,t)}function He(e,t){e.openElements.tmplCount>0?(e.openElements.popUntilTagNamePopped(f.TEMPLATE),e.activeFormattingElements.clearToLastMarker(),e._popTmplInsertionMode(),e._resetInsertionMode(),e._processToken(t)):e.stopped=!0}function we(e,t){e.insertionMode=S,e._processToken(t)}function Fe(e,t){e.insertionMode=S,e._processToken(t)}j.prototype.parse=function(e){var t=this.treeAdapter.createDocument();return this._bootstrap(t,null),this.tokenizer.write(e,!0),this._runParsingLoop(null),t},j.prototype.parseFragment=function(e,t){t||(t=this.treeAdapter.createElement(f.TEMPLATE,T.HTML,[]));var n=this.treeAdapter.createElement("documentmock",T.HTML,[]);this._bootstrap(n,t),this.treeAdapter.getTagName(t)===f.TEMPLATE&&this._pushTmplInsertionMode(H),this._initTokenizerForFragmentParsing(),this._insertFakeRootElement(),this._resetInsertionMode(),this._findFormInFragmentContext(),this.tokenizer.write(e,!0),this._runParsingLoop(null);var r=this.treeAdapter.getFirstChild(n),s=this.treeAdapter.createDocumentFragment();return this._adoptNodes(r,s),s},j.prototype._bootstrap=function(e,t){this.tokenizer=new r(this.options),this.stopped=!1,this.insertionMode=A,this.originalInsertionMode="",this.document=e,this.fragmentContext=t,this.headElement=null,this.formElement=null,this.openElements=new s(this.document,this.treeAdapter),this.activeFormattingElements=new i(this.treeAdapter),this.tmplInsertionModeStack=[],this.tmplInsertionModeStackTop=-1,this.currentTmplInsertionMode=null,this.pendingCharacterTokens=[],this.hasNonWhitespacePendingCharacterToken=!1,this.framesetOk=!0,this.skipNextNewLine=!1,this.fosterParentingEnabled=!1},j.prototype._runParsingLoop=function(e){for(;!this.stopped;){this._setupTokenizerCDATAMode();var t=this.tokenizer.getNextToken();if(t.type===r.HIBERNATION_TOKEN)break;if(this.skipNextNewLine&&(this.skipNextNewLine=!1,t.type===r.WHITESPACE_CHARACTER_TOKEN&&"\n"===t.chars[0])){if(1===t.chars.length)continue;t.chars=t.chars.substr(1)}if(this._processInputToken(t),e&&this.pendingScript)break}},j.prototype.runParsingLoopForCurrentChunk=function(e,t){if(this._runParsingLoop(t),t&&this.pendingScript){var n=this.pendingScript;return this.pendingScript=null,void t(n)}e&&e()},j.prototype._setupTokenizerCDATAMode=function(){var e=this._getAdjustedCurrentElement();this.tokenizer.allowCDATA=e&&e!==this.document&&this.treeAdapter.getNamespaceURI(e)!==T.HTML&&!this._isIntegrationPoint(e)},j.prototype._switchToTextParsing=function(e,t){this._insertElement(e,T.HTML),this.tokenizer.state=t,this.originalInsertionMode=this.insertionMode,this.insertionMode=L},j.prototype.switchToPlaintextParsing=function(){this.insertionMode=L,this.originalInsertionMode=S,this.tokenizer.state=r.MODE.PLAINTEXT},j.prototype._getAdjustedCurrentElement=function(){return 0===this.openElements.stackTop&&this.fragmentContext?this.fragmentContext:this.openElements.current},j.prototype._findFormInFragmentContext=function(){var e=this.fragmentContext;do{if(this.treeAdapter.getTagName(e)===f.FORM){this.formElement=e;break}e=this.treeAdapter.getParentNode(e)}while(e)},j.prototype._initTokenizerForFragmentParsing=function(){if(this.treeAdapter.getNamespaceURI(this.fragmentContext)===T.HTML){var e=this.treeAdapter.getTagName(this.fragmentContext);e===f.TITLE||e===f.TEXTAREA?this.tokenizer.state=r.MODE.RCDATA:e===f.STYLE||e===f.XMP||e===f.IFRAME||e===f.NOEMBED||e===f.NOFRAMES||e===f.NOSCRIPT?this.tokenizer.state=r.MODE.RAWTEXT:e===f.SCRIPT?this.tokenizer.state=r.MODE.SCRIPT_DATA:e===f.PLAINTEXT&&(this.tokenizer.state=r.MODE.PLAINTEXT)}},j.prototype._setDocumentType=function(e){this.treeAdapter.setDocumentType(this.document,e.name,e.publicId,e.systemId)},j.prototype._attachElementToTree=function(e){if(this._shouldFosterParentOnInsertion())this._fosterParentElement(e);else{var t=this.openElements.currentTmplContent||this.openElements.current;this.treeAdapter.appendChild(t,e)}},j.prototype._appendElement=function(e,t){var n=this.treeAdapter.createElement(e.tagName,t,e.attrs);this._attachElementToTree(n)},j.prototype._insertElement=function(e,t){var n=this.treeAdapter.createElement(e.tagName,t,e.attrs);this._attachElementToTree(n),this.openElements.push(n)},j.prototype._insertFakeElement=function(e){var t=this.treeAdapter.createElement(e,T.HTML,[]);this._attachElementToTree(t),this.openElements.push(t)},j.prototype._insertTemplate=function(e){var t=this.treeAdapter.createElement(e.tagName,T.HTML,e.attrs),n=this.treeAdapter.createDocumentFragment();this.treeAdapter.setTemplateContent(t,n),this._attachElementToTree(t),this.openElements.push(t)},j.prototype._insertFakeRootElement=function(){var e=this.treeAdapter.createElement(f.HTML,T.HTML,[]);this.treeAdapter.appendChild(this.openElements.current,e),this.openElements.push(e)},j.prototype._appendCommentNode=function(e,t){var n=this.treeAdapter.createCommentNode(e.data);this.treeAdapter.appendChild(t,n)},j.prototype._insertCharacters=function(e){if(this._shouldFosterParentOnInsertion())this._fosterParentText(e.chars);else{var t=this.openElements.currentTmplContent||this.openElements.current;this.treeAdapter.insertText(t,e.chars)}},j.prototype._adoptNodes=function(e,t){for(;;){var n=this.treeAdapter.getFirstChild(e);if(!n)break;this.treeAdapter.detachNode(n),this.treeAdapter.appendChild(t,n)}},j.prototype._shouldProcessTokenInForeignContent=function(e){var t=this._getAdjustedCurrentElement();if(!t||t===this.document)return!1;var n=this.treeAdapter.getNamespaceURI(t);if(n===T.HTML)return!1;if(this.treeAdapter.getTagName(t)===f.ANNOTATION_XML&&n===T.MATHML&&e.type===r.START_TAG_TOKEN&&e.tagName===f.SVG)return!1;var s=e.type===r.CHARACTER_TOKEN||e.type===r.NULL_CHARACTER_TOKEN||e.type===r.WHITESPACE_CHARACTER_TOKEN;return!((e.type===r.START_TAG_TOKEN&&e.tagName!==f.MGLYPH&&e.tagName!==f.MALIGNMARK||s)&&this._isIntegrationPoint(t,T.MATHML)||(e.type===r.START_TAG_TOKEN||s)&&this._isIntegrationPoint(t,T.HTML)||e.type===r.EOF_TOKEN)},j.prototype._processToken=function(e){z[this.insertionMode][e.type](this,e)},j.prototype._processTokenInBodyMode=function(e){z[S][e.type](this,e)},j.prototype._processTokenInForeignContent=function(e){e.type===r.CHARACTER_TOKEN?function(e,t){e._insertCharacters(t),e.framesetOk=!1}(this,e):e.type===r.NULL_CHARACTER_TOKEN?function(e,t){t.chars=p.REPLACEMENT_CHARACTER,e._insertCharacters(t)}(this,e):e.type===r.WHITESPACE_CHARACTER_TOKEN?ne(this,e):e.type===r.COMMENT_TOKEN?ee(this,e):e.type===r.START_TAG_TOKEN?function(e,t){if(l.causesExit(t)&&!e.fragmentContext){for(;e.treeAdapter.getNamespaceURI(e.openElements.current)!==T.HTML&&!e._isIntegrationPoint(e.openElements.current);)e.openElements.pop();e._processToken(t)}else{var n=e._getAdjustedCurrentElement(),r=e.treeAdapter.getNamespaceURI(n);r===T.MATHML?l.adjustTokenMathMLAttrs(t):r===T.SVG&&(l.adjustTokenSVGTagName(t),l.adjustTokenSVGAttrs(t)),l.adjustTokenXMLAttrs(t),t.selfClosing?e._appendElement(t,r):e._insertElement(t,r)}}(this,e):e.type===r.END_TAG_TOKEN&&function(e,t){for(var n=e.openElements.stackTop;n>0;n--){var r=e.openElements.items[n];if(e.treeAdapter.getNamespaceURI(r)===T.HTML){e._processToken(t);break}if(e.treeAdapter.getTagName(r).toLowerCase()===t.tagName){e.openElements.popUntilElementPopped(r);break}}}(this,e)},j.prototype._processInputToken=function(e){this._shouldProcessTokenInForeignContent(e)?this._processTokenInForeignContent(e):this._processToken(e)},j.prototype._isIntegrationPoint=function(e,t){var n=this.treeAdapter.getTagName(e),r=this.treeAdapter.getNamespaceURI(e),s=this.treeAdapter.getAttrList(e);return l.isIntegrationPoint(n,r,s,t)},j.prototype._reconstructActiveFormattingElements=function(){var e=this.activeFormattingElements.length;if(e){var t=e,n=null;do{if(t--,(n=this.activeFormattingElements.entries[t]).type===i.MARKER_ENTRY||this.openElements.contains(n.element)){t++;break}}while(t>0);for(var r=t;r<e;r++)n=this.activeFormattingElements.entries[r],this._insertElement(n.token,this.treeAdapter.getNamespaceURI(n.element)),n.element=this.openElements.current}},j.prototype._closeTableCell=function(){this.openElements.generateImpliedEndTags(),this.openElements.popUntilTableCellPopped(),this.activeFormattingElements.clearToLastMarker(),this.insertionMode=y},j.prototype._closePElement=function(){this.openElements.generateImpliedEndTagsWithExclusion(f.P),this.openElements.popUntilTagNamePopped(f.P)},j.prototype._resetInsertionMode=function(){for(var e=this.openElements.stackTop,t=!1;e>=0;e--){var n=this.openElements.items[e];0===e&&(t=!0,this.fragmentContext&&(n=this.fragmentContext));var r=this.treeAdapter.getTagName(n),s=K[r];if(s){this.insertionMode=s;break}if(!(t||r!==f.TD&&r!==f.TH)){this.insertionMode=D;break}if(!t&&r===f.HEAD){this.insertionMode=k;break}if(r===f.SELECT){this._resetInsertionModeForSelect(e);break}if(r===f.TEMPLATE){this.insertionMode=this.currentTmplInsertionMode;break}if(r===f.HTML){this.insertionMode=this.headElement?O:N;break}if(t){this.insertionMode=S;break}}},j.prototype._resetInsertionModeForSelect=function(e){if(e>0)for(var t=e-1;t>0;t--){var n=this.openElements.items[t],r=this.treeAdapter.getTagName(n);if(r===f.TEMPLATE)break;if(r===f.TABLE)return void(this.insertionMode=x)}this.insertionMode=P},j.prototype._pushTmplInsertionMode=function(e){this.tmplInsertionModeStack.push(e),this.tmplInsertionModeStackTop++,this.currentTmplInsertionMode=e},j.prototype._popTmplInsertionMode=function(){this.tmplInsertionModeStack.pop(),this.tmplInsertionModeStackTop--,this.currentTmplInsertionMode=this.tmplInsertionModeStack[this.tmplInsertionModeStackTop]},j.prototype._isElementCausesFosterParenting=function(e){var t=this.treeAdapter.getTagName(e);return t===f.TABLE||t===f.TBODY||t===f.TFOOT||t===f.THEAD||t===f.TR},j.prototype._shouldFosterParentOnInsertion=function(){return this.fosterParentingEnabled&&this._isElementCausesFosterParenting(this.openElements.current)},j.prototype._findFosterParentingLocation=function(){for(var e={parent:null,beforeElement:null},t=this.openElements.stackTop;t>=0;t--){var n=this.openElements.items[t],r=this.treeAdapter.getTagName(n),s=this.treeAdapter.getNamespaceURI(n);if(r===f.TEMPLATE&&s===T.HTML){e.parent=this.treeAdapter.getTemplateContent(n);break}if(r===f.TABLE){e.parent=this.treeAdapter.getParentNode(n),e.parent?e.beforeElement=n:e.parent=this.openElements.items[t-1];break}}return e.parent||(e.parent=this.openElements.items[0]),e},j.prototype._fosterParentElement=function(e){var t=this._findFosterParentingLocation();t.beforeElement?this.treeAdapter.insertBefore(t.parent,e,t.beforeElement):this.treeAdapter.appendChild(t.parent,e)},j.prototype._fosterParentText=function(e){var t=this._findFosterParentingLocation();t.beforeElement?this.treeAdapter.insertTextBefore(t.parent,e,t.beforeElement):this.treeAdapter.insertText(t.parent,e)},j.prototype._isSpecialElement=function(e){var t=this.treeAdapter.getTagName(e),n=this.treeAdapter.getNamespaceURI(e);return h.SPECIAL_ELEMENTS[n][t]}},6371:(e,t,n)=>{"use strict";var r=n(6584),s=r.TAG_NAMES,i=r.NAMESPACES;function o(e){switch(e.length){case 1:return e===s.P;case 2:return e===s.RB||e===s.RP||e===s.RT||e===s.DD||e===s.DT||e===s.LI;case 3:return e===s.RTC;case 6:return e===s.OPTION;case 8:return e===s.OPTGROUP||e===s.MENUITEM}return!1}function a(e,t){switch(e.length){case 2:if(e===s.TD||e===s.TH)return t===i.HTML;if(e===s.MI||e===s.MO||e===s.MN||e===s.MS)return t===i.MATHML;break;case 4:if(e===s.HTML)return t===i.HTML;if(e===s.DESC)return t===i.SVG;break;case 5:if(e===s.TABLE)return t===i.HTML;if(e===s.MTEXT)return t===i.MATHML;if(e===s.TITLE)return t===i.SVG;break;case 6:return(e===s.APPLET||e===s.OBJECT)&&t===i.HTML;case 7:return(e===s.CAPTION||e===s.MARQUEE)&&t===i.HTML;case 8:return e===s.TEMPLATE&&t===i.HTML;case 13:return e===s.FOREIGN_OBJECT&&t===i.SVG;case 14:return e===s.ANNOTATION_XML&&t===i.MATHML}return!1}var c=e.exports=function(e,t){this.stackTop=-1,this.items=[],this.current=e,this.currentTagName=null,this.currentTmplContent=null,this.tmplCount=0,this.treeAdapter=t};c.prototype._indexOf=function(e){for(var t=-1,n=this.stackTop;n>=0;n--)if(this.items[n]===e){t=n;break}return t},c.prototype._isInTemplate=function(){return this.currentTagName===s.TEMPLATE&&this.treeAdapter.getNamespaceURI(this.current)===i.HTML},c.prototype._updateCurrentElement=function(){this.current=this.items[this.stackTop],this.currentTagName=this.current&&this.treeAdapter.getTagName(this.current),this.currentTmplContent=this._isInTemplate()?this.treeAdapter.getTemplateContent(this.current):null},c.prototype.push=function(e){this.items[++this.stackTop]=e,this._updateCurrentElement(),this._isInTemplate()&&this.tmplCount++},c.prototype.pop=function(){this.stackTop--,this.tmplCount>0&&this._isInTemplate()&&this.tmplCount--,this._updateCurrentElement()},c.prototype.replace=function(e,t){var n=this._indexOf(e);this.items[n]=t,n===this.stackTop&&this._updateCurrentElement()},c.prototype.insertAfter=function(e,t){var n=this._indexOf(e)+1;this.items.splice(n,0,t),n===++this.stackTop&&this._updateCurrentElement()},c.prototype.popUntilTagNamePopped=function(e){for(;this.stackTop>-1;){var t=this.currentTagName,n=this.treeAdapter.getNamespaceURI(this.current);if(this.pop(),t===e&&n===i.HTML)break}},c.prototype.popUntilElementPopped=function(e){for(;this.stackTop>-1;){var t=this.current;if(this.pop(),t===e)break}},c.prototype.popUntilNumberedHeaderPopped=function(){for(;this.stackTop>-1;){var e=this.currentTagName,t=this.treeAdapter.getNamespaceURI(this.current);if(this.pop(),e===s.H1||e===s.H2||e===s.H3||e===s.H4||e===s.H5||e===s.H6&&t===i.HTML)break}},c.prototype.popUntilTableCellPopped=function(){for(;this.stackTop>-1;){var e=this.currentTagName,t=this.treeAdapter.getNamespaceURI(this.current);if(this.pop(),e===s.TD||e===s.TH&&t===i.HTML)break}},c.prototype.popAllUpToHtmlElement=function(){this.stackTop=0,this._updateCurrentElement()},c.prototype.clearBackToTableContext=function(){for(;this.currentTagName!==s.TABLE&&this.currentTagName!==s.TEMPLATE&&this.currentTagName!==s.HTML||this.treeAdapter.getNamespaceURI(this.current)!==i.HTML;)this.pop()},c.prototype.clearBackToTableBodyContext=function(){for(;this.currentTagName!==s.TBODY&&this.currentTagName!==s.TFOOT&&this.currentTagName!==s.THEAD&&this.currentTagName!==s.TEMPLATE&&this.currentTagName!==s.HTML||this.treeAdapter.getNamespaceURI(this.current)!==i.HTML;)this.pop()},c.prototype.clearBackToTableRowContext=function(){for(;this.currentTagName!==s.TR&&this.currentTagName!==s.TEMPLATE&&this.currentTagName!==s.HTML||this.treeAdapter.getNamespaceURI(this.current)!==i.HTML;)this.pop()},c.prototype.remove=function(e){for(var t=this.stackTop;t>=0;t--)if(this.items[t]===e){this.items.splice(t,1),this.stackTop--,this._updateCurrentElement();break}},c.prototype.tryPeekProperlyNestedBodyElement=function(){var e=this.items[1];return e&&this.treeAdapter.getTagName(e)===s.BODY?e:null},c.prototype.contains=function(e){return this._indexOf(e)>-1},c.prototype.getCommonAncestor=function(e){var t=this._indexOf(e);return--t>=0?this.items[t]:null},c.prototype.isRootHtmlElementCurrent=function(){return 0===this.stackTop&&this.currentTagName===s.HTML},c.prototype.hasInScope=function(e){for(var t=this.stackTop;t>=0;t--){var n=this.treeAdapter.getTagName(this.items[t]),r=this.treeAdapter.getNamespaceURI(this.items[t]);if(n===e&&r===i.HTML)return!0;if(a(n,r))return!1}return!0},c.prototype.hasNumberedHeaderInScope=function(){for(var e=this.stackTop;e>=0;e--){var t=this.treeAdapter.getTagName(this.items[e]),n=this.treeAdapter.getNamespaceURI(this.items[e]);if((t===s.H1||t===s.H2||t===s.H3||t===s.H4||t===s.H5||t===s.H6)&&n===i.HTML)return!0;if(a(t,n))return!1}return!0},c.prototype.hasInListItemScope=function(e){for(var t=this.stackTop;t>=0;t--){var n=this.treeAdapter.getTagName(this.items[t]),r=this.treeAdapter.getNamespaceURI(this.items[t]);if(n===e&&r===i.HTML)return!0;if((n===s.UL||n===s.OL)&&r===i.HTML||a(n,r))return!1}return!0},c.prototype.hasInButtonScope=function(e){for(var t=this.stackTop;t>=0;t--){var n=this.treeAdapter.getTagName(this.items[t]),r=this.treeAdapter.getNamespaceURI(this.items[t]);if(n===e&&r===i.HTML)return!0;if(n===s.BUTTON&&r===i.HTML||a(n,r))return!1}return!0},c.prototype.hasInTableScope=function(e){for(var t=this.stackTop;t>=0;t--){var n=this.treeAdapter.getTagName(this.items[t]);if(this.treeAdapter.getNamespaceURI(this.items[t])===i.HTML){if(n===e)return!0;if(n===s.TABLE||n===s.TEMPLATE||n===s.HTML)return!1}}return!0},c.prototype.hasTableBodyContextInTableScope=function(){for(var e=this.stackTop;e>=0;e--){var t=this.treeAdapter.getTagName(this.items[e]);if(this.treeAdapter.getNamespaceURI(this.items[e])===i.HTML){if(t===s.TBODY||t===s.THEAD||t===s.TFOOT)return!0;if(t===s.TABLE||t===s.HTML)return!1}}return!0},c.prototype.hasInSelectScope=function(e){for(var t=this.stackTop;t>=0;t--){var n=this.treeAdapter.getTagName(this.items[t]);if(this.treeAdapter.getNamespaceURI(this.items[t])===i.HTML){if(n===e)return!0;if(n!==s.OPTION&&n!==s.OPTGROUP)return!1}}return!0},c.prototype.generateImpliedEndTags=function(){for(;o(this.currentTagName);)this.pop()},c.prototype.generateImpliedEndTagsWithExclusion=function(e){for(;o(this.currentTagName)&&this.currentTagName!==e;)this.pop()}},4603:(e,t,n)=>{"use strict";var r=n(2203).Writable,s=n(9023).inherits,i=n(3367),o=e.exports=function(e){r.call(this),this.parser=new i(e),this.lastChunkWritten=!1,this.writeCallback=null,this.pausedByScript=!1,this.document=this.parser.treeAdapter.createDocument(),this.pendingHtmlInsertions=[],this._resume=this._resume.bind(this),this._documentWrite=this._documentWrite.bind(this),this._scriptHandler=this._scriptHandler.bind(this),this.parser._bootstrap(this.document,null)};s(o,r),o.prototype._write=function(e,t,n){this.writeCallback=n,this.parser.tokenizer.write(e.toString("utf8"),this.lastChunkWritten),this._runParsingLoop()},o.prototype.end=function(e,t,n){this.lastChunkWritten=!0,r.prototype.end.call(this,e,t,n)},o.prototype._runParsingLoop=function(){this.parser.runParsingLoopForCurrentChunk(this.writeCallback,this._scriptHandler)},o.prototype._resume=function(){if(!this.pausedByScript)throw new Error("Parser was already resumed");for(;this.pendingHtmlInsertions.length;){var e=this.pendingHtmlInsertions.pop();this.parser.tokenizer.insertHtmlAtCurrentPos(e)}this.pausedByScript=!1,this.parser.tokenizer.active&&this._runParsingLoop()},o.prototype._documentWrite=function(e){this.parser.stopped||this.pendingHtmlInsertions.push(e)},o.prototype._scriptHandler=function(e){this.listeners("script").length?(this.pausedByScript=!0,this.emit("script",e,this._documentWrite,this._resume)):this._runParsingLoop()}},5707:(e,t,n)=>{"use strict";var r=n(4603),s=n(9023).inherits,i=n(6584).TAG_NAMES;s(e.exports=function(e){r.call(this,e),this.parser._insertFakeElement(i.HTML),this.parser._insertFakeElement(i.HEAD),this.parser.openElements.pop(),this.parser._insertFakeElement(i.BODY),this.parser._insertFakeElement(i.PRE),this.parser.treeAdapter.insertText(this.parser.openElements.current,"\n"),this.parser.switchToPlaintextParsing()},r)},5450:(e,t,n)=>{"use strict";var r=n(2203).Writable,s=n(9023),i=e.exports=function(){r.call(this)};s.inherits(i,r),i.prototype._write=function(e,t,n){n()}},8564:(e,t,n)=>{"use strict";var r=n(2203).Transform,s=n(5450),i=n(9023).inherits,o=n(7881),a=n(1686),c=n(5580),l={locationInfo:!1},u=e.exports=function(e){r.call(this),this.options=c(l,e),this.tokenizer=new o(e),this.parserFeedbackSimulator=new a(this.tokenizer),this.pendingText=null,this.currentTokenLocation=void 0,this.lastChunkWritten=!1,this.stopped=!1,this.pipe(new s)};i(u,r),u.prototype._transform=function(e,t,n){this.stopped||(this.tokenizer.write(e.toString("utf8"),this.lastChunkWritten),this._runParsingLoop()),this.push(e),n()},u.prototype._flush=function(e){e()},u.prototype.end=function(e,t,n){this.lastChunkWritten=!0,r.prototype.end.call(this,e,t,n)},u.prototype.stop=function(){this.stopped=!0},u.prototype._runParsingLoop=function(){do{var e=this.parserFeedbackSimulator.getNextToken();if(e.type===o.HIBERNATION_TOKEN)break;e.type===o.CHARACTER_TOKEN||e.type===o.WHITESPACE_CHARACTER_TOKEN||e.type===o.NULL_CHARACTER_TOKEN?(this.options.locationInfo&&(null===this.pendingText?this.currentTokenLocation=e.location:this.currentTokenLocation.endOffset=e.location.endOffset),this.pendingText=(this.pendingText||"")+e.chars):(this._emitPendingText(),this._handleToken(e))}while(!this.stopped&&e.type!==o.EOF_TOKEN)},u.prototype._handleToken=function(e){this.options.locationInfo&&(this.currentTokenLocation=e.location),e.type===o.START_TAG_TOKEN?this.emit("startTag",e.tagName,e.attrs,e.selfClosing,this.currentTokenLocation):e.type===o.END_TAG_TOKEN?this.emit("endTag",e.tagName,this.currentTokenLocation):e.type===o.COMMENT_TOKEN?this.emit("comment",e.data,this.currentTokenLocation):e.type===o.DOCTYPE_TOKEN&&this.emit("doctype",e.name,e.publicId,e.systemId,this.currentTokenLocation)},u.prototype._emitPendingText=function(){null!==this.pendingText&&(this.emit("text",this.pendingText,this.currentTokenLocation),this.pendingText=null)}},1686:(e,t,n)=>{"use strict";var r=n(7881),s=n(1723),i=n(3306),o=n(6584),a=o.TAG_NAMES,c=o.NAMESPACES,l=e.exports=function(e){this.tokenizer=e,this.namespaceStack=[],this.namespaceStackTop=-1,this._enterNamespace(c.HTML)};l.prototype.getNextToken=function(){var e=this.tokenizer.getNextToken();if(e.type===r.START_TAG_TOKEN)this._handleStartTagToken(e);else if(e.type===r.END_TAG_TOKEN)this._handleEndTagToken(e);else if(e.type===r.NULL_CHARACTER_TOKEN&&this.inForeignContent)e.type=r.CHARACTER_TOKEN,e.chars=i.REPLACEMENT_CHARACTER;else if(this.skipNextNewLine&&(e.type!==r.HIBERNATION_TOKEN&&(this.skipNextNewLine=!1),e.type===r.WHITESPACE_CHARACTER_TOKEN&&"\n"===e.chars[0])){if(1===e.chars.length)return this.getNextToken();e.chars=e.chars.substr(1)}return e},l.prototype._enterNamespace=function(e){this.namespaceStackTop++,this.namespaceStack.push(e),this.inForeignContent=e!==c.HTML,this.currentNamespace=e,this.tokenizer.allowCDATA=this.inForeignContent},l.prototype._leaveCurrentNamespace=function(){this.namespaceStackTop--,this.namespaceStack.pop(),this.currentNamespace=this.namespaceStack[this.namespaceStackTop],this.inForeignContent=this.currentNamespace!==c.HTML,this.tokenizer.allowCDATA=this.inForeignContent},l.prototype._ensureTokenizerMode=function(e){e===a.TEXTAREA||e===a.TITLE?this.tokenizer.state=r.MODE.RCDATA:e===a.PLAINTEXT?this.tokenizer.state=r.MODE.PLAINTEXT:e===a.SCRIPT?this.tokenizer.state=r.MODE.SCRIPT_DATA:e!==a.STYLE&&e!==a.IFRAME&&e!==a.XMP&&e!==a.NOEMBED&&e!==a.NOFRAMES&&e!==a.NOSCRIPT||(this.tokenizer.state=r.MODE.RAWTEXT)},l.prototype._handleStartTagToken=function(e){var t=e.tagName;if(t===a.SVG?this._enterNamespace(c.SVG):t===a.MATH&&this._enterNamespace(c.MATHML),this.inForeignContent){if(s.causesExit(e))return void this._leaveCurrentNamespace();var n=this.currentNamespace;n===c.MATHML?s.adjustTokenMathMLAttrs(e):n===c.SVG&&(s.adjustTokenSVGTagName(e),s.adjustTokenSVGAttrs(e)),s.adjustTokenXMLAttrs(e),t=e.tagName,!e.selfClosing&&s.isIntegrationPoint(t,n,e.attrs)&&this._enterNamespace(c.HTML)}else t===a.PRE||t===a.TEXTAREA||t===a.LISTING?this.skipNextNewLine=!0:t===a.IMAGE&&(e.tagName=a.IMG),this._ensureTokenizerMode(t)},l.prototype._handleEndTagToken=function(e){var t=e.tagName;if(this.inForeignContent)(t===a.SVG&&this.currentNamespace===c.SVG||t===a.MATH&&this.currentNamespace===c.MATHML)&&this._leaveCurrentNamespace();else{var n=this.namespaceStack[this.namespaceStackTop-1];n===c.SVG&&s.SVG_TAG_NAMES_ADJUSTMENT_MAP[t]&&(t=s.SVG_TAG_NAMES_ADJUSTMENT_MAP[t]),s.isIntegrationPoint(t,n,e.attrs)&&this._leaveCurrentNamespace()}this.currentNamespace===c.SVG&&s.adjustTokenSVGTagName(e)}},3272:(e,t,n)=>{"use strict";var r=n(7988),s=n(135),i=n(5580),o=n(6584),a=o.TAG_NAMES,c=o.NAMESPACES,l={treeAdapter:r},u=/&/g,p=/\u00a0/g,h=/"/g,f=/</g,T=/>/g,d=e.exports=function(e,t){this.options=i(l,t),this.treeAdapter=this.options.treeAdapter,this.html="",this.startNode=e};d.escapeString=function(e,t){return e=e.replace(u,"&amp;").replace(p,"&nbsp;"),t?e.replace(h,"&quot;"):e.replace(f,"&lt;").replace(T,"&gt;")},d.prototype.serialize=function(){return this._serializeChildNodes(this.startNode),this.html},d.prototype._serializeChildNodes=function(e){var t=this.treeAdapter.getChildNodes(e);if(t)for(var n=0,r=t.length;n<r;n++){var s=t[n];this.treeAdapter.isElementNode(s)?this._serializeElement(s):this.treeAdapter.isTextNode(s)?this._serializeTextNode(s):this.treeAdapter.isCommentNode(s)?this._serializeCommentNode(s):this.treeAdapter.isDocumentTypeNode(s)&&this._serializeDocumentTypeNode(s)}},d.prototype._serializeElement=function(e){var t=this.treeAdapter.getTagName(e),n=this.treeAdapter.getNamespaceURI(e);if(this.html+="<"+t,this._serializeAttributes(e),this.html+=">",t!==a.AREA&&t!==a.BASE&&t!==a.BASEFONT&&t!==a.BGSOUND&&t!==a.BR&&t!==a.BR&&t!==a.COL&&t!==a.EMBED&&t!==a.FRAME&&t!==a.HR&&t!==a.IMG&&t!==a.INPUT&&t!==a.KEYGEN&&t!==a.LINK&&t!==a.MENUITEM&&t!==a.META&&t!==a.PARAM&&t!==a.SOURCE&&t!==a.TRACK&&t!==a.WBR){var r=t===a.TEMPLATE&&n===c.HTML?this.treeAdapter.getTemplateContent(e):e;this._serializeChildNodes(r),this.html+="</"+t+">"}},d.prototype._serializeAttributes=function(e){for(var t=this.treeAdapter.getAttrList(e),n=0,r=t.length;n<r;n++){var s=t[n],i=d.escapeString(s.value,!0);this.html+=" ",s.namespace?s.namespace===c.XML?this.html+="xml:"+s.name:s.namespace===c.XMLNS?("xmlns"!==s.name&&(this.html+="xmlns:"),this.html+=s.name):s.namespace===c.XLINK?this.html+="xlink:"+s.name:this.html+=s.namespace+":"+s.name:this.html+=s.name,this.html+='="'+i+'"'}},d.prototype._serializeTextNode=function(e){var t=this.treeAdapter.getTextNodeContent(e),n=this.treeAdapter.getParentNode(e),r=void 0;n&&this.treeAdapter.isElementNode(n)&&(r=this.treeAdapter.getTagName(n)),r===a.STYLE||r===a.SCRIPT||r===a.XMP||r===a.IFRAME||r===a.NOEMBED||r===a.NOFRAMES||r===a.PLAINTEXT||r===a.NOSCRIPT?this.html+=t:this.html+=d.escapeString(t,!1)},d.prototype._serializeCommentNode=function(e){this.html+="\x3c!--"+this.treeAdapter.getCommentNodeContent(e)+"--\x3e"},d.prototype._serializeDocumentTypeNode=function(e){var t=this.treeAdapter.getDocumentTypeNodeName(e);this.html+="<"+s.serializeContent(t,null,null)+">"}},8261:(e,t,n)=>{"use strict";var r=n(2203).Readable,s=n(9023).inherits,i=n(3272),o=e.exports=function(e,t){r.call(this),this.serializer=new i(e,t),Object.defineProperty(this.serializer,"html",{get:function(){return""},set:this.push.bind(this)})};s(o,r),o.prototype._read=function(){this.serializer.serialize(),this.push(null)}},7881:(e,t,n)=>{"use strict";var r=n(5238),s=n(3555),i=n(3306),o=n(1763),a=i.CODE_POINTS,c=i.CODE_POINT_SEQUENCES,l={0:65533,13:13,128:8364,129:129,130:8218,131:402,132:8222,133:8230,134:8224,135:8225,136:710,137:8240,138:352,139:8249,140:338,141:141,142:381,143:143,144:144,145:8216,146:8217,147:8220,148:8221,149:8226,150:8211,151:8212,152:732,153:8482,154:353,155:8250,156:339,157:157,158:382,159:376},u="DATA_STATE",p="CHARACTER_REFERENCE_IN_DATA_STATE",h="RCDATA_STATE",f="CHARACTER_REFERENCE_IN_RCDATA_STATE",T="RAWTEXT_STATE",d="SCRIPT_DATA_STATE",m="PLAINTEXT_STATE",E="TAG_OPEN_STATE",_="END_TAG_OPEN_STATE",g="TAG_NAME_STATE",A="RCDATA_LESS_THAN_SIGN_STATE",C="RCDATA_END_TAG_OPEN_STATE",N="RCDATA_END_TAG_NAME_STATE",k="RAWTEXT_LESS_THAN_SIGN_STATE",O="RAWTEXT_END_TAG_OPEN_STATE",S="RAWTEXT_END_TAG_NAME_STATE",L="SCRIPT_DATA_LESS_THAN_SIGN_STATE",v="SCRIPT_DATA_END_TAG_OPEN_STATE",b="SCRIPT_DATA_END_TAG_NAME_STATE",R="SCRIPT_DATA_ESCAPE_START_STATE",I="SCRIPT_DATA_ESCAPE_START_DASH_STATE",M="SCRIPT_DATA_ESCAPED_STATE",y="SCRIPT_DATA_ESCAPED_DASH_STATE",D="SCRIPT_DATA_ESCAPED_DASH_DASH_STATE",P="SCRIPT_DATA_ESCAPED_LESS_THAN_SIGN_STATE",x="SCRIPT_DATA_ESCAPED_END_TAG_OPEN_STATE",H="SCRIPT_DATA_ESCAPED_END_TAG_NAME_STATE",w="SCRIPT_DATA_DOUBLE_ESCAPE_START_STATE",F="SCRIPT_DATA_DOUBLE_ESCAPED_STATE",U="SCRIPT_DATA_DOUBLE_ESCAPED_DASH_STATE",B="SCRIPT_DATA_DOUBLE_ESCAPED_DASH_DASH_STATE",G="SCRIPT_DATA_DOUBLE_ESCAPED_LESS_THAN_SIGN_STATE",K="SCRIPT_DATA_DOUBLE_ESCAPE_END_STATE",q="BEFORE_ATTRIBUTE_NAME_STATE",z="ATTRIBUTE_NAME_STATE",j="AFTER_ATTRIBUTE_NAME_STATE",Y="BEFORE_ATTRIBUTE_VALUE_STATE",V="ATTRIBUTE_VALUE_DOUBLE_QUOTED_STATE",W="ATTRIBUTE_VALUE_SINGLE_QUOTED_STATE",Q="ATTRIBUTE_VALUE_UNQUOTED_STATE",X="CHARACTER_REFERENCE_IN_ATTRIBUTE_VALUE_STATE",$="AFTER_ATTRIBUTE_VALUE_QUOTED_STATE",Z="SELF_CLOSING_START_TAG_STATE",J="BOGUS_COMMENT_STATE",ee="BOGUS_COMMENT_STATE_CONTINUATION",te="MARKUP_DECLARATION_OPEN_STATE",ne="COMMENT_START_STATE",re="COMMENT_START_DASH_STATE",se="COMMENT_STATE",ie="COMMENT_END_DASH_STATE",oe="COMMENT_END_STATE",ae="COMMENT_END_BANG_STATE",ce="DOCTYPE_STATE",le="DOCTYPE_NAME_STATE",ue="AFTER_DOCTYPE_NAME_STATE",pe="BEFORE_DOCTYPE_PUBLIC_IDENTIFIER_STATE",he="DOCTYPE_PUBLIC_IDENTIFIER_DOUBLE_QUOTED_STATE",fe="DOCTYPE_PUBLIC_IDENTIFIER_SINGLE_QUOTED_STATE",Te="BETWEEN_DOCTYPE_PUBLIC_AND_SYSTEM_IDENTIFIERS_STATE",de="BEFORE_DOCTYPE_SYSTEM_IDENTIFIER_STATE",me="DOCTYPE_SYSTEM_IDENTIFIER_DOUBLE_QUOTED_STATE",Ee="DOCTYPE_SYSTEM_IDENTIFIER_SINGLE_QUOTED_STATE",_e="AFTER_DOCTYPE_SYSTEM_IDENTIFIER_STATE",ge="BOGUS_DOCTYPE_STATE",Ae="CDATA_SECTION_STATE";function Ce(e){return e===a.SPACE||e===a.LINE_FEED||e===a.TABULATION||e===a.FORM_FEED}function Ne(e){return e>=a.DIGIT_0&&e<=a.DIGIT_9}function ke(e){return e>=a.LATIN_CAPITAL_A&&e<=a.LATIN_CAPITAL_Z}function Oe(e){return e>=a.LATIN_SMALL_A&&e<=a.LATIN_SMALL_Z}function Se(e){return Oe(e)||ke(e)}function Le(e,t){return Ne(e)||t&&(e>=a.LATIN_CAPITAL_A&&e<=a.LATIN_CAPITAL_F||e>=a.LATIN_SMALL_A&&e<=a.LATIN_SMALL_F)}function ve(e){return e+32}function be(e){return e<=65535?String.fromCharCode(e):(e-=65536,String.fromCharCode(e>>>10&1023|55296)+String.fromCharCode(56320|1023&e))}function Re(e){return String.fromCharCode(ve(e))}function Ie(e,t){for(var n=o[++e],r=++e,s=r+n-1;r<=s;){var i=r+s>>>1,a=o[i];if(a<t)r=i+1;else{if(!(a>t))return o[i+n];s=i-1}}return-1}var Me=e.exports=function(e){this.preprocessor=new r,this.tokenQueue=[],this.allowCDATA=!1,this.state=u,this.returnState="",this.tempBuff=[],this.additionalAllowedCp=void 0,this.lastStartTagName="",this.consumedAfterSnapshot=-1,this.active=!1,this.currentCharacterToken=null,this.currentToken=null,this.currentAttr=null,e&&e.locationInfo&&s.assign(this)};Me.CHARACTER_TOKEN="CHARACTER_TOKEN",Me.NULL_CHARACTER_TOKEN="NULL_CHARACTER_TOKEN",Me.WHITESPACE_CHARACTER_TOKEN="WHITESPACE_CHARACTER_TOKEN",Me.START_TAG_TOKEN="START_TAG_TOKEN",Me.END_TAG_TOKEN="END_TAG_TOKEN",Me.COMMENT_TOKEN="COMMENT_TOKEN",Me.DOCTYPE_TOKEN="DOCTYPE_TOKEN",Me.EOF_TOKEN="EOF_TOKEN",Me.HIBERNATION_TOKEN="HIBERNATION_TOKEN",Me.MODE=Me.prototype.MODE={DATA:u,RCDATA:h,RAWTEXT:T,SCRIPT_DATA:d,PLAINTEXT:m},Me.getTokenAttr=function(e,t){for(var n=e.attrs.length-1;n>=0;n--)if(e.attrs[n].name===t)return e.attrs[n].value;return null},Me.prototype.getNextToken=function(){for(;!this.tokenQueue.length&&this.active;){this._hibernationSnapshot();var e=this._consume();this._ensureHibernation()||this[this.state](e)}return this.tokenQueue.shift()},Me.prototype.write=function(e,t){this.active=!0,this.preprocessor.write(e,t)},Me.prototype.insertHtmlAtCurrentPos=function(e){this.active=!0,this.preprocessor.insertHtmlAtCurrentPos(e)},Me.prototype._hibernationSnapshot=function(){this.consumedAfterSnapshot=0},Me.prototype._ensureHibernation=function(){if(this.preprocessor.endOfChunkHit){for(;this.consumedAfterSnapshot>0;this.consumedAfterSnapshot--)this.preprocessor.retreat();return this.active=!1,this.tokenQueue.push({type:Me.HIBERNATION_TOKEN}),!0}return!1},Me.prototype._consume=function(){return this.consumedAfterSnapshot++,this.preprocessor.advance()},Me.prototype._unconsume=function(){this.consumedAfterSnapshot--,this.preprocessor.retreat()},Me.prototype._unconsumeSeveral=function(e){for(;e--;)this._unconsume()},Me.prototype._reconsumeInState=function(e){this.state=e,this._unconsume()},Me.prototype._consumeSubsequentIfMatch=function(e,t,n){for(var r=0,s=!0,i=e.length,o=0,c=t,l=void 0;o<i;o++){if(o>0&&(c=this._consume(),r++),c===a.EOF){s=!1;break}if(c!==(l=e[o])&&(n||c!==ve(l))){s=!1;break}}return s||this._unconsumeSeveral(r),s},Me.prototype._lookahead=function(){var e=this._consume();return this._unconsume(),e},Me.prototype.isTempBufferEqualToScriptString=function(){if(this.tempBuff.length!==c.SCRIPT_STRING.length)return!1;for(var e=0;e<this.tempBuff.length;e++)if(this.tempBuff[e]!==c.SCRIPT_STRING[e])return!1;return!0},Me.prototype._createStartTagToken=function(){this.currentToken={type:Me.START_TAG_TOKEN,tagName:"",selfClosing:!1,attrs:[]}},Me.prototype._createEndTagToken=function(){this.currentToken={type:Me.END_TAG_TOKEN,tagName:"",attrs:[]}},Me.prototype._createCommentToken=function(){this.currentToken={type:Me.COMMENT_TOKEN,data:""}},Me.prototype._createDoctypeToken=function(e){this.currentToken={type:Me.DOCTYPE_TOKEN,name:e,forceQuirks:!1,publicId:null,systemId:null}},Me.prototype._createCharacterToken=function(e,t){this.currentCharacterToken={type:e,chars:t}},Me.prototype._createAttr=function(e){this.currentAttr={name:e,value:""}},Me.prototype._isDuplicateAttr=function(){return null!==Me.getTokenAttr(this.currentToken,this.currentAttr.name)},Me.prototype._leaveAttrName=function(e){this.state=e,this._isDuplicateAttr()||this.currentToken.attrs.push(this.currentAttr)},Me.prototype._leaveAttrValue=function(e){this.state=e},Me.prototype._isAppropriateEndTagToken=function(){return this.lastStartTagName===this.currentToken.tagName},Me.prototype._emitCurrentToken=function(){this._emitCurrentCharacterToken(),this.currentToken.type===Me.START_TAG_TOKEN&&(this.lastStartTagName=this.currentToken.tagName),this.tokenQueue.push(this.currentToken),this.currentToken=null},Me.prototype._emitCurrentCharacterToken=function(){this.currentCharacterToken&&(this.tokenQueue.push(this.currentCharacterToken),this.currentCharacterToken=null)},Me.prototype._emitEOFToken=function(){this._emitCurrentCharacterToken(),this.tokenQueue.push({type:Me.EOF_TOKEN})},Me.prototype._appendCharToCurrentCharacterToken=function(e,t){this.currentCharacterToken&&this.currentCharacterToken.type!==e&&this._emitCurrentCharacterToken(),this.currentCharacterToken?this.currentCharacterToken.chars+=t:this._createCharacterToken(e,t)},Me.prototype._emitCodePoint=function(e){var t=Me.CHARACTER_TOKEN;Ce(e)?t=Me.WHITESPACE_CHARACTER_TOKEN:e===a.NULL&&(t=Me.NULL_CHARACTER_TOKEN),this._appendCharToCurrentCharacterToken(t,be(e))},Me.prototype._emitSeveralCodePoints=function(e){for(var t=0;t<e.length;t++)this._emitCodePoint(e[t])},Me.prototype._emitChar=function(e){this._appendCharToCurrentCharacterToken(Me.CHARACTER_TOKEN,e)},Me.prototype._consumeNumericEntity=function(e){var t="",n=void 0;do{t+=be(this._consume()),n=this._lookahead()}while(n!==a.EOF&&Le(n,e));this._lookahead()===a.SEMICOLON&&this._consume();var r,s=parseInt(t,e?16:10);return l[s]||((r=s)>=55296&&r<=57343||r>1114111?a.REPLACEMENT_CHARACTER:s)},Me.prototype._consumeNamedEntity=function(e){for(var t=null,n=0,r=null,s=0,i=!1,c=0;c>-1;){var l=o[c],u=l<7;if(u&&1&l&&(t=2&l?[o[++c],o[++c]]:[o[++c]],n=s,r===a.SEMICOLON)){i=!0;break}if(s++,(r=this._consume())===a.EOF)break;c=u?4&l?Ie(c,r):-1:r===l?++c:-1}if(t){if(!i&&(this._unconsumeSeveral(s-n),e)){var p=this._lookahead();if(p===a.EQUALS_SIGN||function(e){return Se(e)||Ne(e)}(p))return this._unconsumeSeveral(n),null}return t}return this._unconsumeSeveral(s),null},Me.prototype._consumeCharacterReference=function(e,t){if(Ce(e)||e===a.GREATER_THAN_SIGN||e===a.AMPERSAND||e===this.additionalAllowedCp||e===a.EOF)return this._unconsume(),null;if(e===a.NUMBER_SIGN){var n=!1,r=this._lookahead();return r!==a.LATIN_SMALL_X&&r!==a.LATIN_CAPITAL_X||(this._consume(),n=!0),(r=this._lookahead())!==a.EOF&&Le(r,n)?[this._consumeNumericEntity(n)]:(this._unconsumeSeveral(n?2:1),null)}return this._unconsume(),this._consumeNamedEntity(t)};var ye=Me.prototype;ye[u]=function(e){this.preprocessor.dropParsedChunk(),e===a.AMPERSAND?this.state=p:e===a.LESS_THAN_SIGN?this.state=E:e===a.NULL?this._emitCodePoint(e):e===a.EOF?this._emitEOFToken():this._emitCodePoint(e)},ye[p]=function(e){this.additionalAllowedCp=void 0;var t=this._consumeCharacterReference(e,!1);this._ensureHibernation()||(t?this._emitSeveralCodePoints(t):this._emitChar("&"),this.state=u)},ye[h]=function(e){this.preprocessor.dropParsedChunk(),e===a.AMPERSAND?this.state=f:e===a.LESS_THAN_SIGN?this.state=A:e===a.NULL?this._emitChar(i.REPLACEMENT_CHARACTER):e===a.EOF?this._emitEOFToken():this._emitCodePoint(e)},ye[f]=function(e){this.additionalAllowedCp=void 0;var t=this._consumeCharacterReference(e,!1);this._ensureHibernation()||(t?this._emitSeveralCodePoints(t):this._emitChar("&"),this.state=h)},ye[T]=function(e){this.preprocessor.dropParsedChunk(),e===a.LESS_THAN_SIGN?this.state=k:e===a.NULL?this._emitChar(i.REPLACEMENT_CHARACTER):e===a.EOF?this._emitEOFToken():this._emitCodePoint(e)},ye[d]=function(e){this.preprocessor.dropParsedChunk(),e===a.LESS_THAN_SIGN?this.state=L:e===a.NULL?this._emitChar(i.REPLACEMENT_CHARACTER):e===a.EOF?this._emitEOFToken():this._emitCodePoint(e)},ye[m]=function(e){this.preprocessor.dropParsedChunk(),e===a.NULL?this._emitChar(i.REPLACEMENT_CHARACTER):e===a.EOF?this._emitEOFToken():this._emitCodePoint(e)},ye[E]=function(e){e===a.EXCLAMATION_MARK?this.state=te:e===a.SOLIDUS?this.state=_:Se(e)?(this._createStartTagToken(),this._reconsumeInState(g)):e===a.QUESTION_MARK?this._reconsumeInState(J):(this._emitChar("<"),this._reconsumeInState(u))},ye[_]=function(e){Se(e)?(this._createEndTagToken(),this._reconsumeInState(g)):e===a.GREATER_THAN_SIGN?this.state=u:e===a.EOF?(this._reconsumeInState(u),this._emitChar("<"),this._emitChar("/")):this._reconsumeInState(J)},ye[g]=function(e){Ce(e)?this.state=q:e===a.SOLIDUS?this.state=Z:e===a.GREATER_THAN_SIGN?(this.state=u,this._emitCurrentToken()):ke(e)?this.currentToken.tagName+=Re(e):e===a.NULL?this.currentToken.tagName+=i.REPLACEMENT_CHARACTER:e===a.EOF?this._reconsumeInState(u):this.currentToken.tagName+=be(e)},ye[A]=function(e){e===a.SOLIDUS?(this.tempBuff=[],this.state=C):(this._emitChar("<"),this._reconsumeInState(h))},ye[C]=function(e){Se(e)?(this._createEndTagToken(),this._reconsumeInState(N)):(this._emitChar("<"),this._emitChar("/"),this._reconsumeInState(h))},ye[N]=function(e){if(ke(e))this.currentToken.tagName+=Re(e),this.tempBuff.push(e);else if(Oe(e))this.currentToken.tagName+=be(e),this.tempBuff.push(e);else{if(this._isAppropriateEndTagToken()){if(Ce(e))return void(this.state=q);if(e===a.SOLIDUS)return void(this.state=Z);if(e===a.GREATER_THAN_SIGN)return this.state=u,void this._emitCurrentToken()}this._emitChar("<"),this._emitChar("/"),this._emitSeveralCodePoints(this.tempBuff),this._reconsumeInState(h)}},ye[k]=function(e){e===a.SOLIDUS?(this.tempBuff=[],this.state=O):(this._emitChar("<"),this._reconsumeInState(T))},ye[O]=function(e){Se(e)?(this._createEndTagToken(),this._reconsumeInState(S)):(this._emitChar("<"),this._emitChar("/"),this._reconsumeInState(T))},ye[S]=function(e){if(ke(e))this.currentToken.tagName+=Re(e),this.tempBuff.push(e);else if(Oe(e))this.currentToken.tagName+=be(e),this.tempBuff.push(e);else{if(this._isAppropriateEndTagToken()){if(Ce(e))return void(this.state=q);if(e===a.SOLIDUS)return void(this.state=Z);if(e===a.GREATER_THAN_SIGN)return this._emitCurrentToken(),void(this.state=u)}this._emitChar("<"),this._emitChar("/"),this._emitSeveralCodePoints(this.tempBuff),this._reconsumeInState(T)}},ye[L]=function(e){e===a.SOLIDUS?(this.tempBuff=[],this.state=v):e===a.EXCLAMATION_MARK?(this.state=R,this._emitChar("<"),this._emitChar("!")):(this._emitChar("<"),this._reconsumeInState(d))},ye[v]=function(e){Se(e)?(this._createEndTagToken(),this._reconsumeInState(b)):(this._emitChar("<"),this._emitChar("/"),this._reconsumeInState(d))},ye[b]=function(e){if(ke(e))this.currentToken.tagName+=Re(e),this.tempBuff.push(e);else if(Oe(e))this.currentToken.tagName+=be(e),this.tempBuff.push(e);else{if(this._isAppropriateEndTagToken()){if(Ce(e))return void(this.state=q);if(e===a.SOLIDUS)return void(this.state=Z);if(e===a.GREATER_THAN_SIGN)return this._emitCurrentToken(),void(this.state=u)}this._emitChar("<"),this._emitChar("/"),this._emitSeveralCodePoints(this.tempBuff),this._reconsumeInState(d)}},ye[R]=function(e){e===a.HYPHEN_MINUS?(this.state=I,this._emitChar("-")):this._reconsumeInState(d)},ye[I]=function(e){e===a.HYPHEN_MINUS?(this.state=D,this._emitChar("-")):this._reconsumeInState(d)},ye[M]=function(e){e===a.HYPHEN_MINUS?(this.state=y,this._emitChar("-")):e===a.LESS_THAN_SIGN?this.state=P:e===a.NULL?this._emitChar(i.REPLACEMENT_CHARACTER):e===a.EOF?this._reconsumeInState(u):this._emitCodePoint(e)},ye[y]=function(e){e===a.HYPHEN_MINUS?(this.state=D,this._emitChar("-")):e===a.LESS_THAN_SIGN?this.state=P:e===a.NULL?(this.state=M,this._emitChar(i.REPLACEMENT_CHARACTER)):e===a.EOF?this._reconsumeInState(u):(this.state=M,this._emitCodePoint(e))},ye[D]=function(e){e===a.HYPHEN_MINUS?this._emitChar("-"):e===a.LESS_THAN_SIGN?this.state=P:e===a.GREATER_THAN_SIGN?(this.state=d,this._emitChar(">")):e===a.NULL?(this.state=M,this._emitChar(i.REPLACEMENT_CHARACTER)):e===a.EOF?this._reconsumeInState(u):(this.state=M,this._emitCodePoint(e))},ye[P]=function(e){e===a.SOLIDUS?(this.tempBuff=[],this.state=x):Se(e)?(this.tempBuff=[],this._emitChar("<"),this._reconsumeInState(w)):(this._emitChar("<"),this._reconsumeInState(M))},ye[x]=function(e){Se(e)?(this._createEndTagToken(),this._reconsumeInState(H)):(this._emitChar("<"),this._emitChar("/"),this._reconsumeInState(M))},ye[H]=function(e){if(ke(e))this.currentToken.tagName+=Re(e),this.tempBuff.push(e);else if(Oe(e))this.currentToken.tagName+=be(e),this.tempBuff.push(e);else{if(this._isAppropriateEndTagToken()){if(Ce(e))return void(this.state=q);if(e===a.SOLIDUS)return void(this.state=Z);if(e===a.GREATER_THAN_SIGN)return this._emitCurrentToken(),void(this.state=u)}this._emitChar("<"),this._emitChar("/"),this._emitSeveralCodePoints(this.tempBuff),this._reconsumeInState(M)}},ye[w]=function(e){Ce(e)||e===a.SOLIDUS||e===a.GREATER_THAN_SIGN?(this.state=this.isTempBufferEqualToScriptString()?F:M,this._emitCodePoint(e)):ke(e)?(this.tempBuff.push(ve(e)),this._emitCodePoint(e)):Oe(e)?(this.tempBuff.push(e),this._emitCodePoint(e)):this._reconsumeInState(M)},ye[F]=function(e){e===a.HYPHEN_MINUS?(this.state=U,this._emitChar("-")):e===a.LESS_THAN_SIGN?(this.state=G,this._emitChar("<")):e===a.NULL?this._emitChar(i.REPLACEMENT_CHARACTER):e===a.EOF?this._reconsumeInState(u):this._emitCodePoint(e)},ye[U]=function(e){e===a.HYPHEN_MINUS?(this.state=B,this._emitChar("-")):e===a.LESS_THAN_SIGN?(this.state=G,this._emitChar("<")):e===a.NULL?(this.state=F,this._emitChar(i.REPLACEMENT_CHARACTER)):e===a.EOF?this._reconsumeInState(u):(this.state=F,this._emitCodePoint(e))},ye[B]=function(e){e===a.HYPHEN_MINUS?this._emitChar("-"):e===a.LESS_THAN_SIGN?(this.state=G,this._emitChar("<")):e===a.GREATER_THAN_SIGN?(this.state=d,this._emitChar(">")):e===a.NULL?(this.state=F,this._emitChar(i.REPLACEMENT_CHARACTER)):e===a.EOF?this._reconsumeInState(u):(this.state=F,this._emitCodePoint(e))},ye[G]=function(e){e===a.SOLIDUS?(this.tempBuff=[],this.state=K,this._emitChar("/")):this._reconsumeInState(F)},ye[K]=function(e){Ce(e)||e===a.SOLIDUS||e===a.GREATER_THAN_SIGN?(this.state=this.isTempBufferEqualToScriptString()?M:F,this._emitCodePoint(e)):ke(e)?(this.tempBuff.push(ve(e)),this._emitCodePoint(e)):Oe(e)?(this.tempBuff.push(e),this._emitCodePoint(e)):this._reconsumeInState(F)},ye[q]=function(e){Ce(e)||(e===a.SOLIDUS||e===a.GREATER_THAN_SIGN||e===a.EOF?this._reconsumeInState(j):e===a.EQUALS_SIGN?(this._createAttr("="),this.state=z):(this._createAttr(""),this._reconsumeInState(z)))},ye[z]=function(e){Ce(e)||e===a.SOLIDUS||e===a.GREATER_THAN_SIGN||e===a.EOF?(this._leaveAttrName(j),this._unconsume()):e===a.EQUALS_SIGN?this._leaveAttrName(Y):ke(e)?this.currentAttr.name+=Re(e):e===a.QUOTATION_MARK||e===a.APOSTROPHE||e===a.LESS_THAN_SIGN?this.currentAttr.name+=be(e):e===a.NULL?this.currentAttr.name+=i.REPLACEMENT_CHARACTER:this.currentAttr.name+=be(e)},ye[j]=function(e){Ce(e)||(e===a.SOLIDUS?this.state=Z:e===a.EQUALS_SIGN?this.state=Y:e===a.GREATER_THAN_SIGN?(this.state=u,this._emitCurrentToken()):e===a.EOF?this._reconsumeInState(u):(this._createAttr(""),this._reconsumeInState(z)))},ye[Y]=function(e){Ce(e)||(e===a.QUOTATION_MARK?this.state=V:e===a.APOSTROPHE?this.state=W:this._reconsumeInState(Q))},ye[V]=function(e){e===a.QUOTATION_MARK?this.state=$:e===a.AMPERSAND?(this.additionalAllowedCp=a.QUOTATION_MARK,this.returnState=this.state,this.state=X):e===a.NULL?this.currentAttr.value+=i.REPLACEMENT_CHARACTER:e===a.EOF?this._reconsumeInState(u):this.currentAttr.value+=be(e)},ye[W]=function(e){e===a.APOSTROPHE?this.state=$:e===a.AMPERSAND?(this.additionalAllowedCp=a.APOSTROPHE,this.returnState=this.state,this.state=X):e===a.NULL?this.currentAttr.value+=i.REPLACEMENT_CHARACTER:e===a.EOF?this._reconsumeInState(u):this.currentAttr.value+=be(e)},ye[Q]=function(e){Ce(e)?this._leaveAttrValue(q):e===a.AMPERSAND?(this.additionalAllowedCp=a.GREATER_THAN_SIGN,this.returnState=this.state,this.state=X):e===a.GREATER_THAN_SIGN?(this._leaveAttrValue(u),this._emitCurrentToken()):e===a.NULL?this.currentAttr.value+=i.REPLACEMENT_CHARACTER:e===a.QUOTATION_MARK||e===a.APOSTROPHE||e===a.LESS_THAN_SIGN||e===a.EQUALS_SIGN||e===a.GRAVE_ACCENT?this.currentAttr.value+=be(e):e===a.EOF?this._reconsumeInState(u):this.currentAttr.value+=be(e)},ye[X]=function(e){var t=this._consumeCharacterReference(e,!0);if(!this._ensureHibernation()){if(t)for(var n=0;n<t.length;n++)this.currentAttr.value+=be(t[n]);else this.currentAttr.value+="&";this.state=this.returnState}},ye[$]=function(e){Ce(e)?this._leaveAttrValue(q):e===a.SOLIDUS?this._leaveAttrValue(Z):e===a.GREATER_THAN_SIGN?(this._leaveAttrValue(u),this._emitCurrentToken()):e===a.EOF?this._reconsumeInState(u):this._reconsumeInState(q)},ye[Z]=function(e){e===a.GREATER_THAN_SIGN?(this.currentToken.selfClosing=!0,this.state=u,this._emitCurrentToken()):e===a.EOF?this._reconsumeInState(u):this._reconsumeInState(q)},ye[J]=function(){this._createCommentToken(),this._reconsumeInState(ee)},ye[ee]=function(e){for(;;){if(e===a.GREATER_THAN_SIGN){this.state=u;break}if(e===a.EOF){this._reconsumeInState(u);break}if(this.currentToken.data+=e===a.NULL?i.REPLACEMENT_CHARACTER:be(e),this._hibernationSnapshot(),e=this._consume(),this._ensureHibernation())return}this._emitCurrentToken()},ye[te]=function(e){var t=this._consumeSubsequentIfMatch(c.DASH_DASH_STRING,e,!0),n=!t&&this._consumeSubsequentIfMatch(c.DOCTYPE_STRING,e,!1),r=!t&&!n&&this.allowCDATA&&this._consumeSubsequentIfMatch(c.CDATA_START_STRING,e,!0);this._ensureHibernation()||(t?(this._createCommentToken(),this.state=ne):n?this.state=ce:r?this.state=Ae:this._reconsumeInState(J))},ye[ne]=function(e){e===a.HYPHEN_MINUS?this.state=re:e===a.NULL?(this.currentToken.data+=i.REPLACEMENT_CHARACTER,this.state=se):e===a.GREATER_THAN_SIGN?(this.state=u,this._emitCurrentToken()):e===a.EOF?(this._emitCurrentToken(),this._reconsumeInState(u)):(this.currentToken.data+=be(e),this.state=se)},ye[re]=function(e){e===a.HYPHEN_MINUS?this.state=oe:e===a.NULL?(this.currentToken.data+="-",this.currentToken.data+=i.REPLACEMENT_CHARACTER,this.state=se):e===a.GREATER_THAN_SIGN?(this.state=u,this._emitCurrentToken()):e===a.EOF?(this._emitCurrentToken(),this._reconsumeInState(u)):(this.currentToken.data+="-",this.currentToken.data+=be(e),this.state=se)},ye[se]=function(e){e===a.HYPHEN_MINUS?this.state=ie:e===a.NULL?this.currentToken.data+=i.REPLACEMENT_CHARACTER:e===a.EOF?(this._emitCurrentToken(),this._reconsumeInState(u)):this.currentToken.data+=be(e)},ye[ie]=function(e){e===a.HYPHEN_MINUS?this.state=oe:e===a.NULL?(this.currentToken.data+="-",this.currentToken.data+=i.REPLACEMENT_CHARACTER,this.state=se):e===a.EOF?(this._emitCurrentToken(),this._reconsumeInState(u)):(this.currentToken.data+="-",this.currentToken.data+=be(e),this.state=se)},ye[oe]=function(e){e===a.GREATER_THAN_SIGN?(this.state=u,this._emitCurrentToken()):e===a.EXCLAMATION_MARK?this.state=ae:e===a.HYPHEN_MINUS?this.currentToken.data+="-":e===a.NULL?(this.currentToken.data+="--",this.currentToken.data+=i.REPLACEMENT_CHARACTER,this.state=se):e===a.EOF?(this._reconsumeInState(u),this._emitCurrentToken()):(this.currentToken.data+="--",this.currentToken.data+=be(e),this.state=se)},ye[ae]=function(e){e===a.HYPHEN_MINUS?(this.currentToken.data+="--!",this.state=ie):e===a.GREATER_THAN_SIGN?(this.state=u,this._emitCurrentToken()):e===a.NULL?(this.currentToken.data+="--!",this.currentToken.data+=i.REPLACEMENT_CHARACTER,this.state=se):e===a.EOF?(this._emitCurrentToken(),this._reconsumeInState(u)):(this.currentToken.data+="--!",this.currentToken.data+=be(e),this.state=se)},ye[ce]=function(e){Ce(e)||(e===a.GREATER_THAN_SIGN?(this._createDoctypeToken(null),this.currentToken.forceQuirks=!0,this._emitCurrentToken(),this.state=u):e===a.EOF?(this._createDoctypeToken(null),this.currentToken.forceQuirks=!0,this._emitCurrentToken(),this._reconsumeInState(u)):(this._createDoctypeToken(""),this._reconsumeInState(le)))},ye[le]=function(e){Ce(e)||e===a.GREATER_THAN_SIGN||e===a.EOF?this._reconsumeInState(ue):ke(e)?this.currentToken.name+=Re(e):e===a.NULL?this.currentToken.name+=i.REPLACEMENT_CHARACTER:this.currentToken.name+=be(e)},ye[ue]=function(e){if(!Ce(e))if(e===a.GREATER_THAN_SIGN)this.state=u,this._emitCurrentToken();else{var t=this._consumeSubsequentIfMatch(c.PUBLIC_STRING,e,!1),n=!t&&this._consumeSubsequentIfMatch(c.SYSTEM_STRING,e,!1);this._ensureHibernation()||(t?this.state=pe:n?this.state=de:(this.currentToken.forceQuirks=!0,this.state=ge))}},ye[pe]=function(e){Ce(e)||(e===a.QUOTATION_MARK?(this.currentToken.publicId="",this.state=he):e===a.APOSTROPHE?(this.currentToken.publicId="",this.state=fe):(this.currentToken.forceQuirks=!0,this._reconsumeInState(ge)))},ye[he]=function(e){e===a.QUOTATION_MARK?this.state=Te:e===a.NULL?this.currentToken.publicId+=i.REPLACEMENT_CHARACTER:e===a.GREATER_THAN_SIGN?(this.currentToken.forceQuirks=!0,this._emitCurrentToken(),this.state=u):e===a.EOF?(this.currentToken.forceQuirks=!0,this._emitCurrentToken(),this._reconsumeInState(u)):this.currentToken.publicId+=be(e)},ye[fe]=function(e){e===a.APOSTROPHE?this.state=Te:e===a.NULL?this.currentToken.publicId+=i.REPLACEMENT_CHARACTER:e===a.GREATER_THAN_SIGN?(this.currentToken.forceQuirks=!0,this._emitCurrentToken(),this.state=u):e===a.EOF?(this.currentToken.forceQuirks=!0,this._emitCurrentToken(),this._reconsumeInState(u)):this.currentToken.publicId+=be(e)},ye[Te]=function(e){Ce(e)||(e===a.GREATER_THAN_SIGN?(this._emitCurrentToken(),this.state=u):e===a.QUOTATION_MARK?(this.currentToken.systemId="",this.state=me):e===a.APOSTROPHE?(this.currentToken.systemId="",this.state=Ee):(this.currentToken.forceQuirks=!0,this._reconsumeInState(ge)))},ye[de]=function(e){Ce(e)||(e===a.QUOTATION_MARK?(this.currentToken.systemId="",this.state=me):e===a.APOSTROPHE?(this.currentToken.systemId="",this.state=Ee):(this.currentToken.forceQuirks=!0,this._reconsumeInState(ge)))},ye[me]=function(e){e===a.QUOTATION_MARK?this.state=_e:e===a.GREATER_THAN_SIGN?(this.currentToken.forceQuirks=!0,this._emitCurrentToken(),this.state=u):e===a.NULL?this.currentToken.systemId+=i.REPLACEMENT_CHARACTER:e===a.EOF?(this.currentToken.forceQuirks=!0,this._emitCurrentToken(),this._reconsumeInState(u)):this.currentToken.systemId+=be(e)},ye[Ee]=function(e){e===a.APOSTROPHE?this.state=_e:e===a.GREATER_THAN_SIGN?(this.currentToken.forceQuirks=!0,this._emitCurrentToken(),this.state=u):e===a.NULL?this.currentToken.systemId+=i.REPLACEMENT_CHARACTER:e===a.EOF?(this.currentToken.forceQuirks=!0,this._emitCurrentToken(),this._reconsumeInState(u)):this.currentToken.systemId+=be(e)},ye[_e]=function(e){Ce(e)||(e===a.GREATER_THAN_SIGN?(this._emitCurrentToken(),this.state=u):e===a.EOF?(this.currentToken.forceQuirks=!0,this._emitCurrentToken(),this._reconsumeInState(u)):this.state=ge)},ye[ge]=function(e){e===a.GREATER_THAN_SIGN?(this._emitCurrentToken(),this.state=u):e===a.EOF&&(this._emitCurrentToken(),this._reconsumeInState(u))},ye[Ae]=function(e){for(;;){if(e===a.EOF){this._reconsumeInState(u);break}var t=this._consumeSubsequentIfMatch(c.CDATA_END_STRING,e,!0);if(this._ensureHibernation())break;if(t){this.state=u;break}if(this._emitCodePoint(e),this._hibernationSnapshot(),e=this._consume(),this._ensureHibernation())break}}},1763:e=>{"use strict";e.exports=new Uint16Array([4,52,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,106,303,412,810,1432,1701,1796,1987,2114,2360,2420,2484,3170,3251,4140,4393,4575,4610,5106,5512,5728,6117,6274,6315,6345,6427,6516,7002,7910,8733,9323,9870,10170,10631,10893,11318,11386,11467,12773,13092,14474,14922,15448,15542,16419,17666,18166,18611,19004,19095,19298,19397,4,16,69,77,97,98,99,102,103,108,109,110,111,112,114,115,116,117,140,150,158,169,176,194,199,210,216,222,226,242,256,266,283,294,108,105,103,5,198,1,59,148,1,198,80,5,38,1,59,156,1,38,99,117,116,101,5,193,1,59,167,1,193,114,101,118,101,59,1,258,4,2,105,121,182,191,114,99,5,194,1,59,189,1,194,59,1,1040,114,59,3,55349,56580,114,97,118,101,5,192,1,59,208,1,192,112,104,97,59,1,913,97,99,114,59,1,256,100,59,1,10835,4,2,103,112,232,237,111,110,59,1,260,102,59,3,55349,56632,112,108,121,70,117,110,99,116,105,111,110,59,1,8289,105,110,103,5,197,1,59,264,1,197,4,2,99,115,272,277,114,59,3,55349,56476,105,103,110,59,1,8788,105,108,100,101,5,195,1,59,292,1,195,109,108,5,196,1,59,301,1,196,4,8,97,99,101,102,111,114,115,117,321,350,354,383,388,394,400,405,4,2,99,114,327,336,107,115,108,97,115,104,59,1,8726,4,2,118,119,342,345,59,1,10983,101,100,59,1,8966,121,59,1,1041,4,3,99,114,116,362,369,379,97,117,115,101,59,1,8757,110,111,117,108,108,105,115,59,1,8492,97,59,1,914,114,59,3,55349,56581,112,102,59,3,55349,56633,101,118,101,59,1,728,99,114,59,1,8492,109,112,101,113,59,1,8782,4,14,72,79,97,99,100,101,102,104,105,108,111,114,115,117,442,447,456,504,542,547,569,573,577,616,678,784,790,796,99,121,59,1,1063,80,89,5,169,1,59,454,1,169,4,3,99,112,121,464,470,497,117,116,101,59,1,262,4,2,59,105,476,478,1,8914,116,97,108,68,105,102,102,101,114,101,110,116,105,97,108,68,59,1,8517,108,101,121,115,59,1,8493,4,4,97,101,105,111,514,520,530,535,114,111,110,59,1,268,100,105,108,5,199,1,59,528,1,199,114,99,59,1,264,110,105,110,116,59,1,8752,111,116,59,1,266,4,2,100,110,553,560,105,108,108,97,59,1,184,116,101,114,68,111,116,59,1,183,114,59,1,8493,105,59,1,935,114,99,108,101,4,4,68,77,80,84,591,596,603,609,111,116,59,1,8857,105,110,117,115,59,1,8854,108,117,115,59,1,8853,105,109,101,115,59,1,8855,111,4,2,99,115,623,646,107,119,105,115,101,67,111,110,116,111,117,114,73,110,116,101,103,114,97,108,59,1,8754,101,67,117,114,108,121,4,2,68,81,658,671,111,117,98,108,101,81,117,111,116,101,59,1,8221,117,111,116,101,59,1,8217,4,4,108,110,112,117,688,701,736,753,111,110,4,2,59,101,696,698,1,8759,59,1,10868,4,3,103,105,116,709,717,722,114,117,101,110,116,59,1,8801,110,116,59,1,8751,111,117,114,73,110,116,101,103,114,97,108,59,1,8750,4,2,102,114,742,745,59,1,8450,111,100,117,99,116,59,1,8720,110,116,101,114,67,108,111,99,107,119,105,115,101,67,111,110,116,111,117,114,73,110,116,101,103,114,97,108,59,1,8755,111,115,115,59,1,10799,99,114,59,3,55349,56478,112,4,2,59,67,803,805,1,8915,97,112,59,1,8781,4,11,68,74,83,90,97,99,101,102,105,111,115,834,850,855,860,865,888,903,916,921,1011,1415,4,2,59,111,840,842,1,8517,116,114,97,104,100,59,1,10513,99,121,59,1,1026,99,121,59,1,1029,99,121,59,1,1039,4,3,103,114,115,873,879,883,103,101,114,59,1,8225,114,59,1,8609,104,118,59,1,10980,4,2,97,121,894,900,114,111,110,59,1,270,59,1,1044,108,4,2,59,116,910,912,1,8711,97,59,1,916,114,59,3,55349,56583,4,2,97,102,927,998,4,2,99,109,933,992,114,105,116,105,99,97,108,4,4,65,68,71,84,950,957,978,985,99,117,116,101,59,1,180,111,4,2,116,117,964,967,59,1,729,98,108,101,65,99,117,116,101,59,1,733,114,97,118,101,59,1,96,105,108,100,101,59,1,732,111,110,100,59,1,8900,102,101,114,101,110,116,105,97,108,68,59,1,8518,4,4,112,116,117,119,1021,1026,1048,1249,102,59,3,55349,56635,4,3,59,68,69,1034,1036,1041,1,168,111,116,59,1,8412,113,117,97,108,59,1,8784,98,108,101,4,6,67,68,76,82,85,86,1065,1082,1101,1189,1211,1236,111,110,116,111,117,114,73,110,116,101,103,114,97,108,59,1,8751,111,4,2,116,119,1089,1092,59,1,168,110,65,114,114,111,119,59,1,8659,4,2,101,111,1107,1141,102,116,4,3,65,82,84,1117,1124,1136,114,114,111,119,59,1,8656,105,103,104,116,65,114,114,111,119,59,1,8660,101,101,59,1,10980,110,103,4,2,76,82,1149,1177,101,102,116,4,2,65,82,1158,1165,114,114,111,119,59,1,10232,105,103,104,116,65,114,114,111,119,59,1,10234,105,103,104,116,65,114,114,111,119,59,1,10233,105,103,104,116,4,2,65,84,1199,1206,114,114,111,119,59,1,8658,101,101,59,1,8872,112,4,2,65,68,1218,1225,114,114,111,119,59,1,8657,111,119,110,65,114,114,111,119,59,1,8661,101,114,116,105,99,97,108,66,97,114,59,1,8741,110,4,6,65,66,76,82,84,97,1264,1292,1299,1352,1391,1408,114,114,111,119,4,3,59,66,85,1276,1278,1283,1,8595,97,114,59,1,10515,112,65,114,114,111,119,59,1,8693,114,101,118,101,59,1,785,101,102,116,4,3,82,84,86,1310,1323,1334,105,103,104,116,86,101,99,116,111,114,59,1,10576,101,101,86,101,99,116,111,114,59,1,10590,101,99,116,111,114,4,2,59,66,1345,1347,1,8637,97,114,59,1,10582,105,103,104,116,4,2,84,86,1362,1373,101,101,86,101,99,116,111,114,59,1,10591,101,99,116,111,114,4,2,59,66,1384,1386,1,8641,97,114,59,1,10583,101,101,4,2,59,65,1399,1401,1,8868,114,114,111,119,59,1,8615,114,114,111,119,59,1,8659,4,2,99,116,1421,1426,114,59,3,55349,56479,114,111,107,59,1,272,4,16,78,84,97,99,100,102,103,108,109,111,112,113,115,116,117,120,1466,1470,1478,1489,1515,1520,1525,1536,1544,1593,1609,1617,1650,1664,1668,1677,71,59,1,330,72,5,208,1,59,1476,1,208,99,117,116,101,5,201,1,59,1487,1,201,4,3,97,105,121,1497,1503,1512,114,111,110,59,1,282,114,99,5,202,1,59,1510,1,202,59,1,1069,111,116,59,1,278,114,59,3,55349,56584,114,97,118,101,5,200,1,59,1534,1,200,101,109,101,110,116,59,1,8712,4,2,97,112,1550,1555,99,114,59,1,274,116,121,4,2,83,86,1563,1576,109,97,108,108,83,113,117,97,114,101,59,1,9723,101,114,121,83,109,97,108,108,83,113,117,97,114,101,59,1,9643,4,2,103,112,1599,1604,111,110,59,1,280,102,59,3,55349,56636,115,105,108,111,110,59,1,917,117,4,2,97,105,1624,1640,108,4,2,59,84,1631,1633,1,10869,105,108,100,101,59,1,8770,108,105,98,114,105,117,109,59,1,8652,4,2,99,105,1656,1660,114,59,1,8496,109,59,1,10867,97,59,1,919,109,108,5,203,1,59,1675,1,203,4,2,105,112,1683,1689,115,116,115,59,1,8707,111,110,101,110,116,105,97,108,69,59,1,8519,4,5,99,102,105,111,115,1713,1717,1722,1762,1791,121,59,1,1060,114,59,3,55349,56585,108,108,101,100,4,2,83,86,1732,1745,109,97,108,108,83,113,117,97,114,101,59,1,9724,101,114,121,83,109,97,108,108,83,113,117,97,114,101,59,1,9642,4,3,112,114,117,1770,1775,1781,102,59,3,55349,56637,65,108,108,59,1,8704,114,105,101,114,116,114,102,59,1,8497,99,114,59,1,8497,4,12,74,84,97,98,99,100,102,103,111,114,115,116,1822,1827,1834,1848,1855,1877,1882,1887,1890,1896,1978,1984,99,121,59,1,1027,5,62,1,59,1832,1,62,109,109,97,4,2,59,100,1843,1845,1,915,59,1,988,114,101,118,101,59,1,286,4,3,101,105,121,1863,1869,1874,100,105,108,59,1,290,114,99,59,1,284,59,1,1043,111,116,59,1,288,114,59,3,55349,56586,59,1,8921,112,102,59,3,55349,56638,101,97,116,101,114,4,6,69,70,71,76,83,84,1915,1933,1944,1953,1959,1971,113,117,97,108,4,2,59,76,1925,1927,1,8805,101,115,115,59,1,8923,117,108,108,69,113,117,97,108,59,1,8807,114,101,97,116,101,114,59,1,10914,101,115,115,59,1,8823,108,97,110,116,69,113,117,97,108,59,1,10878,105,108,100,101,59,1,8819,99,114,59,3,55349,56482,59,1,8811,4,8,65,97,99,102,105,111,115,117,2005,2012,2026,2032,2036,2049,2073,2089,82,68,99,121,59,1,1066,4,2,99,116,2018,2023,101,107,59,1,711,59,1,94,105,114,99,59,1,292,114,59,1,8460,108,98,101,114,116,83,112,97,99,101,59,1,8459,4,2,112,114,2055,2059,102,59,1,8461,105,122,111,110,116,97,108,76,105,110,101,59,1,9472,4,2,99,116,2079,2083,114,59,1,8459,114,111,107,59,1,294,109,112,4,2,68,69,2097,2107,111,119,110,72,117,109,112,59,1,8782,113,117,97,108,59,1,8783,4,14,69,74,79,97,99,100,102,103,109,110,111,115,116,117,2144,2149,2155,2160,2171,2189,2194,2198,2209,2245,2307,2329,2334,2341,99,121,59,1,1045,108,105,103,59,1,306,99,121,59,1,1025,99,117,116,101,5,205,1,59,2169,1,205,4,2,105,121,2177,2186,114,99,5,206,1,59,2184,1,206,59,1,1048,111,116,59,1,304,114,59,1,8465,114,97,118,101,5,204,1,59,2207,1,204,4,3,59,97,112,2217,2219,2238,1,8465,4,2,99,103,2225,2229,114,59,1,298,105,110,97,114,121,73,59,1,8520,108,105,101,115,59,1,8658,4,2,116,118,2251,2281,4,2,59,101,2257,2259,1,8748,4,2,103,114,2265,2271,114,97,108,59,1,8747,115,101,99,116,105,111,110,59,1,8898,105,115,105,98,108,101,4,2,67,84,2293,2300,111,109,109,97,59,1,8291,105,109,101,115,59,1,8290,4,3,103,112,116,2315,2320,2325,111,110,59,1,302,102,59,3,55349,56640,97,59,1,921,99,114,59,1,8464,105,108,100,101,59,1,296,4,2,107,109,2347,2352,99,121,59,1,1030,108,5,207,1,59,2358,1,207,4,5,99,102,111,115,117,2372,2386,2391,2397,2414,4,2,105,121,2378,2383,114,99,59,1,308,59,1,1049,114,59,3,55349,56589,112,102,59,3,55349,56641,4,2,99,101,2403,2408,114,59,3,55349,56485,114,99,121,59,1,1032,107,99,121,59,1,1028,4,7,72,74,97,99,102,111,115,2436,2441,2446,2452,2467,2472,2478,99,121,59,1,1061,99,121,59,1,1036,112,112,97,59,1,922,4,2,101,121,2458,2464,100,105,108,59,1,310,59,1,1050,114,59,3,55349,56590,112,102,59,3,55349,56642,99,114,59,3,55349,56486,4,11,74,84,97,99,101,102,108,109,111,115,116,2508,2513,2520,2562,2585,2981,2986,3004,3011,3146,3167,99,121,59,1,1033,5,60,1,59,2518,1,60,4,5,99,109,110,112,114,2532,2538,2544,2548,2558,117,116,101,59,1,313,98,100,97,59,1,923,103,59,1,10218,108,97,99,101,116,114,102,59,1,8466,114,59,1,8606,4,3,97,101,121,2570,2576,2582,114,111,110,59,1,317,100,105,108,59,1,315,59,1,1051,4,2,102,115,2591,2907,116,4,10,65,67,68,70,82,84,85,86,97,114,2614,2663,2672,2728,2735,2760,2820,2870,2888,2895,4,2,110,114,2620,2633,103,108,101,66,114,97,99,107,101,116,59,1,10216,114,111,119,4,3,59,66,82,2644,2646,2651,1,8592,97,114,59,1,8676,105,103,104,116,65,114,114,111,119,59,1,8646,101,105,108,105,110,103,59,1,8968,111,4,2,117,119,2679,2692,98,108,101,66,114,97,99,107,101,116,59,1,10214,110,4,2,84,86,2699,2710,101,101,86,101,99,116,111,114,59,1,10593,101,99,116,111,114,4,2,59,66,2721,2723,1,8643,97,114,59,1,10585,108,111,111,114,59,1,8970,105,103,104,116,4,2,65,86,2745,2752,114,114,111,119,59,1,8596,101,99,116,111,114,59,1,10574,4,2,101,114,2766,2792,101,4,3,59,65,86,2775,2777,2784,1,8867,114,114,111,119,59,1,8612,101,99,116,111,114,59,1,10586,105,97,110,103,108,101,4,3,59,66,69,2806,2808,2813,1,8882,97,114,59,1,10703,113,117,97,108,59,1,8884,112,4,3,68,84,86,2829,2841,2852,111,119,110,86,101,99,116,111,114,59,1,10577,101,101,86,101,99,116,111,114,59,1,10592,101,99,116,111,114,4,2,59,66,2863,2865,1,8639,97,114,59,1,10584,101,99,116,111,114,4,2,59,66,2881,2883,1,8636,97,114,59,1,10578,114,114,111,119,59,1,8656,105,103,104,116,97,114,114,111,119,59,1,8660,115,4,6,69,70,71,76,83,84,2922,2936,2947,2956,2962,2974,113,117,97,108,71,114,101,97,116,101,114,59,1,8922,117,108,108,69,113,117,97,108,59,1,8806,114,101,97,116,101,114,59,1,8822,101,115,115,59,1,10913,108,97,110,116,69,113,117,97,108,59,1,10877,105,108,100,101,59,1,8818,114,59,3,55349,56591,4,2,59,101,2992,2994,1,8920,102,116,97,114,114,111,119,59,1,8666,105,100,111,116,59,1,319,4,3,110,112,119,3019,3110,3115,103,4,4,76,82,108,114,3030,3058,3070,3098,101,102,116,4,2,65,82,3039,3046,114,114,111,119,59,1,10229,105,103,104,116,65,114,114,111,119,59,1,10231,105,103,104,116,65,114,114,111,119,59,1,10230,101,102,116,4,2,97,114,3079,3086,114,114,111,119,59,1,10232,105,103,104,116,97,114,114,111,119,59,1,10234,105,103,104,116,97,114,114,111,119,59,1,10233,102,59,3,55349,56643,101,114,4,2,76,82,3123,3134,101,102,116,65,114,114,111,119,59,1,8601,105,103,104,116,65,114,114,111,119,59,1,8600,4,3,99,104,116,3154,3158,3161,114,59,1,8466,59,1,8624,114,111,107,59,1,321,59,1,8810,4,8,97,99,101,102,105,111,115,117,3188,3192,3196,3222,3227,3237,3243,3248,112,59,1,10501,121,59,1,1052,4,2,100,108,3202,3213,105,117,109,83,112,97,99,101,59,1,8287,108,105,110,116,114,102,59,1,8499,114,59,3,55349,56592,110,117,115,80,108,117,115,59,1,8723,112,102,59,3,55349,56644,99,114,59,1,8499,59,1,924,4,9,74,97,99,101,102,111,115,116,117,3271,3276,3283,3306,3422,3427,4120,4126,4137,99,121,59,1,1034,99,117,116,101,59,1,323,4,3,97,101,121,3291,3297,3303,114,111,110,59,1,327,100,105,108,59,1,325,59,1,1053,4,3,103,115,119,3314,3380,3415,97,116,105,118,101,4,3,77,84,86,3327,3340,3365,101,100,105,117,109,83,112,97,99,101,59,1,8203,104,105,4,2,99,110,3348,3357,107,83,112,97,99,101,59,1,8203,83,112,97,99,101,59,1,8203,101,114,121,84,104,105,110,83,112,97,99,101,59,1,8203,116,101,100,4,2,71,76,3389,3405,114,101,97,116,101,114,71,114,101,97,116,101,114,59,1,8811,101,115,115,76,101,115,115,59,1,8810,76,105,110,101,59,1,10,114,59,3,55349,56593,4,4,66,110,112,116,3437,3444,3460,3464,114,101,97,107,59,1,8288,66,114,101,97,107,105,110,103,83,112,97,99,101,59,1,160,102,59,1,8469,4,13,59,67,68,69,71,72,76,78,80,82,83,84,86,3492,3494,3517,3536,3578,3657,3685,3784,3823,3860,3915,4066,4107,1,10988,4,2,111,117,3500,3510,110,103,114,117,101,110,116,59,1,8802,112,67,97,112,59,1,8813,111,117,98,108,101,86,101,114,116,105,99,97,108,66,97,114,59,1,8742,4,3,108,113,120,3544,3552,3571,101,109,101,110,116,59,1,8713,117,97,108,4,2,59,84,3561,3563,1,8800,105,108,100,101,59,3,8770,824,105,115,116,115,59,1,8708,114,101,97,116,101,114,4,7,59,69,70,71,76,83,84,3600,3602,3609,3621,3631,3637,3650,1,8815,113,117,97,108,59,1,8817,117,108,108,69,113,117,97,108,59,3,8807,824,114,101,97,116,101,114,59,3,8811,824,101,115,115,59,1,8825,108,97,110,116,69,113,117,97,108,59,3,10878,824,105,108,100,101,59,1,8821,117,109,112,4,2,68,69,3666,3677,111,119,110,72,117,109,112,59,3,8782,824,113,117,97,108,59,3,8783,824,101,4,2,102,115,3692,3724,116,84,114,105,97,110,103,108,101,4,3,59,66,69,3709,3711,3717,1,8938,97,114,59,3,10703,824,113,117,97,108,59,1,8940,115,4,6,59,69,71,76,83,84,3739,3741,3748,3757,3764,3777,1,8814,113,117,97,108,59,1,8816,114,101,97,116,101,114,59,1,8824,101,115,115,59,3,8810,824,108,97,110,116,69,113,117,97,108,59,3,10877,824,105,108,100,101,59,1,8820,101,115,116,101,100,4,2,71,76,3795,3812,114,101,97,116,101,114,71,114,101,97,116,101,114,59,3,10914,824,101,115,115,76,101,115,115,59,3,10913,824,114,101,99,101,100,101,115,4,3,59,69,83,3838,3840,3848,1,8832,113,117,97,108,59,3,10927,824,108,97,110,116,69,113,117,97,108,59,1,8928,4,2,101,105,3866,3881,118,101,114,115,101,69,108,101,109,101,110,116,59,1,8716,103,104,116,84,114,105,97,110,103,108,101,4,3,59,66,69,3900,3902,3908,1,8939,97,114,59,3,10704,824,113,117,97,108,59,1,8941,4,2,113,117,3921,3973,117,97,114,101,83,117,4,2,98,112,3933,3952,115,101,116,4,2,59,69,3942,3945,3,8847,824,113,117,97,108,59,1,8930,101,114,115,101,116,4,2,59,69,3963,3966,3,8848,824,113,117,97,108,59,1,8931,4,3,98,99,112,3981,4e3,4045,115,101,116,4,2,59,69,3990,3993,3,8834,8402,113,117,97,108,59,1,8840,99,101,101,100,115,4,4,59,69,83,84,4015,4017,4025,4037,1,8833,113,117,97,108,59,3,10928,824,108,97,110,116,69,113,117,97,108,59,1,8929,105,108,100,101,59,3,8831,824,101,114,115,101,116,4,2,59,69,4056,4059,3,8835,8402,113,117,97,108,59,1,8841,105,108,100,101,4,4,59,69,70,84,4080,4082,4089,4100,1,8769,113,117,97,108,59,1,8772,117,108,108,69,113,117,97,108,59,1,8775,105,108,100,101,59,1,8777,101,114,116,105,99,97,108,66,97,114,59,1,8740,99,114,59,3,55349,56489,105,108,100,101,5,209,1,59,4135,1,209,59,1,925,4,14,69,97,99,100,102,103,109,111,112,114,115,116,117,118,4170,4176,4187,4205,4212,4217,4228,4253,4259,4292,4295,4316,4337,4346,108,105,103,59,1,338,99,117,116,101,5,211,1,59,4185,1,211,4,2,105,121,4193,4202,114,99,5,212,1,59,4200,1,212,59,1,1054,98,108,97,99,59,1,336,114,59,3,55349,56594,114,97,118,101,5,210,1,59,4226,1,210,4,3,97,101,105,4236,4241,4246,99,114,59,1,332,103,97,59,1,937,99,114,111,110,59,1,927,112,102,59,3,55349,56646,101,110,67,117,114,108,121,4,2,68,81,4272,4285,111,117,98,108,101,81,117,111,116,101,59,1,8220,117,111,116,101,59,1,8216,59,1,10836,4,2,99,108,4301,4306,114,59,3,55349,56490,97,115,104,5,216,1,59,4314,1,216,105,4,2,108,109,4323,4332,100,101,5,213,1,59,4330,1,213,101,115,59,1,10807,109,108,5,214,1,59,4344,1,214,101,114,4,2,66,80,4354,4380,4,2,97,114,4360,4364,114,59,1,8254,97,99,4,2,101,107,4372,4375,59,1,9182,101,116,59,1,9140,97,114,101,110,116,104,101,115,105,115,59,1,9180,4,9,97,99,102,104,105,108,111,114,115,4413,4422,4426,4431,4435,4438,4448,4471,4561,114,116,105,97,108,68,59,1,8706,121,59,1,1055,114,59,3,55349,56595,105,59,1,934,59,1,928,117,115,77,105,110,117,115,59,1,177,4,2,105,112,4454,4467,110,99,97,114,101,112,108,97,110,101,59,1,8460,102,59,1,8473,4,4,59,101,105,111,4481,4483,4526,4531,1,10939,99,101,100,101,115,4,4,59,69,83,84,4498,4500,4507,4519,1,8826,113,117,97,108,59,1,10927,108,97,110,116,69,113,117,97,108,59,1,8828,105,108,100,101,59,1,8830,109,101,59,1,8243,4,2,100,112,4537,4543,117,99,116,59,1,8719,111,114,116,105,111,110,4,2,59,97,4555,4557,1,8759,108,59,1,8733,4,2,99,105,4567,4572,114,59,3,55349,56491,59,1,936,4,4,85,102,111,115,4585,4594,4599,4604,79,84,5,34,1,59,4592,1,34,114,59,3,55349,56596,112,102,59,1,8474,99,114,59,3,55349,56492,4,12,66,69,97,99,101,102,104,105,111,114,115,117,4636,4642,4650,4681,4704,4763,4767,4771,5047,5069,5081,5094,97,114,114,59,1,10512,71,5,174,1,59,4648,1,174,4,3,99,110,114,4658,4664,4668,117,116,101,59,1,340,103,59,1,10219,114,4,2,59,116,4675,4677,1,8608,108,59,1,10518,4,3,97,101,121,4689,4695,4701,114,111,110,59,1,344,100,105,108,59,1,342,59,1,1056,4,2,59,118,4710,4712,1,8476,101,114,115,101,4,2,69,85,4722,4748,4,2,108,113,4728,4736,101,109,101,110,116,59,1,8715,117,105,108,105,98,114,105,117,109,59,1,8651,112,69,113,117,105,108,105,98,114,105,117,109,59,1,10607,114,59,1,8476,111,59,1,929,103,104,116,4,8,65,67,68,70,84,85,86,97,4792,4840,4849,4905,4912,4972,5022,5040,4,2,110,114,4798,4811,103,108,101,66,114,97,99,107,101,116,59,1,10217,114,111,119,4,3,59,66,76,4822,4824,4829,1,8594,97,114,59,1,8677,101,102,116,65,114,114,111,119,59,1,8644,101,105,108,105,110,103,59,1,8969,111,4,2,117,119,4856,4869,98,108,101,66,114,97,99,107,101,116,59,1,10215,110,4,2,84,86,4876,4887,101,101,86,101,99,116,111,114,59,1,10589,101,99,116,111,114,4,2,59,66,4898,4900,1,8642,97,114,59,1,10581,108,111,111,114,59,1,8971,4,2,101,114,4918,4944,101,4,3,59,65,86,4927,4929,4936,1,8866,114,114,111,119,59,1,8614,101,99,116,111,114,59,1,10587,105,97,110,103,108,101,4,3,59,66,69,4958,4960,4965,1,8883,97,114,59,1,10704,113,117,97,108,59,1,8885,112,4,3,68,84,86,4981,4993,5004,111,119,110,86,101,99,116,111,114,59,1,10575,101,101,86,101,99,116,111,114,59,1,10588,101,99,116,111,114,4,2,59,66,5015,5017,1,8638,97,114,59,1,10580,101,99,116,111,114,4,2,59,66,5033,5035,1,8640,97,114,59,1,10579,114,114,111,119,59,1,8658,4,2,112,117,5053,5057,102,59,1,8477,110,100,73,109,112,108,105,101,115,59,1,10608,105,103,104,116,97,114,114,111,119,59,1,8667,4,2,99,104,5087,5091,114,59,1,8475,59,1,8625,108,101,68,101,108,97,121,101,100,59,1,10740,4,13,72,79,97,99,102,104,105,109,111,113,115,116,117,5134,5150,5157,5164,5198,5203,5259,5265,5277,5283,5374,5380,5385,4,2,67,99,5140,5146,72,99,121,59,1,1065,121,59,1,1064,70,84,99,121,59,1,1068,99,117,116,101,59,1,346,4,5,59,97,101,105,121,5176,5178,5184,5190,5195,1,10940,114,111,110,59,1,352,100,105,108,59,1,350,114,99,59,1,348,59,1,1057,114,59,3,55349,56598,111,114,116,4,4,68,76,82,85,5216,5227,5238,5250,111,119,110,65,114,114,111,119,59,1,8595,101,102,116,65,114,114,111,119,59,1,8592,105,103,104,116,65,114,114,111,119,59,1,8594,112,65,114,114,111,119,59,1,8593,103,109,97,59,1,931,97,108,108,67,105,114,99,108,101,59,1,8728,112,102,59,3,55349,56650,4,2,114,117,5289,5293,116,59,1,8730,97,114,101,4,4,59,73,83,85,5306,5308,5322,5367,1,9633,110,116,101,114,115,101,99,116,105,111,110,59,1,8851,117,4,2,98,112,5329,5347,115,101,116,4,2,59,69,5338,5340,1,8847,113,117,97,108,59,1,8849,101,114,115,101,116,4,2,59,69,5358,5360,1,8848,113,117,97,108,59,1,8850,110,105,111,110,59,1,8852,99,114,59,3,55349,56494,97,114,59,1,8902,4,4,98,99,109,112,5395,5420,5475,5478,4,2,59,115,5401,5403,1,8912,101,116,4,2,59,69,5411,5413,1,8912,113,117,97,108,59,1,8838,4,2,99,104,5426,5468,101,101,100,115,4,4,59,69,83,84,5440,5442,5449,5461,1,8827,113,117,97,108,59,1,10928,108,97,110,116,69,113,117,97,108,59,1,8829,105,108,100,101,59,1,8831,84,104,97,116,59,1,8715,59,1,8721,4,3,59,101,115,5486,5488,5507,1,8913,114,115,101,116,4,2,59,69,5498,5500,1,8835,113,117,97,108,59,1,8839,101,116,59,1,8913,4,11,72,82,83,97,99,102,104,105,111,114,115,5536,5546,5552,5567,5579,5602,5607,5655,5695,5701,5711,79,82,78,5,222,1,59,5544,1,222,65,68,69,59,1,8482,4,2,72,99,5558,5563,99,121,59,1,1035,121,59,1,1062,4,2,98,117,5573,5576,59,1,9,59,1,932,4,3,97,101,121,5587,5593,5599,114,111,110,59,1,356,100,105,108,59,1,354,59,1,1058,114,59,3,55349,56599,4,2,101,105,5613,5631,4,2,114,116,5619,5627,101,102,111,114,101,59,1,8756,97,59,1,920,4,2,99,110,5637,5647,107,83,112,97,99,101,59,3,8287,8202,83,112,97,99,101,59,1,8201,108,100,101,4,4,59,69,70,84,5668,5670,5677,5688,1,8764,113,117,97,108,59,1,8771,117,108,108,69,113,117,97,108,59,1,8773,105,108,100,101,59,1,8776,112,102,59,3,55349,56651,105,112,108,101,68,111,116,59,1,8411,4,2,99,116,5717,5722,114,59,3,55349,56495,114,111,107,59,1,358,4,14,97,98,99,100,102,103,109,110,111,112,114,115,116,117,5758,5789,5805,5823,5830,5835,5846,5852,5921,5937,6089,6095,6101,6108,4,2,99,114,5764,5774,117,116,101,5,218,1,59,5772,1,218,114,4,2,59,111,5781,5783,1,8607,99,105,114,59,1,10569,114,4,2,99,101,5796,5800,121,59,1,1038,118,101,59,1,364,4,2,105,121,5811,5820,114,99,5,219,1,59,5818,1,219,59,1,1059,98,108,97,99,59,1,368,114,59,3,55349,56600,114,97,118,101,5,217,1,59,5844,1,217,97,99,114,59,1,362,4,2,100,105,5858,5905,101,114,4,2,66,80,5866,5892,4,2,97,114,5872,5876,114,59,1,95,97,99,4,2,101,107,5884,5887,59,1,9183,101,116,59,1,9141,97,114,101,110,116,104,101,115,105,115,59,1,9181,111,110,4,2,59,80,5913,5915,1,8899,108,117,115,59,1,8846,4,2,103,112,5927,5932,111,110,59,1,370,102,59,3,55349,56652,4,8,65,68,69,84,97,100,112,115,5955,5985,5996,6009,6026,6033,6044,6075,114,114,111,119,4,3,59,66,68,5967,5969,5974,1,8593,97,114,59,1,10514,111,119,110,65,114,114,111,119,59,1,8645,111,119,110,65,114,114,111,119,59,1,8597,113,117,105,108,105,98,114,105,117,109,59,1,10606,101,101,4,2,59,65,6017,6019,1,8869,114,114,111,119,59,1,8613,114,114,111,119,59,1,8657,111,119,110,97,114,114,111,119,59,1,8661,101,114,4,2,76,82,6052,6063,101,102,116,65,114,114,111,119,59,1,8598,105,103,104,116,65,114,114,111,119,59,1,8599,105,4,2,59,108,6082,6084,1,978,111,110,59,1,933,105,110,103,59,1,366,99,114,59,3,55349,56496,105,108,100,101,59,1,360,109,108,5,220,1,59,6115,1,220,4,9,68,98,99,100,101,102,111,115,118,6137,6143,6148,6152,6166,6250,6255,6261,6267,97,115,104,59,1,8875,97,114,59,1,10987,121,59,1,1042,97,115,104,4,2,59,108,6161,6163,1,8873,59,1,10982,4,2,101,114,6172,6175,59,1,8897,4,3,98,116,121,6183,6188,6238,97,114,59,1,8214,4,2,59,105,6194,6196,1,8214,99,97,108,4,4,66,76,83,84,6209,6214,6220,6231,97,114,59,1,8739,105,110,101,59,1,124,101,112,97,114,97,116,111,114,59,1,10072,105,108,100,101,59,1,8768,84,104,105,110,83,112,97,99,101,59,1,8202,114,59,3,55349,56601,112,102,59,3,55349,56653,99,114,59,3,55349,56497,100,97,115,104,59,1,8874,4,5,99,101,102,111,115,6286,6292,6298,6303,6309,105,114,99,59,1,372,100,103,101,59,1,8896,114,59,3,55349,56602,112,102,59,3,55349,56654,99,114,59,3,55349,56498,4,4,102,105,111,115,6325,6330,6333,6339,114,59,3,55349,56603,59,1,926,112,102,59,3,55349,56655,99,114,59,3,55349,56499,4,9,65,73,85,97,99,102,111,115,117,6365,6370,6375,6380,6391,6405,6410,6416,6422,99,121,59,1,1071,99,121,59,1,1031,99,121,59,1,1070,99,117,116,101,5,221,1,59,6389,1,221,4,2,105,121,6397,6402,114,99,59,1,374,59,1,1067,114,59,3,55349,56604,112,102,59,3,55349,56656,99,114,59,3,55349,56500,109,108,59,1,376,4,8,72,97,99,100,101,102,111,115,6445,6450,6457,6472,6477,6501,6505,6510,99,121,59,1,1046,99,117,116,101,59,1,377,4,2,97,121,6463,6469,114,111,110,59,1,381,59,1,1047,111,116,59,1,379,4,2,114,116,6483,6497,111,87,105,100,116,104,83,112,97,99,101,59,1,8203,97,59,1,918,114,59,1,8488,112,102,59,1,8484,99,114,59,3,55349,56501,4,16,97,98,99,101,102,103,108,109,110,111,112,114,115,116,117,119,6550,6561,6568,6612,6622,6634,6645,6672,6699,6854,6870,6923,6933,6963,6974,6983,99,117,116,101,5,225,1,59,6559,1,225,114,101,118,101,59,1,259,4,6,59,69,100,105,117,121,6582,6584,6588,6591,6600,6609,1,8766,59,3,8766,819,59,1,8767,114,99,5,226,1,59,6598,1,226,116,101,5,180,1,59,6607,1,180,59,1,1072,108,105,103,5,230,1,59,6620,1,230,4,2,59,114,6628,6630,1,8289,59,3,55349,56606,114,97,118,101,5,224,1,59,6643,1,224,4,2,101,112,6651,6667,4,2,102,112,6657,6663,115,121,109,59,1,8501,104,59,1,8501,104,97,59,1,945,4,2,97,112,6678,6692,4,2,99,108,6684,6688,114,59,1,257,103,59,1,10815,5,38,1,59,6697,1,38,4,2,100,103,6705,6737,4,5,59,97,100,115,118,6717,6719,6724,6727,6734,1,8743,110,100,59,1,10837,59,1,10844,108,111,112,101,59,1,10840,59,1,10842,4,7,59,101,108,109,114,115,122,6753,6755,6758,6762,6814,6835,6848,1,8736,59,1,10660,101,59,1,8736,115,100,4,2,59,97,6770,6772,1,8737,4,8,97,98,99,100,101,102,103,104,6790,6793,6796,6799,6802,6805,6808,6811,59,1,10664,59,1,10665,59,1,10666,59,1,10667,59,1,10668,59,1,10669,59,1,10670,59,1,10671,116,4,2,59,118,6821,6823,1,8735,98,4,2,59,100,6830,6832,1,8894,59,1,10653,4,2,112,116,6841,6845,104,59,1,8738,59,1,197,97,114,114,59,1,9084,4,2,103,112,6860,6865,111,110,59,1,261,102,59,3,55349,56658,4,7,59,69,97,101,105,111,112,6886,6888,6891,6897,6900,6904,6908,1,8776,59,1,10864,99,105,114,59,1,10863,59,1,8778,100,59,1,8779,115,59,1,39,114,111,120,4,2,59,101,6917,6919,1,8776,113,59,1,8778,105,110,103,5,229,1,59,6931,1,229,4,3,99,116,121,6941,6946,6949,114,59,3,55349,56502,59,1,42,109,112,4,2,59,101,6957,6959,1,8776,113,59,1,8781,105,108,100,101,5,227,1,59,6972,1,227,109,108,5,228,1,59,6981,1,228,4,2,99,105,6989,6997,111,110,105,110,116,59,1,8755,110,116,59,1,10769,4,16,78,97,98,99,100,101,102,105,107,108,110,111,112,114,115,117,7036,7041,7119,7135,7149,7155,7219,7224,7347,7354,7463,7489,7786,7793,7814,7866,111,116,59,1,10989,4,2,99,114,7047,7094,107,4,4,99,101,112,115,7058,7064,7073,7080,111,110,103,59,1,8780,112,115,105,108,111,110,59,1,1014,114,105,109,101,59,1,8245,105,109,4,2,59,101,7088,7090,1,8765,113,59,1,8909,4,2,118,119,7100,7105,101,101,59,1,8893,101,100,4,2,59,103,7113,7115,1,8965,101,59,1,8965,114,107,4,2,59,116,7127,7129,1,9141,98,114,107,59,1,9142,4,2,111,121,7141,7146,110,103,59,1,8780,59,1,1073,113,117,111,59,1,8222,4,5,99,109,112,114,116,7167,7181,7188,7193,7199,97,117,115,4,2,59,101,7176,7178,1,8757,59,1,8757,112,116,121,118,59,1,10672,115,105,59,1,1014,110,111,117,59,1,8492,4,3,97,104,119,7207,7210,7213,59,1,946,59,1,8502,101,101,110,59,1,8812,114,59,3,55349,56607,103,4,7,99,111,115,116,117,118,119,7241,7262,7288,7305,7328,7335,7340,4,3,97,105,117,7249,7253,7258,112,59,1,8898,114,99,59,1,9711,112,59,1,8899,4,3,100,112,116,7270,7275,7281,111,116,59,1,10752,108,117,115,59,1,10753,105,109,101,115,59,1,10754,4,2,113,116,7294,7300,99,117,112,59,1,10758,97,114,59,1,9733,114,105,97,110,103,108,101,4,2,100,117,7318,7324,111,119,110,59,1,9661,112,59,1,9651,112,108,117,115,59,1,10756,101,101,59,1,8897,101,100,103,101,59,1,8896,97,114,111,119,59,1,10509,4,3,97,107,111,7362,7436,7458,4,2,99,110,7368,7432,107,4,3,108,115,116,7377,7386,7394,111,122,101,110,103,101,59,1,10731,113,117,97,114,101,59,1,9642,114,105,97,110,103,108,101,4,4,59,100,108,114,7411,7413,7419,7425,1,9652,111,119,110,59,1,9662,101,102,116,59,1,9666,105,103,104,116,59,1,9656,107,59,1,9251,4,2,49,51,7442,7454,4,2,50,52,7448,7451,59,1,9618,59,1,9617,52,59,1,9619,99,107,59,1,9608,4,2,101,111,7469,7485,4,2,59,113,7475,7478,3,61,8421,117,105,118,59,3,8801,8421,116,59,1,8976,4,4,112,116,119,120,7499,7504,7517,7523,102,59,3,55349,56659,4,2,59,116,7510,7512,1,8869,111,109,59,1,8869,116,105,101,59,1,8904,4,12,68,72,85,86,98,100,104,109,112,116,117,118,7549,7571,7597,7619,7655,7660,7682,7708,7715,7721,7728,7750,4,4,76,82,108,114,7559,7562,7565,7568,59,1,9559,59,1,9556,59,1,9558,59,1,9555,4,5,59,68,85,100,117,7583,7585,7588,7591,7594,1,9552,59,1,9574,59,1,9577,59,1,9572,59,1,9575,4,4,76,82,108,114,7607,7610,7613,7616,59,1,9565,59,1,9562,59,1,9564,59,1,9561,4,7,59,72,76,82,104,108,114,7635,7637,7640,7643,7646,7649,7652,1,9553,59,1,9580,59,1,9571,59,1,9568,59,1,9579,59,1,9570,59,1,9567,111,120,59,1,10697,4,4,76,82,108,114,7670,7673,7676,7679,59,1,9557,59,1,9554,59,1,9488,59,1,9484,4,5,59,68,85,100,117,7694,7696,7699,7702,7705,1,9472,59,1,9573,59,1,9576,59,1,9516,59,1,9524,105,110,117,115,59,1,8863,108,117,115,59,1,8862,105,109,101,115,59,1,8864,4,4,76,82,108,114,7738,7741,7744,7747,59,1,9563,59,1,9560,59,1,9496,59,1,9492,4,7,59,72,76,82,104,108,114,7766,7768,7771,7774,7777,7780,7783,1,9474,59,1,9578,59,1,9569,59,1,9566,59,1,9532,59,1,9508,59,1,9500,114,105,109,101,59,1,8245,4,2,101,118,7799,7804,118,101,59,1,728,98,97,114,5,166,1,59,7812,1,166,4,4,99,101,105,111,7824,7829,7834,7846,114,59,3,55349,56503,109,105,59,1,8271,109,4,2,59,101,7841,7843,1,8765,59,1,8909,108,4,3,59,98,104,7855,7857,7860,1,92,59,1,10693,115,117,98,59,1,10184,4,2,108,109,7872,7885,108,4,2,59,101,7879,7881,1,8226,116,59,1,8226,112,4,3,59,69,101,7894,7896,7899,1,8782,59,1,10926,4,2,59,113,7905,7907,1,8783,59,1,8783,4,15,97,99,100,101,102,104,105,108,111,114,115,116,117,119,121,7942,8021,8075,8080,8121,8126,8157,8279,8295,8430,8446,8485,8491,8707,8726,4,3,99,112,114,7950,7956,8007,117,116,101,59,1,263,4,6,59,97,98,99,100,115,7970,7972,7977,7984,7998,8003,1,8745,110,100,59,1,10820,114,99,117,112,59,1,10825,4,2,97,117,7990,7994,112,59,1,10827,112,59,1,10823,111,116,59,1,10816,59,3,8745,65024,4,2,101,111,8013,8017,116,59,1,8257,110,59,1,711,4,4,97,101,105,117,8031,8046,8056,8061,4,2,112,114,8037,8041,115,59,1,10829,111,110,59,1,269,100,105,108,5,231,1,59,8054,1,231,114,99,59,1,265,112,115,4,2,59,115,8069,8071,1,10828,109,59,1,10832,111,116,59,1,267,4,3,100,109,110,8088,8097,8104,105,108,5,184,1,59,8095,1,184,112,116,121,118,59,1,10674,116,5,162,2,59,101,8112,8114,1,162,114,100,111,116,59,1,183,114,59,3,55349,56608,4,3,99,101,105,8134,8138,8154,121,59,1,1095,99,107,4,2,59,109,8146,8148,1,10003,97,114,107,59,1,10003,59,1,967,114,4,7,59,69,99,101,102,109,115,8174,8176,8179,8258,8261,8268,8273,1,9675,59,1,10691,4,3,59,101,108,8187,8189,8193,1,710,113,59,1,8791,101,4,2,97,100,8200,8223,114,114,111,119,4,2,108,114,8210,8216,101,102,116,59,1,8634,105,103,104,116,59,1,8635,4,5,82,83,97,99,100,8235,8238,8241,8246,8252,59,1,174,59,1,9416,115,116,59,1,8859,105,114,99,59,1,8858,97,115,104,59,1,8861,59,1,8791,110,105,110,116,59,1,10768,105,100,59,1,10991,99,105,114,59,1,10690,117,98,115,4,2,59,117,8288,8290,1,9827,105,116,59,1,9827,4,4,108,109,110,112,8305,8326,8376,8400,111,110,4,2,59,101,8313,8315,1,58,4,2,59,113,8321,8323,1,8788,59,1,8788,4,2,109,112,8332,8344,97,4,2,59,116,8339,8341,1,44,59,1,64,4,3,59,102,108,8352,8354,8358,1,8705,110,59,1,8728,101,4,2,109,120,8365,8371,101,110,116,59,1,8705,101,115,59,1,8450,4,2,103,105,8382,8395,4,2,59,100,8388,8390,1,8773,111,116,59,1,10861,110,116,59,1,8750,4,3,102,114,121,8408,8412,8417,59,3,55349,56660,111,100,59,1,8720,5,169,2,59,115,8424,8426,1,169,114,59,1,8471,4,2,97,111,8436,8441,114,114,59,1,8629,115,115,59,1,10007,4,2,99,117,8452,8457,114,59,3,55349,56504,4,2,98,112,8463,8474,4,2,59,101,8469,8471,1,10959,59,1,10961,4,2,59,101,8480,8482,1,10960,59,1,10962,100,111,116,59,1,8943,4,7,100,101,108,112,114,118,119,8507,8522,8536,8550,8600,8697,8702,97,114,114,4,2,108,114,8516,8519,59,1,10552,59,1,10549,4,2,112,115,8528,8532,114,59,1,8926,99,59,1,8927,97,114,114,4,2,59,112,8545,8547,1,8630,59,1,10557,4,6,59,98,99,100,111,115,8564,8566,8573,8587,8592,8596,1,8746,114,99,97,112,59,1,10824,4,2,97,117,8579,8583,112,59,1,10822,112,59,1,10826,111,116,59,1,8845,114,59,1,10821,59,3,8746,65024,4,4,97,108,114,118,8610,8623,8663,8672,114,114,4,2,59,109,8618,8620,1,8631,59,1,10556,121,4,3,101,118,119,8632,8651,8656,113,4,2,112,115,8639,8645,114,101,99,59,1,8926,117,99,99,59,1,8927,101,101,59,1,8910,101,100,103,101,59,1,8911,101,110,5,164,1,59,8670,1,164,101,97,114,114,111,119,4,2,108,114,8684,8690,101,102,116,59,1,8630,105,103,104,116,59,1,8631,101,101,59,1,8910,101,100,59,1,8911,4,2,99,105,8713,8721,111,110,105,110,116,59,1,8754,110,116,59,1,8753,108,99,116,121,59,1,9005,4,19,65,72,97,98,99,100,101,102,104,105,106,108,111,114,115,116,117,119,122,8773,8778,8783,8821,8839,8854,8887,8914,8930,8944,9036,9041,9058,9197,9227,9258,9281,9297,9305,114,114,59,1,8659,97,114,59,1,10597,4,4,103,108,114,115,8793,8799,8805,8809,103,101,114,59,1,8224,101,116,104,59,1,8504,114,59,1,8595,104,4,2,59,118,8816,8818,1,8208,59,1,8867,4,2,107,108,8827,8834,97,114,111,119,59,1,10511,97,99,59,1,733,4,2,97,121,8845,8851,114,111,110,59,1,271,59,1,1076,4,3,59,97,111,8862,8864,8880,1,8518,4,2,103,114,8870,8876,103,101,114,59,1,8225,114,59,1,8650,116,115,101,113,59,1,10871,4,3,103,108,109,8895,8902,8907,5,176,1,59,8900,1,176,116,97,59,1,948,112,116,121,118,59,1,10673,4,2,105,114,8920,8926,115,104,116,59,1,10623,59,3,55349,56609,97,114,4,2,108,114,8938,8941,59,1,8643,59,1,8642,4,5,97,101,103,115,118,8956,8986,8989,8996,9001,109,4,3,59,111,115,8965,8967,8983,1,8900,110,100,4,2,59,115,8975,8977,1,8900,117,105,116,59,1,9830,59,1,9830,59,1,168,97,109,109,97,59,1,989,105,110,59,1,8946,4,3,59,105,111,9009,9011,9031,1,247,100,101,5,247,2,59,111,9020,9022,1,247,110,116,105,109,101,115,59,1,8903,110,120,59,1,8903,99,121,59,1,1106,99,4,2,111,114,9048,9053,114,110,59,1,8990,111,112,59,1,8973,4,5,108,112,116,117,119,9070,9076,9081,9130,9144,108,97,114,59,1,36,102,59,3,55349,56661,4,5,59,101,109,112,115,9093,9095,9109,9116,9122,1,729,113,4,2,59,100,9102,9104,1,8784,111,116,59,1,8785,105,110,117,115,59,1,8760,108,117,115,59,1,8724,113,117,97,114,101,59,1,8865,98,108,101,98,97,114,119,101,100,103,101,59,1,8966,110,4,3,97,100,104,9153,9160,9172,114,114,111,119,59,1,8595,111,119,110,97,114,114,111,119,115,59,1,8650,97,114,112,111,111,110,4,2,108,114,9184,9190,101,102,116,59,1,8643,105,103,104,116,59,1,8642,4,2,98,99,9203,9211,107,97,114,111,119,59,1,10512,4,2,111,114,9217,9222,114,110,59,1,8991,111,112,59,1,8972,4,3,99,111,116,9235,9248,9252,4,2,114,121,9241,9245,59,3,55349,56505,59,1,1109,108,59,1,10742,114,111,107,59,1,273,4,2,100,114,9264,9269,111,116,59,1,8945,105,4,2,59,102,9276,9278,1,9663,59,1,9662,4,2,97,104,9287,9292,114,114,59,1,8693,97,114,59,1,10607,97,110,103,108,101,59,1,10662,4,2,99,105,9311,9315,121,59,1,1119,103,114,97,114,114,59,1,10239,4,18,68,97,99,100,101,102,103,108,109,110,111,112,113,114,115,116,117,120,9361,9376,9398,9439,9444,9447,9462,9495,9531,9585,9598,9614,9659,9755,9771,9792,9808,9826,4,2,68,111,9367,9372,111,116,59,1,10871,116,59,1,8785,4,2,99,115,9382,9392,117,116,101,5,233,1,59,9390,1,233,116,101,114,59,1,10862,4,4,97,105,111,121,9408,9414,9430,9436,114,111,110,59,1,283,114,4,2,59,99,9421,9423,1,8790,5,234,1,59,9428,1,234,108,111,110,59,1,8789,59,1,1101,111,116,59,1,279,59,1,8519,4,2,68,114,9453,9458,111,116,59,1,8786,59,3,55349,56610,4,3,59,114,115,9470,9472,9482,1,10906,97,118,101,5,232,1,59,9480,1,232,4,2,59,100,9488,9490,1,10902,111,116,59,1,10904,4,4,59,105,108,115,9505,9507,9515,9518,1,10905,110,116,101,114,115,59,1,9191,59,1,8467,4,2,59,100,9524,9526,1,10901,111,116,59,1,10903,4,3,97,112,115,9539,9544,9564,99,114,59,1,275,116,121,4,3,59,115,118,9554,9556,9561,1,8709,101,116,59,1,8709,59,1,8709,112,4,2,49,59,9571,9583,4,2,51,52,9577,9580,59,1,8196,59,1,8197,1,8195,4,2,103,115,9591,9594,59,1,331,112,59,1,8194,4,2,103,112,9604,9609,111,110,59,1,281,102,59,3,55349,56662,4,3,97,108,115,9622,9635,9640,114,4,2,59,115,9629,9631,1,8917,108,59,1,10723,117,115,59,1,10865,105,4,3,59,108,118,9649,9651,9656,1,949,111,110,59,1,949,59,1,1013,4,4,99,115,117,118,9669,9686,9716,9747,4,2,105,111,9675,9680,114,99,59,1,8790,108,111,110,59,1,8789,4,2,105,108,9692,9696,109,59,1,8770,97,110,116,4,2,103,108,9705,9710,116,114,59,1,10902,101,115,115,59,1,10901,4,3,97,101,105,9724,9729,9734,108,115,59,1,61,115,116,59,1,8799,118,4,2,59,68,9741,9743,1,8801,68,59,1,10872,112,97,114,115,108,59,1,10725,4,2,68,97,9761,9766,111,116,59,1,8787,114,114,59,1,10609,4,3,99,100,105,9779,9783,9788,114,59,1,8495,111,116,59,1,8784,109,59,1,8770,4,2,97,104,9798,9801,59,1,951,5,240,1,59,9806,1,240,4,2,109,114,9814,9822,108,5,235,1,59,9820,1,235,111,59,1,8364,4,3,99,105,112,9834,9838,9843,108,59,1,33,115,116,59,1,8707,4,2,101,111,9849,9859,99,116,97,116,105,111,110,59,1,8496,110,101,110,116,105,97,108,101,59,1,8519,4,12,97,99,101,102,105,106,108,110,111,112,114,115,9896,9910,9914,9921,9954,9960,9967,9989,9994,10027,10036,10164,108,108,105,110,103,100,111,116,115,101,113,59,1,8786,121,59,1,1092,109,97,108,101,59,1,9792,4,3,105,108,114,9929,9935,9950,108,105,103,59,1,64259,4,2,105,108,9941,9945,103,59,1,64256,105,103,59,1,64260,59,3,55349,56611,108,105,103,59,1,64257,108,105,103,59,3,102,106,4,3,97,108,116,9975,9979,9984,116,59,1,9837,105,103,59,1,64258,110,115,59,1,9649,111,102,59,1,402,4,2,112,114,1e4,10005,102,59,3,55349,56663,4,2,97,107,10011,10016,108,108,59,1,8704,4,2,59,118,10022,10024,1,8916,59,1,10969,97,114,116,105,110,116,59,1,10765,4,2,97,111,10042,10159,4,2,99,115,10048,10155,4,6,49,50,51,52,53,55,10062,10102,10114,10135,10139,10151,4,6,50,51,52,53,54,56,10076,10083,10086,10093,10096,10099,5,189,1,59,10081,1,189,59,1,8531,5,188,1,59,10091,1,188,59,1,8533,59,1,8537,59,1,8539,4,2,51,53,10108,10111,59,1,8532,59,1,8534,4,3,52,53,56,10122,10129,10132,5,190,1,59,10127,1,190,59,1,8535,59,1,8540,53,59,1,8536,4,2,54,56,10145,10148,59,1,8538,59,1,8541,56,59,1,8542,108,59,1,8260,119,110,59,1,8994,99,114,59,3,55349,56507,4,17,69,97,98,99,100,101,102,103,105,106,108,110,111,114,115,116,118,10206,10217,10247,10254,10268,10273,10358,10363,10374,10380,10385,10406,10458,10464,10470,10497,10610,4,2,59,108,10212,10214,1,8807,59,1,10892,4,3,99,109,112,10225,10231,10244,117,116,101,59,1,501,109,97,4,2,59,100,10239,10241,1,947,59,1,989,59,1,10886,114,101,118,101,59,1,287,4,2,105,121,10260,10265,114,99,59,1,285,59,1,1075,111,116,59,1,289,4,4,59,108,113,115,10283,10285,10288,10308,1,8805,59,1,8923,4,3,59,113,115,10296,10298,10301,1,8805,59,1,8807,108,97,110,116,59,1,10878,4,4,59,99,100,108,10318,10320,10324,10345,1,10878,99,59,1,10921,111,116,4,2,59,111,10332,10334,1,10880,4,2,59,108,10340,10342,1,10882,59,1,10884,4,2,59,101,10351,10354,3,8923,65024,115,59,1,10900,114,59,3,55349,56612,4,2,59,103,10369,10371,1,8811,59,1,8921,109,101,108,59,1,8503,99,121,59,1,1107,4,4,59,69,97,106,10395,10397,10400,10403,1,8823,59,1,10898,59,1,10917,59,1,10916,4,4,69,97,101,115,10416,10419,10434,10453,59,1,8809,112,4,2,59,112,10426,10428,1,10890,114,111,120,59,1,10890,4,2,59,113,10440,10442,1,10888,4,2,59,113,10448,10450,1,10888,59,1,8809,105,109,59,1,8935,112,102,59,3,55349,56664,97,118,101,59,1,96,4,2,99,105,10476,10480,114,59,1,8458,109,4,3,59,101,108,10489,10491,10494,1,8819,59,1,10894,59,1,10896,5,62,6,59,99,100,108,113,114,10512,10514,10527,10532,10538,10545,1,62,4,2,99,105,10520,10523,59,1,10919,114,59,1,10874,111,116,59,1,8919,80,97,114,59,1,10645,117,101,115,116,59,1,10876,4,5,97,100,101,108,115,10557,10574,10579,10599,10605,4,2,112,114,10563,10570,112,114,111,120,59,1,10886,114,59,1,10616,111,116,59,1,8919,113,4,2,108,113,10586,10592,101,115,115,59,1,8923,108,101,115,115,59,1,10892,101,115,115,59,1,8823,105,109,59,1,8819,4,2,101,110,10616,10626,114,116,110,101,113,113,59,3,8809,65024,69,59,3,8809,65024,4,10,65,97,98,99,101,102,107,111,115,121,10653,10658,10713,10718,10724,10760,10765,10786,10850,10875,114,114,59,1,8660,4,4,105,108,109,114,10668,10674,10678,10684,114,115,112,59,1,8202,102,59,1,189,105,108,116,59,1,8459,4,2,100,114,10690,10695,99,121,59,1,1098,4,3,59,99,119,10703,10705,10710,1,8596,105,114,59,1,10568,59,1,8621,97,114,59,1,8463,105,114,99,59,1,293,4,3,97,108,114,10732,10748,10754,114,116,115,4,2,59,117,10741,10743,1,9829,105,116,59,1,9829,108,105,112,59,1,8230,99,111,110,59,1,8889,114,59,3,55349,56613,115,4,2,101,119,10772,10779,97,114,111,119,59,1,10533,97,114,111,119,59,1,10534,4,5,97,109,111,112,114,10798,10803,10809,10839,10844,114,114,59,1,8703,116,104,116,59,1,8763,107,4,2,108,114,10816,10827,101,102,116,97,114,114,111,119,59,1,8617,105,103,104,116,97,114,114,111,119,59,1,8618,102,59,3,55349,56665,98,97,114,59,1,8213,4,3,99,108,116,10858,10863,10869,114,59,3,55349,56509,97,115,104,59,1,8463,114,111,107,59,1,295,4,2,98,112,10881,10887,117,108,108,59,1,8259,104,101,110,59,1,8208,4,15,97,99,101,102,103,105,106,109,110,111,112,113,115,116,117,10925,10936,10958,10977,10990,11001,11039,11045,11101,11192,11220,11226,11237,11285,11299,99,117,116,101,5,237,1,59,10934,1,237,4,3,59,105,121,10944,10946,10955,1,8291,114,99,5,238,1,59,10953,1,238,59,1,1080,4,2,99,120,10964,10968,121,59,1,1077,99,108,5,161,1,59,10975,1,161,4,2,102,114,10983,10986,59,1,8660,59,3,55349,56614,114,97,118,101,5,236,1,59,10999,1,236,4,4,59,105,110,111,11011,11013,11028,11034,1,8520,4,2,105,110,11019,11024,110,116,59,1,10764,116,59,1,8749,102,105,110,59,1,10716,116,97,59,1,8489,108,105,103,59,1,307,4,3,97,111,112,11053,11092,11096,4,3,99,103,116,11061,11065,11088,114,59,1,299,4,3,101,108,112,11073,11076,11082,59,1,8465,105,110,101,59,1,8464,97,114,116,59,1,8465,104,59,1,305,102,59,1,8887,101,100,59,1,437,4,5,59,99,102,111,116,11113,11115,11121,11136,11142,1,8712,97,114,101,59,1,8453,105,110,4,2,59,116,11129,11131,1,8734,105,101,59,1,10717,100,111,116,59,1,305,4,5,59,99,101,108,112,11154,11156,11161,11179,11186,1,8747,97,108,59,1,8890,4,2,103,114,11167,11173,101,114,115,59,1,8484,99,97,108,59,1,8890,97,114,104,107,59,1,10775,114,111,100,59,1,10812,4,4,99,103,112,116,11202,11206,11211,11216,121,59,1,1105,111,110,59,1,303,102,59,3,55349,56666,97,59,1,953,114,111,100,59,1,10812,117,101,115,116,5,191,1,59,11235,1,191,4,2,99,105,11243,11248,114,59,3,55349,56510,110,4,5,59,69,100,115,118,11261,11263,11266,11271,11282,1,8712,59,1,8953,111,116,59,1,8949,4,2,59,118,11277,11279,1,8948,59,1,8947,59,1,8712,4,2,59,105,11291,11293,1,8290,108,100,101,59,1,297,4,2,107,109,11305,11310,99,121,59,1,1110,108,5,239,1,59,11316,1,239,4,6,99,102,109,111,115,117,11332,11346,11351,11357,11363,11380,4,2,105,121,11338,11343,114,99,59,1,309,59,1,1081,114,59,3,55349,56615,97,116,104,59,1,567,112,102,59,3,55349,56667,4,2,99,101,11369,11374,114,59,3,55349,56511,114,99,121,59,1,1112,107,99,121,59,1,1108,4,8,97,99,102,103,104,106,111,115,11404,11418,11433,11438,11445,11450,11455,11461,112,112,97,4,2,59,118,11413,11415,1,954,59,1,1008,4,2,101,121,11424,11430,100,105,108,59,1,311,59,1,1082,114,59,3,55349,56616,114,101,101,110,59,1,312,99,121,59,1,1093,99,121,59,1,1116,112,102,59,3,55349,56668,99,114,59,3,55349,56512,4,23,65,66,69,72,97,98,99,100,101,102,103,104,106,108,109,110,111,112,114,115,116,117,118,11515,11538,11544,11555,11560,11721,11780,11818,11868,12136,12160,12171,12203,12208,12246,12275,12327,12509,12523,12569,12641,12732,12752,4,3,97,114,116,11523,11528,11532,114,114,59,1,8666,114,59,1,8656,97,105,108,59,1,10523,97,114,114,59,1,10510,4,2,59,103,11550,11552,1,8806,59,1,10891,97,114,59,1,10594,4,9,99,101,103,109,110,112,113,114,116,11580,11586,11594,11600,11606,11624,11627,11636,11694,117,116,101,59,1,314,109,112,116,121,118,59,1,10676,114,97,110,59,1,8466,98,100,97,59,1,955,103,4,3,59,100,108,11615,11617,11620,1,10216,59,1,10641,101,59,1,10216,59,1,10885,117,111,5,171,1,59,11634,1,171,114,4,8,59,98,102,104,108,112,115,116,11655,11657,11669,11673,11677,11681,11685,11690,1,8592,4,2,59,102,11663,11665,1,8676,115,59,1,10527,115,59,1,10525,107,59,1,8617,112,59,1,8619,108,59,1,10553,105,109,59,1,10611,108,59,1,8610,4,3,59,97,101,11702,11704,11709,1,10923,105,108,59,1,10521,4,2,59,115,11715,11717,1,10925,59,3,10925,65024,4,3,97,98,114,11729,11734,11739,114,114,59,1,10508,114,107,59,1,10098,4,2,97,107,11745,11758,99,4,2,101,107,11752,11755,59,1,123,59,1,91,4,2,101,115,11764,11767,59,1,10635,108,4,2,100,117,11774,11777,59,1,10639,59,1,10637,4,4,97,101,117,121,11790,11796,11811,11815,114,111,110,59,1,318,4,2,100,105,11802,11807,105,108,59,1,316,108,59,1,8968,98,59,1,123,59,1,1083,4,4,99,113,114,115,11828,11832,11845,11864,97,59,1,10550,117,111,4,2,59,114,11840,11842,1,8220,59,1,8222,4,2,100,117,11851,11857,104,97,114,59,1,10599,115,104,97,114,59,1,10571,104,59,1,8626,4,5,59,102,103,113,115,11880,11882,12008,12011,12031,1,8804,116,4,5,97,104,108,114,116,11895,11913,11935,11947,11996,114,114,111,119,4,2,59,116,11905,11907,1,8592,97,105,108,59,1,8610,97,114,112,111,111,110,4,2,100,117,11925,11931,111,119,110,59,1,8637,112,59,1,8636,101,102,116,97,114,114,111,119,115,59,1,8647,105,103,104,116,4,3,97,104,115,11959,11974,11984,114,114,111,119,4,2,59,115,11969,11971,1,8596,59,1,8646,97,114,112,111,111,110,115,59,1,8651,113,117,105,103,97,114,114,111,119,59,1,8621,104,114,101,101,116,105,109,101,115,59,1,8907,59,1,8922,4,3,59,113,115,12019,12021,12024,1,8804,59,1,8806,108,97,110,116,59,1,10877,4,5,59,99,100,103,115,12043,12045,12049,12070,12083,1,10877,99,59,1,10920,111,116,4,2,59,111,12057,12059,1,10879,4,2,59,114,12065,12067,1,10881,59,1,10883,4,2,59,101,12076,12079,3,8922,65024,115,59,1,10899,4,5,97,100,101,103,115,12095,12103,12108,12126,12131,112,112,114,111,120,59,1,10885,111,116,59,1,8918,113,4,2,103,113,12115,12120,116,114,59,1,8922,103,116,114,59,1,10891,116,114,59,1,8822,105,109,59,1,8818,4,3,105,108,114,12144,12150,12156,115,104,116,59,1,10620,111,111,114,59,1,8970,59,3,55349,56617,4,2,59,69,12166,12168,1,8822,59,1,10897,4,2,97,98,12177,12198,114,4,2,100,117,12184,12187,59,1,8637,4,2,59,108,12193,12195,1,8636,59,1,10602,108,107,59,1,9604,99,121,59,1,1113,4,5,59,97,99,104,116,12220,12222,12227,12235,12241,1,8810,114,114,59,1,8647,111,114,110,101,114,59,1,8990,97,114,100,59,1,10603,114,105,59,1,9722,4,2,105,111,12252,12258,100,111,116,59,1,320,117,115,116,4,2,59,97,12267,12269,1,9136,99,104,101,59,1,9136,4,4,69,97,101,115,12285,12288,12303,12322,59,1,8808,112,4,2,59,112,12295,12297,1,10889,114,111,120,59,1,10889,4,2,59,113,12309,12311,1,10887,4,2,59,113,12317,12319,1,10887,59,1,8808,105,109,59,1,8934,4,8,97,98,110,111,112,116,119,122,12345,12359,12364,12421,12446,12467,12474,12490,4,2,110,114,12351,12355,103,59,1,10220,114,59,1,8701,114,107,59,1,10214,103,4,3,108,109,114,12373,12401,12409,101,102,116,4,2,97,114,12382,12389,114,114,111,119,59,1,10229,105,103,104,116,97,114,114,111,119,59,1,10231,97,112,115,116,111,59,1,10236,105,103,104,116,97,114,114,111,119,59,1,10230,112,97,114,114,111,119,4,2,108,114,12433,12439,101,102,116,59,1,8619,105,103,104,116,59,1,8620,4,3,97,102,108,12454,12458,12462,114,59,1,10629,59,3,55349,56669,117,115,59,1,10797,105,109,101,115,59,1,10804,4,2,97,98,12480,12485,115,116,59,1,8727,97,114,59,1,95,4,3,59,101,102,12498,12500,12506,1,9674,110,103,101,59,1,9674,59,1,10731,97,114,4,2,59,108,12517,12519,1,40,116,59,1,10643,4,5,97,99,104,109,116,12535,12540,12548,12561,12564,114,114,59,1,8646,111,114,110,101,114,59,1,8991,97,114,4,2,59,100,12556,12558,1,8651,59,1,10605,59,1,8206,114,105,59,1,8895,4,6,97,99,104,105,113,116,12583,12589,12594,12597,12614,12635,113,117,111,59,1,8249,114,59,3,55349,56513,59,1,8624,109,4,3,59,101,103,12606,12608,12611,1,8818,59,1,10893,59,1,10895,4,2,98,117,12620,12623,59,1,91,111,4,2,59,114,12630,12632,1,8216,59,1,8218,114,111,107,59,1,322,5,60,8,59,99,100,104,105,108,113,114,12660,12662,12675,12680,12686,12692,12698,12705,1,60,4,2,99,105,12668,12671,59,1,10918,114,59,1,10873,111,116,59,1,8918,114,101,101,59,1,8907,109,101,115,59,1,8905,97,114,114,59,1,10614,117,101,115,116,59,1,10875,4,2,80,105,12711,12716,97,114,59,1,10646,4,3,59,101,102,12724,12726,12729,1,9667,59,1,8884,59,1,9666,114,4,2,100,117,12739,12746,115,104,97,114,59,1,10570,104,97,114,59,1,10598,4,2,101,110,12758,12768,114,116,110,101,113,113,59,3,8808,65024,69,59,3,8808,65024,4,14,68,97,99,100,101,102,104,105,108,110,111,112,115,117,12803,12809,12893,12908,12914,12928,12933,12937,13011,13025,13032,13049,13052,13069,68,111,116,59,1,8762,4,4,99,108,112,114,12819,12827,12849,12887,114,5,175,1,59,12825,1,175,4,2,101,116,12833,12836,59,1,9794,4,2,59,101,12842,12844,1,10016,115,101,59,1,10016,4,2,59,115,12855,12857,1,8614,116,111,4,4,59,100,108,117,12869,12871,12877,12883,1,8614,111,119,110,59,1,8615,101,102,116,59,1,8612,112,59,1,8613,107,101,114,59,1,9646,4,2,111,121,12899,12905,109,109,97,59,1,10793,59,1,1084,97,115,104,59,1,8212,97,115,117,114,101,100,97,110,103,108,101,59,1,8737,114,59,3,55349,56618,111,59,1,8487,4,3,99,100,110,12945,12954,12985,114,111,5,181,1,59,12952,1,181,4,4,59,97,99,100,12964,12966,12971,12976,1,8739,115,116,59,1,42,105,114,59,1,10992,111,116,5,183,1,59,12983,1,183,117,115,4,3,59,98,100,12995,12997,13e3,1,8722,59,1,8863,4,2,59,117,13006,13008,1,8760,59,1,10794,4,2,99,100,13017,13021,112,59,1,10971,114,59,1,8230,112,108,117,115,59,1,8723,4,2,100,112,13038,13044,101,108,115,59,1,8871,102,59,3,55349,56670,59,1,8723,4,2,99,116,13058,13063,114,59,3,55349,56514,112,111,115,59,1,8766,4,3,59,108,109,13077,13079,13087,1,956,116,105,109,97,112,59,1,8888,97,112,59,1,8888,4,24,71,76,82,86,97,98,99,100,101,102,103,104,105,106,108,109,111,112,114,115,116,117,118,119,13142,13165,13217,13229,13247,13330,13359,13414,13420,13508,13513,13579,13602,13626,13631,13762,13767,13855,13936,13995,14214,14285,14312,14432,4,2,103,116,13148,13152,59,3,8921,824,4,2,59,118,13158,13161,3,8811,8402,59,3,8811,824,4,3,101,108,116,13173,13200,13204,102,116,4,2,97,114,13181,13188,114,114,111,119,59,1,8653,105,103,104,116,97,114,114,111,119,59,1,8654,59,3,8920,824,4,2,59,118,13210,13213,3,8810,8402,59,3,8810,824,105,103,104,116,97,114,114,111,119,59,1,8655,4,2,68,100,13235,13241,97,115,104,59,1,8879,97,115,104,59,1,8878,4,5,98,99,110,112,116,13259,13264,13270,13275,13308,108,97,59,1,8711,117,116,101,59,1,324,103,59,3,8736,8402,4,5,59,69,105,111,112,13287,13289,13293,13298,13302,1,8777,59,3,10864,824,100,59,3,8779,824,115,59,1,329,114,111,120,59,1,8777,117,114,4,2,59,97,13316,13318,1,9838,108,4,2,59,115,13325,13327,1,9838,59,1,8469,4,2,115,117,13336,13344,112,5,160,1,59,13342,1,160,109,112,4,2,59,101,13352,13355,3,8782,824,59,3,8783,824,4,5,97,101,111,117,121,13371,13385,13391,13407,13411,4,2,112,114,13377,13380,59,1,10819,111,110,59,1,328,100,105,108,59,1,326,110,103,4,2,59,100,13399,13401,1,8775,111,116,59,3,10861,824,112,59,1,10818,59,1,1085,97,115,104,59,1,8211,4,7,59,65,97,100,113,115,120,13436,13438,13443,13466,13472,13478,13494,1,8800,114,114,59,1,8663,114,4,2,104,114,13450,13454,107,59,1,10532,4,2,59,111,13460,13462,1,8599,119,59,1,8599,111,116,59,3,8784,824,117,105,118,59,1,8802,4,2,101,105,13484,13489,97,114,59,1,10536,109,59,3,8770,824,105,115,116,4,2,59,115,13503,13505,1,8708,59,1,8708,114,59,3,55349,56619,4,4,69,101,115,116,13523,13527,13563,13568,59,3,8807,824,4,3,59,113,115,13535,13537,13559,1,8817,4,3,59,113,115,13545,13547,13551,1,8817,59,3,8807,824,108,97,110,116,59,3,10878,824,59,3,10878,824,105,109,59,1,8821,4,2,59,114,13574,13576,1,8815,59,1,8815,4,3,65,97,112,13587,13592,13597,114,114,59,1,8654,114,114,59,1,8622,97,114,59,1,10994,4,3,59,115,118,13610,13612,13623,1,8715,4,2,59,100,13618,13620,1,8956,59,1,8954,59,1,8715,99,121,59,1,1114,4,7,65,69,97,100,101,115,116,13647,13652,13656,13661,13665,13737,13742,114,114,59,1,8653,59,3,8806,824,114,114,59,1,8602,114,59,1,8229,4,4,59,102,113,115,13675,13677,13703,13725,1,8816,116,4,2,97,114,13684,13691,114,114,111,119,59,1,8602,105,103,104,116,97,114,114,111,119,59,1,8622,4,3,59,113,115,13711,13713,13717,1,8816,59,3,8806,824,108,97,110,116,59,3,10877,824,4,2,59,115,13731,13734,3,10877,824,59,1,8814,105,109,59,1,8820,4,2,59,114,13748,13750,1,8814,105,4,2,59,101,13757,13759,1,8938,59,1,8940,105,100,59,1,8740,4,2,112,116,13773,13778,102,59,3,55349,56671,5,172,3,59,105,110,13787,13789,13829,1,172,110,4,4,59,69,100,118,13800,13802,13806,13812,1,8713,59,3,8953,824,111,116,59,3,8949,824,4,3,97,98,99,13820,13823,13826,59,1,8713,59,1,8951,59,1,8950,105,4,2,59,118,13836,13838,1,8716,4,3,97,98,99,13846,13849,13852,59,1,8716,59,1,8958,59,1,8957,4,3,97,111,114,13863,13892,13899,114,4,4,59,97,115,116,13874,13876,13883,13888,1,8742,108,108,101,108,59,1,8742,108,59,3,11005,8421,59,3,8706,824,108,105,110,116,59,1,10772,4,3,59,99,101,13907,13909,13914,1,8832,117,101,59,1,8928,4,2,59,99,13920,13923,3,10927,824,4,2,59,101,13929,13931,1,8832,113,59,3,10927,824,4,4,65,97,105,116,13946,13951,13971,13982,114,114,59,1,8655,114,114,4,3,59,99,119,13961,13963,13967,1,8603,59,3,10547,824,59,3,8605,824,103,104,116,97,114,114,111,119,59,1,8603,114,105,4,2,59,101,13990,13992,1,8939,59,1,8941,4,7,99,104,105,109,112,113,117,14011,14036,14060,14080,14085,14090,14106,4,4,59,99,101,114,14021,14023,14028,14032,1,8833,117,101,59,1,8929,59,3,10928,824,59,3,55349,56515,111,114,116,4,2,109,112,14045,14050,105,100,59,1,8740,97,114,97,108,108,101,108,59,1,8742,109,4,2,59,101,14067,14069,1,8769,4,2,59,113,14075,14077,1,8772,59,1,8772,105,100,59,1,8740,97,114,59,1,8742,115,117,4,2,98,112,14098,14102,101,59,1,8930,101,59,1,8931,4,3,98,99,112,14114,14157,14171,4,4,59,69,101,115,14124,14126,14130,14133,1,8836,59,3,10949,824,59,1,8840,101,116,4,2,59,101,14141,14144,3,8834,8402,113,4,2,59,113,14151,14153,1,8840,59,3,10949,824,99,4,2,59,101,14164,14166,1,8833,113,59,3,10928,824,4,4,59,69,101,115,14181,14183,14187,14190,1,8837,59,3,10950,824,59,1,8841,101,116,4,2,59,101,14198,14201,3,8835,8402,113,4,2,59,113,14208,14210,1,8841,59,3,10950,824,4,4,103,105,108,114,14224,14228,14238,14242,108,59,1,8825,108,100,101,5,241,1,59,14236,1,241,103,59,1,8824,105,97,110,103,108,101,4,2,108,114,14254,14269,101,102,116,4,2,59,101,14263,14265,1,8938,113,59,1,8940,105,103,104,116,4,2,59,101,14279,14281,1,8939,113,59,1,8941,4,2,59,109,14291,14293,1,957,4,3,59,101,115,14301,14303,14308,1,35,114,111,59,1,8470,112,59,1,8199,4,9,68,72,97,100,103,105,108,114,115,14332,14338,14344,14349,14355,14369,14376,14408,14426,97,115,104,59,1,8877,97,114,114,59,1,10500,112,59,3,8781,8402,97,115,104,59,1,8876,4,2,101,116,14361,14365,59,3,8805,8402,59,3,62,8402,110,102,105,110,59,1,10718,4,3,65,101,116,14384,14389,14393,114,114,59,1,10498,59,3,8804,8402,4,2,59,114,14399,14402,3,60,8402,105,101,59,3,8884,8402,4,2,65,116,14414,14419,114,114,59,1,10499,114,105,101,59,3,8885,8402,105,109,59,3,8764,8402,4,3,65,97,110,14440,14445,14468,114,114,59,1,8662,114,4,2,104,114,14452,14456,107,59,1,10531,4,2,59,111,14462,14464,1,8598,119,59,1,8598,101,97,114,59,1,10535,4,18,83,97,99,100,101,102,103,104,105,108,109,111,112,114,115,116,117,118,14512,14515,14535,14560,14597,14603,14618,14643,14657,14662,14701,14741,14747,14769,14851,14877,14907,14916,59,1,9416,4,2,99,115,14521,14531,117,116,101,5,243,1,59,14529,1,243,116,59,1,8859,4,2,105,121,14541,14557,114,4,2,59,99,14548,14550,1,8858,5,244,1,59,14555,1,244,59,1,1086,4,5,97,98,105,111,115,14572,14577,14583,14587,14591,115,104,59,1,8861,108,97,99,59,1,337,118,59,1,10808,116,59,1,8857,111,108,100,59,1,10684,108,105,103,59,1,339,4,2,99,114,14609,14614,105,114,59,1,10687,59,3,55349,56620,4,3,111,114,116,14626,14630,14640,110,59,1,731,97,118,101,5,242,1,59,14638,1,242,59,1,10689,4,2,98,109,14649,14654,97,114,59,1,10677,59,1,937,110,116,59,1,8750,4,4,97,99,105,116,14672,14677,14693,14698,114,114,59,1,8634,4,2,105,114,14683,14687,114,59,1,10686,111,115,115,59,1,10683,110,101,59,1,8254,59,1,10688,4,3,97,101,105,14709,14714,14719,99,114,59,1,333,103,97,59,1,969,4,3,99,100,110,14727,14733,14736,114,111,110,59,1,959,59,1,10678,117,115,59,1,8854,112,102,59,3,55349,56672,4,3,97,101,108,14755,14759,14764,114,59,1,10679,114,112,59,1,10681,117,115,59,1,8853,4,7,59,97,100,105,111,115,118,14785,14787,14792,14831,14837,14841,14848,1,8744,114,114,59,1,8635,4,4,59,101,102,109,14802,14804,14817,14824,1,10845,114,4,2,59,111,14811,14813,1,8500,102,59,1,8500,5,170,1,59,14822,1,170,5,186,1,59,14829,1,186,103,111,102,59,1,8886,114,59,1,10838,108,111,112,101,59,1,10839,59,1,10843,4,3,99,108,111,14859,14863,14873,114,59,1,8500,97,115,104,5,248,1,59,14871,1,248,108,59,1,8856,105,4,2,108,109,14884,14893,100,101,5,245,1,59,14891,1,245,101,115,4,2,59,97,14901,14903,1,8855,115,59,1,10806,109,108,5,246,1,59,14914,1,246,98,97,114,59,1,9021,4,12,97,99,101,102,104,105,108,109,111,114,115,117,14948,14992,14996,15033,15038,15068,15090,15189,15192,15222,15427,15441,114,4,4,59,97,115,116,14959,14961,14976,14989,1,8741,5,182,2,59,108,14968,14970,1,182,108,101,108,59,1,8741,4,2,105,108,14982,14986,109,59,1,10995,59,1,11005,59,1,8706,121,59,1,1087,114,4,5,99,105,109,112,116,15009,15014,15019,15024,15027,110,116,59,1,37,111,100,59,1,46,105,108,59,1,8240,59,1,8869,101,110,107,59,1,8241,114,59,3,55349,56621,4,3,105,109,111,15046,15057,15063,4,2,59,118,15052,15054,1,966,59,1,981,109,97,116,59,1,8499,110,101,59,1,9742,4,3,59,116,118,15076,15078,15087,1,960,99,104,102,111,114,107,59,1,8916,59,1,982,4,2,97,117,15096,15119,110,4,2,99,107,15103,15115,107,4,2,59,104,15110,15112,1,8463,59,1,8462,118,59,1,8463,115,4,9,59,97,98,99,100,101,109,115,116,15140,15142,15148,15151,15156,15168,15171,15179,15184,1,43,99,105,114,59,1,10787,59,1,8862,105,114,59,1,10786,4,2,111,117,15162,15165,59,1,8724,59,1,10789,59,1,10866,110,5,177,1,59,15177,1,177,105,109,59,1,10790,119,111,59,1,10791,59,1,177,4,3,105,112,117,15200,15208,15213,110,116,105,110,116,59,1,10773,102,59,3,55349,56673,110,100,5,163,1,59,15220,1,163,4,10,59,69,97,99,101,105,110,111,115,117,15244,15246,15249,15253,15258,15334,15347,15367,15416,15421,1,8826,59,1,10931,112,59,1,10935,117,101,59,1,8828,4,2,59,99,15264,15266,1,10927,4,6,59,97,99,101,110,115,15280,15282,15290,15299,15303,15329,1,8826,112,112,114,111,120,59,1,10935,117,114,108,121,101,113,59,1,8828,113,59,1,10927,4,3,97,101,115,15311,15319,15324,112,112,114,111,120,59,1,10937,113,113,59,1,10933,105,109,59,1,8936,105,109,59,1,8830,109,101,4,2,59,115,15342,15344,1,8242,59,1,8473,4,3,69,97,115,15355,15358,15362,59,1,10933,112,59,1,10937,105,109,59,1,8936,4,3,100,102,112,15375,15378,15404,59,1,8719,4,3,97,108,115,15386,15392,15398,108,97,114,59,1,9006,105,110,101,59,1,8978,117,114,102,59,1,8979,4,2,59,116,15410,15412,1,8733,111,59,1,8733,105,109,59,1,8830,114,101,108,59,1,8880,4,2,99,105,15433,15438,114,59,3,55349,56517,59,1,968,110,99,115,112,59,1,8200,4,6,102,105,111,112,115,117,15462,15467,15472,15478,15485,15491,114,59,3,55349,56622,110,116,59,1,10764,112,102,59,3,55349,56674,114,105,109,101,59,1,8279,99,114,59,3,55349,56518,4,3,97,101,111,15499,15520,15534,116,4,2,101,105,15506,15515,114,110,105,111,110,115,59,1,8461,110,116,59,1,10774,115,116,4,2,59,101,15528,15530,1,63,113,59,1,8799,116,5,34,1,59,15540,1,34,4,21,65,66,72,97,98,99,100,101,102,104,105,108,109,110,111,112,114,115,116,117,120,15586,15609,15615,15620,15796,15855,15893,15931,15977,16001,16039,16183,16204,16222,16228,16285,16312,16318,16363,16408,16416,4,3,97,114,116,15594,15599,15603,114,114,59,1,8667,114,59,1,8658,97,105,108,59,1,10524,97,114,114,59,1,10511,97,114,59,1,10596,4,7,99,100,101,110,113,114,116,15636,15651,15656,15664,15687,15696,15770,4,2,101,117,15642,15646,59,3,8765,817,116,101,59,1,341,105,99,59,1,8730,109,112,116,121,118,59,1,10675,103,4,4,59,100,101,108,15675,15677,15680,15683,1,10217,59,1,10642,59,1,10661,101,59,1,10217,117,111,5,187,1,59,15694,1,187,114,4,11,59,97,98,99,102,104,108,112,115,116,119,15721,15723,15727,15739,15742,15746,15750,15754,15758,15763,15767,1,8594,112,59,1,10613,4,2,59,102,15733,15735,1,8677,115,59,1,10528,59,1,10547,115,59,1,10526,107,59,1,8618,112,59,1,8620,108,59,1,10565,105,109,59,1,10612,108,59,1,8611,59,1,8605,4,2,97,105,15776,15781,105,108,59,1,10522,111,4,2,59,110,15788,15790,1,8758,97,108,115,59,1,8474,4,3,97,98,114,15804,15809,15814,114,114,59,1,10509,114,107,59,1,10099,4,2,97,107,15820,15833,99,4,2,101,107,15827,15830,59,1,125,59,1,93,4,2,101,115,15839,15842,59,1,10636,108,4,2,100,117,15849,15852,59,1,10638,59,1,10640,4,4,97,101,117,121,15865,15871,15886,15890,114,111,110,59,1,345,4,2,100,105,15877,15882,105,108,59,1,343,108,59,1,8969,98,59,1,125,59,1,1088,4,4,99,108,113,115,15903,15907,15914,15927,97,59,1,10551,100,104,97,114,59,1,10601,117,111,4,2,59,114,15922,15924,1,8221,59,1,8221,104,59,1,8627,4,3,97,99,103,15939,15966,15970,108,4,4,59,105,112,115,15950,15952,15957,15963,1,8476,110,101,59,1,8475,97,114,116,59,1,8476,59,1,8477,116,59,1,9645,5,174,1,59,15975,1,174,4,3,105,108,114,15985,15991,15997,115,104,116,59,1,10621,111,111,114,59,1,8971,59,3,55349,56623,4,2,97,111,16007,16028,114,4,2,100,117,16014,16017,59,1,8641,4,2,59,108,16023,16025,1,8640,59,1,10604,4,2,59,118,16034,16036,1,961,59,1,1009,4,3,103,110,115,16047,16167,16171,104,116,4,6,97,104,108,114,115,116,16063,16081,16103,16130,16143,16155,114,114,111,119,4,2,59,116,16073,16075,1,8594,97,105,108,59,1,8611,97,114,112,111,111,110,4,2,100,117,16093,16099,111,119,110,59,1,8641,112,59,1,8640,101,102,116,4,2,97,104,16112,16120,114,114,111,119,115,59,1,8644,97,114,112,111,111,110,115,59,1,8652,105,103,104,116,97,114,114,111,119,115,59,1,8649,113,117,105,103,97,114,114,111,119,59,1,8605,104,114,101,101,116,105,109,101,115,59,1,8908,103,59,1,730,105,110,103,100,111,116,115,101,113,59,1,8787,4,3,97,104,109,16191,16196,16201,114,114,59,1,8644,97,114,59,1,8652,59,1,8207,111,117,115,116,4,2,59,97,16214,16216,1,9137,99,104,101,59,1,9137,109,105,100,59,1,10990,4,4,97,98,112,116,16238,16252,16257,16278,4,2,110,114,16244,16248,103,59,1,10221,114,59,1,8702,114,107,59,1,10215,4,3,97,102,108,16265,16269,16273,114,59,1,10630,59,3,55349,56675,117,115,59,1,10798,105,109,101,115,59,1,10805,4,2,97,112,16291,16304,114,4,2,59,103,16298,16300,1,41,116,59,1,10644,111,108,105,110,116,59,1,10770,97,114,114,59,1,8649,4,4,97,99,104,113,16328,16334,16339,16342,113,117,111,59,1,8250,114,59,3,55349,56519,59,1,8625,4,2,98,117,16348,16351,59,1,93,111,4,2,59,114,16358,16360,1,8217,59,1,8217,4,3,104,105,114,16371,16377,16383,114,101,101,59,1,8908,109,101,115,59,1,8906,105,4,4,59,101,102,108,16394,16396,16399,16402,1,9657,59,1,8885,59,1,9656,116,114,105,59,1,10702,108,117,104,97,114,59,1,10600,59,1,8478,4,19,97,98,99,100,101,102,104,105,108,109,111,112,113,114,115,116,117,119,122,16459,16466,16472,16572,16590,16672,16687,16746,16844,16850,16924,16963,16988,17115,17121,17154,17206,17614,17656,99,117,116,101,59,1,347,113,117,111,59,1,8218,4,10,59,69,97,99,101,105,110,112,115,121,16494,16496,16499,16513,16518,16531,16536,16556,16564,16569,1,8827,59,1,10932,4,2,112,114,16505,16508,59,1,10936,111,110,59,1,353,117,101,59,1,8829,4,2,59,100,16524,16526,1,10928,105,108,59,1,351,114,99,59,1,349,4,3,69,97,115,16544,16547,16551,59,1,10934,112,59,1,10938,105,109,59,1,8937,111,108,105,110,116,59,1,10771,105,109,59,1,8831,59,1,1089,111,116,4,3,59,98,101,16582,16584,16587,1,8901,59,1,8865,59,1,10854,4,7,65,97,99,109,115,116,120,16606,16611,16634,16642,16646,16652,16668,114,114,59,1,8664,114,4,2,104,114,16618,16622,107,59,1,10533,4,2,59,111,16628,16630,1,8600,119,59,1,8600,116,5,167,1,59,16640,1,167,105,59,1,59,119,97,114,59,1,10537,109,4,2,105,110,16659,16665,110,117,115,59,1,8726,59,1,8726,116,59,1,10038,114,4,2,59,111,16679,16682,3,55349,56624,119,110,59,1,8994,4,4,97,99,111,121,16697,16702,16716,16739,114,112,59,1,9839,4,2,104,121,16708,16713,99,121,59,1,1097,59,1,1096,114,116,4,2,109,112,16724,16729,105,100,59,1,8739,97,114,97,108,108,101,108,59,1,8741,5,173,1,59,16744,1,173,4,2,103,109,16752,16770,109,97,4,3,59,102,118,16762,16764,16767,1,963,59,1,962,59,1,962,4,8,59,100,101,103,108,110,112,114,16788,16790,16795,16806,16817,16828,16832,16838,1,8764,111,116,59,1,10858,4,2,59,113,16801,16803,1,8771,59,1,8771,4,2,59,69,16812,16814,1,10910,59,1,10912,4,2,59,69,16823,16825,1,10909,59,1,10911,101,59,1,8774,108,117,115,59,1,10788,97,114,114,59,1,10610,97,114,114,59,1,8592,4,4,97,101,105,116,16860,16883,16891,16904,4,2,108,115,16866,16878,108,115,101,116,109,105,110,117,115,59,1,8726,104,112,59,1,10803,112,97,114,115,108,59,1,10724,4,2,100,108,16897,16900,59,1,8739,101,59,1,8995,4,2,59,101,16910,16912,1,10922,4,2,59,115,16918,16920,1,10924,59,3,10924,65024,4,3,102,108,112,16932,16938,16958,116,99,121,59,1,1100,4,2,59,98,16944,16946,1,47,4,2,59,97,16952,16954,1,10692,114,59,1,9023,102,59,3,55349,56676,97,4,2,100,114,16970,16985,101,115,4,2,59,117,16978,16980,1,9824,105,116,59,1,9824,59,1,8741,4,3,99,115,117,16996,17028,17089,4,2,97,117,17002,17015,112,4,2,59,115,17009,17011,1,8851,59,3,8851,65024,112,4,2,59,115,17022,17024,1,8852,59,3,8852,65024,117,4,2,98,112,17035,17062,4,3,59,101,115,17043,17045,17048,1,8847,59,1,8849,101,116,4,2,59,101,17056,17058,1,8847,113,59,1,8849,4,3,59,101,115,17070,17072,17075,1,8848,59,1,8850,101,116,4,2,59,101,17083,17085,1,8848,113,59,1,8850,4,3,59,97,102,17097,17099,17112,1,9633,114,4,2,101,102,17106,17109,59,1,9633,59,1,9642,59,1,9642,97,114,114,59,1,8594,4,4,99,101,109,116,17131,17136,17142,17148,114,59,3,55349,56520,116,109,110,59,1,8726,105,108,101,59,1,8995,97,114,102,59,1,8902,4,2,97,114,17160,17172,114,4,2,59,102,17167,17169,1,9734,59,1,9733,4,2,97,110,17178,17202,105,103,104,116,4,2,101,112,17188,17197,112,115,105,108,111,110,59,1,1013,104,105,59,1,981,115,59,1,175,4,5,98,99,109,110,112,17218,17351,17420,17423,17427,4,9,59,69,100,101,109,110,112,114,115,17238,17240,17243,17248,17261,17267,17279,17285,17291,1,8834,59,1,10949,111,116,59,1,10941,4,2,59,100,17254,17256,1,8838,111,116,59,1,10947,117,108,116,59,1,10945,4,2,69,101,17273,17276,59,1,10955,59,1,8842,108,117,115,59,1,10943,97,114,114,59,1,10617,4,3,101,105,117,17299,17335,17339,116,4,3,59,101,110,17308,17310,17322,1,8834,113,4,2,59,113,17317,17319,1,8838,59,1,10949,101,113,4,2,59,113,17330,17332,1,8842,59,1,10955,109,59,1,10951,4,2,98,112,17345,17348,59,1,10965,59,1,10963,99,4,6,59,97,99,101,110,115,17366,17368,17376,17385,17389,17415,1,8827,112,112,114,111,120,59,1,10936,117,114,108,121,101,113,59,1,8829,113,59,1,10928,4,3,97,101,115,17397,17405,17410,112,112,114,111,120,59,1,10938,113,113,59,1,10934,105,109,59,1,8937,105,109,59,1,8831,59,1,8721,103,59,1,9834,4,13,49,50,51,59,69,100,101,104,108,109,110,112,115,17455,17462,17469,17476,17478,17481,17496,17509,17524,17530,17536,17548,17554,5,185,1,59,17460,1,185,5,178,1,59,17467,1,178,5,179,1,59,17474,1,179,1,8835,59,1,10950,4,2,111,115,17487,17491,116,59,1,10942,117,98,59,1,10968,4,2,59,100,17502,17504,1,8839,111,116,59,1,10948,115,4,2,111,117,17516,17520,108,59,1,10185,98,59,1,10967,97,114,114,59,1,10619,117,108,116,59,1,10946,4,2,69,101,17542,17545,59,1,10956,59,1,8843,108,117,115,59,1,10944,4,3,101,105,117,17562,17598,17602,116,4,3,59,101,110,17571,17573,17585,1,8835,113,4,2,59,113,17580,17582,1,8839,59,1,10950,101,113,4,2,59,113,17593,17595,1,8843,59,1,10956,109,59,1,10952,4,2,98,112,17608,17611,59,1,10964,59,1,10966,4,3,65,97,110,17622,17627,17650,114,114,59,1,8665,114,4,2,104,114,17634,17638,107,59,1,10534,4,2,59,111,17644,17646,1,8601,119,59,1,8601,119,97,114,59,1,10538,108,105,103,5,223,1,59,17664,1,223,4,13,97,98,99,100,101,102,104,105,111,112,114,115,119,17694,17709,17714,17737,17742,17749,17754,17860,17905,17957,17964,18090,18122,4,2,114,117,17700,17706,103,101,116,59,1,8982,59,1,964,114,107,59,1,9140,4,3,97,101,121,17722,17728,17734,114,111,110,59,1,357,100,105,108,59,1,355,59,1,1090,111,116,59,1,8411,108,114,101,99,59,1,8981,114,59,3,55349,56625,4,4,101,105,107,111,17764,17805,17836,17851,4,2,114,116,17770,17786,101,4,2,52,102,17777,17780,59,1,8756,111,114,101,59,1,8756,97,4,3,59,115,118,17795,17797,17802,1,952,121,109,59,1,977,59,1,977,4,2,99,110,17811,17831,107,4,2,97,115,17818,17826,112,112,114,111,120,59,1,8776,105,109,59,1,8764,115,112,59,1,8201,4,2,97,115,17842,17846,112,59,1,8776,105,109,59,1,8764,114,110,5,254,1,59,17858,1,254,4,3,108,109,110,17868,17873,17901,100,101,59,1,732,101,115,5,215,3,59,98,100,17884,17886,17898,1,215,4,2,59,97,17892,17894,1,8864,114,59,1,10801,59,1,10800,116,59,1,8749,4,3,101,112,115,17913,17917,17953,97,59,1,10536,4,4,59,98,99,102,17927,17929,17934,17939,1,8868,111,116,59,1,9014,105,114,59,1,10993,4,2,59,111,17945,17948,3,55349,56677,114,107,59,1,10970,97,59,1,10537,114,105,109,101,59,1,8244,4,3,97,105,112,17972,17977,18082,100,101,59,1,8482,4,7,97,100,101,109,112,115,116,17993,18051,18056,18059,18066,18072,18076,110,103,108,101,4,5,59,100,108,113,114,18009,18011,18017,18032,18035,1,9653,111,119,110,59,1,9663,101,102,116,4,2,59,101,18026,18028,1,9667,113,59,1,8884,59,1,8796,105,103,104,116,4,2,59,101,18045,18047,1,9657,113,59,1,8885,111,116,59,1,9708,59,1,8796,105,110,117,115,59,1,10810,108,117,115,59,1,10809,98,59,1,10701,105,109,101,59,1,10811,101,122,105,117,109,59,1,9186,4,3,99,104,116,18098,18111,18116,4,2,114,121,18104,18108,59,3,55349,56521,59,1,1094,99,121,59,1,1115,114,111,107,59,1,359,4,2,105,111,18128,18133,120,116,59,1,8812,104,101,97,100,4,2,108,114,18143,18154,101,102,116,97,114,114,111,119,59,1,8606,105,103,104,116,97,114,114,111,119,59,1,8608,4,18,65,72,97,98,99,100,102,103,104,108,109,111,112,114,115,116,117,119,18204,18209,18214,18234,18250,18268,18292,18308,18319,18343,18379,18397,18413,18504,18547,18553,18584,18603,114,114,59,1,8657,97,114,59,1,10595,4,2,99,114,18220,18230,117,116,101,5,250,1,59,18228,1,250,114,59,1,8593,114,4,2,99,101,18241,18245,121,59,1,1118,118,101,59,1,365,4,2,105,121,18256,18265,114,99,5,251,1,59,18263,1,251,59,1,1091,4,3,97,98,104,18276,18281,18287,114,114,59,1,8645,108,97,99,59,1,369,97,114,59,1,10606,4,2,105,114,18298,18304,115,104,116,59,1,10622,59,3,55349,56626,114,97,118,101,5,249,1,59,18317,1,249,4,2,97,98,18325,18338,114,4,2,108,114,18332,18335,59,1,8639,59,1,8638,108,107,59,1,9600,4,2,99,116,18349,18374,4,2,111,114,18355,18369,114,110,4,2,59,101,18363,18365,1,8988,114,59,1,8988,111,112,59,1,8975,114,105,59,1,9720,4,2,97,108,18385,18390,99,114,59,1,363,5,168,1,59,18395,1,168,4,2,103,112,18403,18408,111,110,59,1,371,102,59,3,55349,56678,4,6,97,100,104,108,115,117,18427,18434,18445,18470,18475,18494,114,114,111,119,59,1,8593,111,119,110,97,114,114,111,119,59,1,8597,97,114,112,111,111,110,4,2,108,114,18457,18463,101,102,116,59,1,8639,105,103,104,116,59,1,8638,117,115,59,1,8846,105,4,3,59,104,108,18484,18486,18489,1,965,59,1,978,111,110,59,1,965,112,97,114,114,111,119,115,59,1,8648,4,3,99,105,116,18512,18537,18542,4,2,111,114,18518,18532,114,110,4,2,59,101,18526,18528,1,8989,114,59,1,8989,111,112,59,1,8974,110,103,59,1,367,114,105,59,1,9721,99,114,59,3,55349,56522,4,3,100,105,114,18561,18566,18572,111,116,59,1,8944,108,100,101,59,1,361,105,4,2,59,102,18579,18581,1,9653,59,1,9652,4,2,97,109,18590,18595,114,114,59,1,8648,108,5,252,1,59,18601,1,252,97,110,103,108,101,59,1,10663,4,15,65,66,68,97,99,100,101,102,108,110,111,112,114,115,122,18643,18648,18661,18667,18847,18851,18857,18904,18909,18915,18931,18937,18943,18949,18996,114,114,59,1,8661,97,114,4,2,59,118,18656,18658,1,10984,59,1,10985,97,115,104,59,1,8872,4,2,110,114,18673,18679,103,114,116,59,1,10652,4,7,101,107,110,112,114,115,116,18695,18704,18711,18720,18742,18754,18810,112,115,105,108,111,110,59,1,1013,97,112,112,97,59,1,1008,111,116,104,105,110,103,59,1,8709,4,3,104,105,114,18728,18732,18735,105,59,1,981,59,1,982,111,112,116,111,59,1,8733,4,2,59,104,18748,18750,1,8597,111,59,1,1009,4,2,105,117,18760,18766,103,109,97,59,1,962,4,2,98,112,18772,18791,115,101,116,110,101,113,4,2,59,113,18784,18787,3,8842,65024,59,3,10955,65024,115,101,116,110,101,113,4,2,59,113,18803,18806,3,8843,65024,59,3,10956,65024,4,2,104,114,18816,18822,101,116,97,59,1,977,105,97,110,103,108,101,4,2,108,114,18834,18840,101,102,116,59,1,8882,105,103,104,116,59,1,8883,121,59,1,1074,97,115,104,59,1,8866,4,3,101,108,114,18865,18884,18890,4,3,59,98,101,18873,18875,18880,1,8744,97,114,59,1,8891,113,59,1,8794,108,105,112,59,1,8942,4,2,98,116,18896,18901,97,114,59,1,124,59,1,124,114,59,3,55349,56627,116,114,105,59,1,8882,115,117,4,2,98,112,18923,18927,59,3,8834,8402,59,3,8835,8402,112,102,59,3,55349,56679,114,111,112,59,1,8733,116,114,105,59,1,8883,4,2,99,117,18955,18960,114,59,3,55349,56523,4,2,98,112,18966,18981,110,4,2,69,101,18973,18977,59,3,10955,65024,59,3,8842,65024,110,4,2,69,101,18988,18992,59,3,10956,65024,59,3,8843,65024,105,103,122,97,103,59,1,10650,4,7,99,101,102,111,112,114,115,19020,19026,19061,19066,19072,19075,19089,105,114,99,59,1,373,4,2,100,105,19032,19055,4,2,98,103,19038,19043,97,114,59,1,10847,101,4,2,59,113,19050,19052,1,8743,59,1,8793,101,114,112,59,1,8472,114,59,3,55349,56628,112,102,59,3,55349,56680,59,1,8472,4,2,59,101,19081,19083,1,8768,97,116,104,59,1,8768,99,114,59,3,55349,56524,4,14,99,100,102,104,105,108,109,110,111,114,115,117,118,119,19125,19146,19152,19157,19173,19176,19192,19197,19202,19236,19252,19269,19286,19291,4,3,97,105,117,19133,19137,19142,112,59,1,8898,114,99,59,1,9711,112,59,1,8899,116,114,105,59,1,9661,114,59,3,55349,56629,4,2,65,97,19163,19168,114,114,59,1,10234,114,114,59,1,10231,59,1,958,4,2,65,97,19182,19187,114,114,59,1,10232,114,114,59,1,10229,97,112,59,1,10236,105,115,59,1,8955,4,3,100,112,116,19210,19215,19230,111,116,59,1,10752,4,2,102,108,19221,19225,59,3,55349,56681,117,115,59,1,10753,105,109,101,59,1,10754,4,2,65,97,19242,19247,114,114,59,1,10233,114,114,59,1,10230,4,2,99,113,19258,19263,114,59,3,55349,56525,99,117,112,59,1,10758,4,2,112,116,19275,19281,108,117,115,59,1,10756,114,105,59,1,9651,101,101,59,1,8897,101,100,103,101,59,1,8896,4,8,97,99,101,102,105,111,115,117,19316,19335,19349,19357,19362,19367,19373,19379,99,4,2,117,121,19323,19332,116,101,5,253,1,59,19330,1,253,59,1,1103,4,2,105,121,19341,19346,114,99,59,1,375,59,1,1099,110,5,165,1,59,19355,1,165,114,59,3,55349,56630,99,121,59,1,1111,112,102,59,3,55349,56682,99,114,59,3,55349,56526,4,2,99,109,19385,19389,121,59,1,1102,108,5,255,1,59,19395,1,255,4,10,97,99,100,101,102,104,105,111,115,119,19419,19426,19441,19446,19462,19467,19472,19480,19486,19492,99,117,116,101,59,1,378,4,2,97,121,19432,19438,114,111,110,59,1,382,59,1,1079,111,116,59,1,380,4,2,101,116,19452,19458,116,114,102,59,1,8488,97,59,1,950,114,59,3,55349,56631,99,121,59,1,1078,103,114,97,114,114,59,1,8669,112,102,59,3,55349,56683,99,114,59,3,55349,56527,4,2,106,110,19498,19501,59,1,8205,106,59,1,8204])},5238:(e,t,n)=>{"use strict";var r=n(3306).CODE_POINTS,s=e.exports=function(){this.html=null,this.pos=-1,this.lastGapPos=-1,this.lastCharPos=-1,this.droppedBufferSize=0,this.gapStack=[],this.skipNextNewLine=!1,this.lastChunkWritten=!1,this.endOfChunkHit=!1,this.bufferWaterline=65536};Object.defineProperty(s.prototype,"sourcePos",{get:function(){return this.droppedBufferSize+this.pos}}),s.prototype.dropParsedChunk=function(){this.pos>this.bufferWaterline&&(this.lastCharPos-=this.pos,this.droppedBufferSize+=this.pos,this.html=this.html.substring(this.pos),this.pos=0,this.lastGapPos=-1,this.gapStack=[])},s.prototype._addGap=function(){this.gapStack.push(this.lastGapPos),this.lastGapPos=this.pos},s.prototype._processHighRangeCodePoint=function(e){if(this.pos!==this.lastCharPos){var t=this.html.charCodeAt(this.pos+1);s=t,(n=e)>=55296&&n<=56319&&s>=56320&&s<=57343&&(this.pos++,e=function(e,t){return 1024*(e-55296)+9216+t}(e,t),this._addGap())}else if(!this.lastChunkWritten)return this.endOfChunkHit=!0,r.EOF;var n,s;return e},s.prototype.write=function(e,t){this.html?this.html+=e:this.html=e,this.lastCharPos=this.html.length-1,this.endOfChunkHit=!1,this.lastChunkWritten=t},s.prototype.insertHtmlAtCurrentPos=function(e){this.html=this.html.substring(0,this.pos+1)+e+this.html.substring(this.pos+1,this.html.length),this.lastCharPos=this.html.length-1,this.endOfChunkHit=!1},s.prototype.advance=function(){if(this.pos++,this.pos>this.lastCharPos)return this.lastChunkWritten||(this.endOfChunkHit=!0),r.EOF;var e=this.html.charCodeAt(this.pos);return this.skipNextNewLine&&e===r.LINE_FEED?(this.skipNextNewLine=!1,this._addGap(),this.advance()):e===r.CARRIAGE_RETURN?(this.skipNextNewLine=!0,r.LINE_FEED):(this.skipNextNewLine=!1,e>=55296?this._processHighRangeCodePoint(e):e)},s.prototype.retreat=function(){this.pos===this.lastGapPos&&(this.lastGapPos=this.gapStack.pop(),this.pos--),this.pos--}},7988:(e,t,n)=>{"use strict";var r=n(6584).DOCUMENT_MODE;t.createDocument=function(){return{nodeName:"#document",mode:r.NO_QUIRKS,childNodes:[]}},t.createDocumentFragment=function(){return{nodeName:"#document-fragment",childNodes:[]}},t.createElement=function(e,t,n){return{nodeName:e,tagName:e,attrs:n,namespaceURI:t,childNodes:[],parentNode:null}},t.createCommentNode=function(e){return{nodeName:"#comment",data:e,parentNode:null}};var s=function(e){return{nodeName:"#text",value:e,parentNode:null}},i=t.appendChild=function(e,t){e.childNodes.push(t),t.parentNode=e},o=t.insertBefore=function(e,t,n){var r=e.childNodes.indexOf(n);e.childNodes.splice(r,0,t),t.parentNode=e};t.setTemplateContent=function(e,t){e.content=t},t.getTemplateContent=function(e){return e.content},t.setDocumentType=function(e,t,n,r){for(var s=null,o=0;o<e.childNodes.length;o++)if("#documentType"===e.childNodes[o].nodeName){s=e.childNodes[o];break}s?(s.name=t,s.publicId=n,s.systemId=r):i(e,{nodeName:"#documentType",name:t,publicId:n,systemId:r})},t.setDocumentMode=function(e,t){e.mode=t},t.getDocumentMode=function(e){return e.mode},t.detachNode=function(e){if(e.parentNode){var t=e.parentNode.childNodes.indexOf(e);e.parentNode.childNodes.splice(t,1),e.parentNode=null}},t.insertText=function(e,t){if(e.childNodes.length){var n=e.childNodes[e.childNodes.length-1];if("#text"===n.nodeName)return void(n.value+=t)}i(e,s(t))},t.insertTextBefore=function(e,t,n){var r=e.childNodes[e.childNodes.indexOf(n)-1];r&&"#text"===r.nodeName?r.value+=t:o(e,s(t),n)},t.adoptAttributes=function(e,t){for(var n=[],r=0;r<e.attrs.length;r++)n.push(e.attrs[r].name);for(var s=0;s<t.length;s++)-1===n.indexOf(t[s].name)&&e.attrs.push(t[s])},t.getFirstChild=function(e){return e.childNodes[0]},t.getChildNodes=function(e){return e.childNodes},t.getParentNode=function(e){return e.parentNode},t.getAttrList=function(e){return e.attrs},t.getTagName=function(e){return e.tagName},t.getNamespaceURI=function(e){return e.namespaceURI},t.getTextNodeContent=function(e){return e.value},t.getCommentNodeContent=function(e){return e.data},t.getDocumentTypeNodeName=function(e){return e.name},t.getDocumentTypeNodePublicId=function(e){return e.publicId},t.getDocumentTypeNodeSystemId=function(e){return e.systemId},t.isTextNode=function(e){return"#text"===e.nodeName},t.isCommentNode=function(e){return"#comment"===e.nodeName},t.isDocumentTypeNode=function(e){return"#documentType"===e.nodeName},t.isElementNode=function(e){return!!e.tagName}},8537:(e,t,n)=>{"use strict";var r=n(135),s=n(6584).DOCUMENT_MODE,i={element:1,text:3,cdata:4,comment:8},o={tagName:"name",childNodes:"children",parentNode:"parent",previousSibling:"prev",nextSibling:"next",nodeValue:"data"},a=function(e){for(var t in e)e.hasOwnProperty(t)&&(this[t]=e[t])};a.prototype={get firstChild(){var e=this.children;return e&&e[0]||null},get lastChild(){var e=this.children;return e&&e[e.length-1]||null},get nodeType(){return i[this.type]||i.element}},Object.keys(o).forEach((function(e){var t=o[e];Object.defineProperty(a.prototype,e,{get:function(){return this[t]||null},set:function(e){return this[t]=e,e}})})),t.createDocument=function(){return new a({type:"root",name:"root",parent:null,prev:null,next:null,children:[],"x-mode":s.NO_QUIRKS})},t.createDocumentFragment=function(){return new a({type:"root",name:"root",parent:null,prev:null,next:null,children:[]})},t.createElement=function(e,t,n){for(var r=Object.create(null),s=Object.create(null),i=Object.create(null),o=0;o<n.length;o++){var c=n[o].name;r[c]=n[o].value,s[c]=n[o].namespace,i[c]=n[o].prefix}return new a({type:"script"===e||"style"===e?e:"tag",name:e,namespace:t,attribs:r,"x-attribsNamespace":s,"x-attribsPrefix":i,children:[],parent:null,prev:null,next:null})},t.createCommentNode=function(e){return new a({type:"comment",data:e,parent:null,prev:null,next:null})};var c=function(e){return new a({type:"text",data:e,parent:null,prev:null,next:null})},l=t.appendChild=function(e,t){var n=e.children[e.children.length-1];n&&(n.next=t,t.prev=n),e.children.push(t),t.parent=e},u=t.insertBefore=function(e,t,n){var r=e.children.indexOf(n),s=n.prev;s&&(s.next=t,t.prev=s),n.prev=t,t.next=n,e.children.splice(r,0,t),t.parent=e};t.setTemplateContent=function(e,t){l(e,t)},t.getTemplateContent=function(e){return e.children[0]},t.setDocumentType=function(e,t,n,s){for(var i=r.serializeContent(t,n,s),o=null,c=0;c<e.children.length;c++)if("directive"===e.children[c].type&&"!doctype"===e.children[c].name){o=e.children[c];break}o?(o.data=i,o["x-name"]=t,o["x-publicId"]=n,o["x-systemId"]=s):l(e,new a({type:"directive",name:"!doctype",data:i,"x-name":t,"x-publicId":n,"x-systemId":s}))},t.setDocumentMode=function(e,t){e["x-mode"]=t},t.getDocumentMode=function(e){return e["x-mode"]},t.detachNode=function(e){if(e.parent){var t=e.parent.children.indexOf(e),n=e.prev,r=e.next;e.prev=null,e.next=null,n&&(n.next=r),r&&(r.prev=n),e.parent.children.splice(t,1),e.parent=null}},t.insertText=function(e,t){var n=e.children[e.children.length-1];n&&"text"===n.type?n.data+=t:l(e,c(t))},t.insertTextBefore=function(e,t,n){var r=e.children[e.children.indexOf(n)-1];r&&"text"===r.type?r.data+=t:u(e,c(t),n)},t.adoptAttributes=function(e,t){for(var n=0;n<t.length;n++){var r=t[n].name;void 0===e.attribs[r]&&(e.attribs[r]=t[n].value,e["x-attribsNamespace"][r]=t[n].namespace,e["x-attribsPrefix"][r]=t[n].prefix)}},t.getFirstChild=function(e){return e.children[0]},t.getChildNodes=function(e){return e.children},t.getParentNode=function(e){return e.parent},t.getAttrList=function(e){var t=[];for(var n in e.attribs)t.push({name:n,value:e.attribs[n],namespace:e["x-attribsNamespace"][n],prefix:e["x-attribsPrefix"][n]});return t},t.getTagName=function(e){return e.name},t.getNamespaceURI=function(e){return e.namespace},t.getTextNodeContent=function(e){return e.data},t.getCommentNodeContent=function(e){return e.data},t.getDocumentTypeNodeName=function(e){return e["x-name"]},t.getDocumentTypeNodePublicId=function(e){return e["x-publicId"]},t.getDocumentTypeNodeSystemId=function(e){return e["x-systemId"]},t.isTextNode=function(e){return"text"===e.type},t.isCommentNode=function(e){return"comment"===e.type},t.isDocumentTypeNode=function(e){return"directive"===e.type&&"!doctype"===e.name},t.isElementNode=function(e){return!!e.attribs}},592:e=>{e.exports=/[\0-\x1F\x7F-\x9F]/},2675:e=>{e.exports=/[\xAD\u0600-\u0605\u061C\u06DD\u070F\u08E2\u180E\u200B-\u200F\u202A-\u202E\u2060-\u2064\u2066-\u206F\uFEFF\uFFF9-\uFFFB]|\uD804[\uDCBD\uDCCD]|\uD82F[\uDCA0-\uDCA3]|\uD834[\uDD73-\uDD7A]|\uDB40[\uDC01\uDC20-\uDC7F]/},2828:e=>{e.exports=/[!-#%-\*,-\/:;\?@\[-\]_\{\}\xA1\xA7\xAB\xB6\xB7\xBB\xBF\u037E\u0387\u055A-\u055F\u0589\u058A\u05BE\u05C0\u05C3\u05C6\u05F3\u05F4\u0609\u060A\u060C\u060D\u061B\u061E\u061F\u066A-\u066D\u06D4\u0700-\u070D\u07F7-\u07F9\u0830-\u083E\u085E\u0964\u0965\u0970\u09FD\u0A76\u0AF0\u0C84\u0DF4\u0E4F\u0E5A\u0E5B\u0F04-\u0F12\u0F14\u0F3A-\u0F3D\u0F85\u0FD0-\u0FD4\u0FD9\u0FDA\u104A-\u104F\u10FB\u1360-\u1368\u1400\u166D\u166E\u169B\u169C\u16EB-\u16ED\u1735\u1736\u17D4-\u17D6\u17D8-\u17DA\u1800-\u180A\u1944\u1945\u1A1E\u1A1F\u1AA0-\u1AA6\u1AA8-\u1AAD\u1B5A-\u1B60\u1BFC-\u1BFF\u1C3B-\u1C3F\u1C7E\u1C7F\u1CC0-\u1CC7\u1CD3\u2010-\u2027\u2030-\u2043\u2045-\u2051\u2053-\u205E\u207D\u207E\u208D\u208E\u2308-\u230B\u2329\u232A\u2768-\u2775\u27C5\u27C6\u27E6-\u27EF\u2983-\u2998\u29D8-\u29DB\u29FC\u29FD\u2CF9-\u2CFC\u2CFE\u2CFF\u2D70\u2E00-\u2E2E\u2E30-\u2E4E\u3001-\u3003\u3008-\u3011\u3014-\u301F\u3030\u303D\u30A0\u30FB\uA4FE\uA4FF\uA60D-\uA60F\uA673\uA67E\uA6F2-\uA6F7\uA874-\uA877\uA8CE\uA8CF\uA8F8-\uA8FA\uA8FC\uA92E\uA92F\uA95F\uA9C1-\uA9CD\uA9DE\uA9DF\uAA5C-\uAA5F\uAADE\uAADF\uAAF0\uAAF1\uABEB\uFD3E\uFD3F\uFE10-\uFE19\uFE30-\uFE52\uFE54-\uFE61\uFE63\uFE68\uFE6A\uFE6B\uFF01-\uFF03\uFF05-\uFF0A\uFF0C-\uFF0F\uFF1A\uFF1B\uFF1F\uFF20\uFF3B-\uFF3D\uFF3F\uFF5B\uFF5D\uFF5F-\uFF65]|\uD800[\uDD00-\uDD02\uDF9F\uDFD0]|\uD801\uDD6F|\uD802[\uDC57\uDD1F\uDD3F\uDE50-\uDE58\uDE7F\uDEF0-\uDEF6\uDF39-\uDF3F\uDF99-\uDF9C]|\uD803[\uDF55-\uDF59]|\uD804[\uDC47-\uDC4D\uDCBB\uDCBC\uDCBE-\uDCC1\uDD40-\uDD43\uDD74\uDD75\uDDC5-\uDDC8\uDDCD\uDDDB\uDDDD-\uDDDF\uDE38-\uDE3D\uDEA9]|\uD805[\uDC4B-\uDC4F\uDC5B\uDC5D\uDCC6\uDDC1-\uDDD7\uDE41-\uDE43\uDE60-\uDE6C\uDF3C-\uDF3E]|\uD806[\uDC3B\uDE3F-\uDE46\uDE9A-\uDE9C\uDE9E-\uDEA2]|\uD807[\uDC41-\uDC45\uDC70\uDC71\uDEF7\uDEF8]|\uD809[\uDC70-\uDC74]|\uD81A[\uDE6E\uDE6F\uDEF5\uDF37-\uDF3B\uDF44]|\uD81B[\uDE97-\uDE9A]|\uD82F\uDC9F|\uD836[\uDE87-\uDE8B]|\uD83A[\uDD5E\uDD5F]/},3978:e=>{e.exports=/[ \xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000]/},9295:(e,t,n)=>{"use strict";t.Any=n(6027),t.Cc=n(592),t.Cf=n(2675),t.P=n(2828),t.Z=n(3978)},6027:e=>{e.exports=/[\0-\uD7FF\uE000-\uFFFF]|[\uD800-\uDBFF][\uDC00-\uDFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF]/},4712:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.redundantImplicitActivationEvent=t.implicitActivationEvent=void 0;const r=n(1398);t.implicitActivationEvent=r.l10n.t("This activation event cannot be explicitly listed by your extension."),t.redundantImplicitActivationEvent=r.l10n.t("This activation event can be removed as VS Code generates these automatically from your package.json contribution declarations.")},5059:function(e,t,n){"use strict";var r,s=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var s=Object.getOwnPropertyDescriptor(t,n);s&&!("get"in s?!t.__esModule:s.writable||s.configurable)||(s={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,s)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),i=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),o=this&&this.__importStar||(r=function(e){return r=Object.getOwnPropertyNames||function(e){var t=[];for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[t.length]=n);return t},r(e)},function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n=r(e),o=0;o<n.length;o++)"default"!==n[o]&&s(t,e,n[o]);return i(t,e),t});Object.defineProperty(t,"__esModule",{value:!0}),t.activate=function(e){e.subscriptions.push(a.languages.registerCompletionItemProvider({language:"json",pattern:"**/package.json"},{provideCompletionItems:(e,t,n)=>new c.PackageDocument(e).provideCompletionItems(t,n)})),e.subscriptions.push(a.languages.registerCodeActionsProvider({language:"json",pattern:"**/package.json"},{provideCodeActions:(e,t,n,r)=>new c.PackageDocument(e).provideCodeActions(t,n,r)})),e.subscriptions.push(new l.ExtensionLinter)};const a=o(n(1398)),c=n(7972),l=n(3462)},7105:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.isValidVersionStr=s,t.parseVersion=function(e){if(!s(e))return null;if("*"===(e=e.trim()))return{hasCaret:!1,hasGreaterEquals:!1,majorBase:0,majorMustEqual:!1,minorBase:0,minorMustEqual:!1,patchBase:0,patchMustEqual:!1,preRelease:null};const t=e.match(n);return t?{hasCaret:"^"===t[1],hasGreaterEquals:">="===t[1],majorBase:"x"===t[2]?0:parseInt(t[2],10),majorMustEqual:"x"!==t[2],minorBase:"x"===t[4]?0:parseInt(t[4],10),minorMustEqual:"x"!==t[4],patchBase:"x"===t[6]?0:parseInt(t[6],10),patchMustEqual:"x"!==t[6],preRelease:t[8]||null}:null},t.normalizeVersion=function(e){if(!e)return null;const t=e.majorBase,n=e.majorMustEqual,s=e.minorBase;let i=e.minorMustEqual;const o=e.patchBase;let a=e.patchMustEqual;e.hasCaret&&(0===t||(i=!1),a=!1);let c=0;if(e.preRelease){const t=r.exec(e.preRelease);if(t){const[,e,n,r]=t;c=Date.UTC(Number(e),Number(n)-1,Number(r))}}return{majorBase:t,majorMustEqual:n,minorBase:s,minorMustEqual:i,patchBase:o,patchMustEqual:a,isMinimum:e.hasGreaterEquals,notBefore:c}};const n=/^(\^|>=)?((\d+)|x)\.((\d+)|x)\.((\d+)|x)(\-.*)?$/,r=/^-(\d{4})(\d{2})(\d{2})$/;function s(e){return"*"===(e=e.trim())||n.test(e)}},3462:function(e,t,n){"use strict";var r,s=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var s=Object.getOwnPropertyDescriptor(t,n);s&&!("get"in s?!t.__esModule:s.writable||s.configurable)||(s={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,s)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),i=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),o=this&&this.__importStar||(r=function(e){return r=Object.getOwnPropertyNames||function(e){var t=[];for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[t.length]=n);return t},r(e)},function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n=r(e),o=0;o<n.length;o++)"default"!==n[o]&&s(t,e,n[o]);return i(t,e),t});Object.defineProperty(t,"__esModule",{value:!0}),t.ExtensionLinter=void 0;const a=o(n(6928)),c=o(n(9896)),l=n(7016),u=n(5887),p=n(1398),h=n(7105),f=n(9437),T=n(4712),d=JSON.parse(c.readFileSync(a.join(p.env.appRoot,"product.json"),{encoding:"utf-8"})),m=(d.extensionAllowedBadgeProviders||[]).map((e=>e.toLowerCase())),E=(d.extensionAllowedBadgeProvidersRegex||[]).map((e=>new RegExp(e))),_=d.extensionEnabledApiProposals??{},g=["onNotebookSerializer:"],A=["onLanguage:","onView:","onAuthenticationRequest:","onCommand:","onCustomEditor:","onTerminalProfile:","onRenderer:","onTerminalQuickFixRequest:","onWalkthrough:"],C=p.l10n.t("Images must use the HTTPS protocol."),N=p.l10n.t("SVGs are not a valid image source."),k=p.l10n.t("Embedded SVGs are not a valid image source."),O=p.l10n.t("Data URLs are not a valid image source."),S=p.l10n.t("Relative image URLs require a repository with HTTPS protocol to be specified in the package.json."),L=p.l10n.t("Relative badge URLs require a repository with HTTPS protocol to be specified in this package.json."),v=p.l10n.t("This proposal cannot be used because for this extension the product defines a fixed set of API proposals. You can test your extension but before publishing you MUST reach out to the VS Code team."),b=p.l10n.t("This activation event can be removed for extensions targeting engine version ^1.75 as VS Code will generate these automatically from your package.json contribution declarations."),R=p.l10n.t("Using '*' activation is usually a bad idea as it impacts performance."),I=p.l10n.t("Error parsing the when-clause:");var M;function y(e,t,n=!0){try{const n=new l.URL(e,t);return p.Uri.parse(n.toString())}catch(r){return n?y(encodeURI(e),t,!1):null}}!function(e){e[e.ICON=0]="ICON",e[e.BADGE=1]="BADGE",e[e.MARKDOWN=2]="MARKDOWN"}(M||(M={})),t.ExtensionLinter=class{constructor(){this.diagnosticsCollection=p.languages.createDiagnosticCollection("extension-editing"),this.fileWatcher=p.workspace.createFileSystemWatcher("**/package.json"),this.disposables=[this.diagnosticsCollection,this.fileWatcher],this.folderToPackageJsonInfo={},this.packageJsonQ=new Set,this.readmeQ=new Set,this.disposables.push(p.workspace.onDidOpenTextDocument((e=>this.queue(e))),p.workspace.onDidChangeTextDocument((e=>this.queue(e.document))),p.workspace.onDidCloseTextDocument((e=>this.clear(e))),this.fileWatcher.onDidChange((e=>this.packageJsonChanged(this.getUriFolder(e)))),this.fileWatcher.onDidCreate((e=>this.packageJsonChanged(this.getUriFolder(e)))),this.fileWatcher.onDidDelete((e=>this.packageJsonChanged(this.getUriFolder(e))))),p.workspace.textDocuments.forEach((e=>this.queue(e)))}queue(e){const t=e.uri.path;"json"===e.languageId&&t.endsWith("/package.json")&&(this.packageJsonQ.add(e),this.startTimer()),this.queueReadme(e)}queueReadme(e){const t=e.uri.path;"markdown"===e.languageId&&(t.toLowerCase().endsWith("/readme.md")||t.toLowerCase().endsWith("/changelog.md"))&&(this.readmeQ.add(e),this.startTimer())}startTimer(){this.timer&&clearTimeout(this.timer),this.timer=setTimeout((()=>{this.lint().catch(console.error)}),300)}async lint(){await Promise.all([this.lintPackageJson(),this.lintReadme()])}async lintPackageJson(){for(const e of Array.from(this.packageJsonQ)){if(this.packageJsonQ.delete(e),e.isClosed)continue;const t=[],n=(0,u.parseTree)(e.getText()),r=this.readPackageJsonInfo(this.getUriFolder(e.uri),n);if(n&&r.isExtension){const s=(0,u.findNodeAtLocation)(n,["icon"]);s&&"string"===s.type&&this.addDiagnostics(t,e,s.offset+1,s.offset+s.length-1,s.value,M.ICON,r);const i=(0,u.findNodeAtLocation)(n,["badges"]);i&&"array"===i.type&&i.children&&i.children.map((e=>(0,u.findNodeAtLocation)(e,["url"]))).filter((e=>e&&"string"===e.type)).map((n=>this.addDiagnostics(t,e,n.offset+1,n.offset+n.length-1,n.value,M.BADGE,r)));const o=(0,u.findNodeAtLocation)(n,["publisher"]),a=(0,u.findNodeAtLocation)(n,["name"]),c=(0,u.findNodeAtLocation)(n,["enabledApiProposals"]);if("string"===o?.type&&"string"===a?.type&&"array"===c?.type){const n=`${(0,u.getNodeValue)(o)}.${(0,u.getNodeValue)(a)}`,r=_[n];if(Array.isArray(r)&&c.children)for(const n of c.children){const s="string"===n.type?(0,u.getNodeValue)(n):void 0;if("string"==typeof s&&!r.includes(s.split("@")[0])){const r=e.positionAt(n.offset),s=e.positionAt(n.offset+n.length);t.push(new p.Diagnostic(new p.Range(r,s),v,p.DiagnosticSeverity.Error))}}}const l=(0,u.findNodeAtLocation)(n,["activationEvents"]);if("array"===l?.type&&l.children)for(const n of l.children){const s=(0,u.getNodeValue)(n),i=r.engineVersion&&r.engineVersion?.majorBase>=1&&r.engineVersion?.minorBase>=75;if(r.implicitActivationEvents?.has(s)&&A.some((e=>s.startsWith(e)))){const r=e.positionAt(n.offset),s=e.positionAt(n.offset+n.length),o=i?T.redundantImplicitActivationEvent:b;t.push(new p.Diagnostic(new p.Range(r,s),o,i?p.DiagnosticSeverity.Warning:p.DiagnosticSeverity.Information))}for(const r of g)if(i&&s.startsWith(r)){const r=e.positionAt(n.offset),s=e.positionAt(n.offset+n.length);t.push(new p.Diagnostic(new p.Range(r,s),T.implicitActivationEvent,p.DiagnosticSeverity.Error))}if("*"===s){const r=e.positionAt(n.offset),s=e.positionAt(n.offset+n.length),i=new p.Diagnostic(new p.Range(r,s),R,p.DiagnosticSeverity.Information);i.code={value:"star-activation",target:p.Uri.parse("https://code.visualstudio.com/api/references/activation-events#Start-up")},t.push(i)}}const h=await this.lintWhenClauses((0,u.findNodeAtLocation)(n,["contributes"]),e);t.push(...h)}this.diagnosticsCollection.set(e.uri,t)}}async lintWhenClauses(e,t){if(!e)return[];const n=[];function r(e,t){if(e)switch(e.type){case"property":if(e.children&&2===e.children.length){const s=e.children[0],i=e.children[1];switch(i.type){case"string":s.value===t&&"string"==typeof i.value&&n.push(i);case"object":case"array":r(i,t)}}break;case"object":case"array":e.children&&e.children.forEach((e=>r(e,t)))}}[(0,u.findNodeAtLocation)(e,["menus"]),(0,u.findNodeAtLocation)(e,["views"]),(0,u.findNodeAtLocation)(e,["viewsWelcome"]),(0,u.findNodeAtLocation)(e,["keybindings"])].forEach((e=>r(e,"when"))),r((0,u.findNodeAtLocation)(e,["commands"]),"enablement");const s=await p.commands.executeCommand("_validateWhenClauses",n.map((e=>e.value))),i=[];for(let e=0;e<s.length;++e){const r=n[e],o=new f.JsonStringScanner(t.getText(),r.offset+1);for(const n of s[e]){const e=o.getOffsetInEncoded(n.offset),r=o.getOffsetInEncoded(n.offset+n.length),s=t.positionAt(e),a=t.positionAt(r),c=`${I}\n\n${n.errorMessage}`,l=new p.Diagnostic(new p.Range(s,a),c,p.DiagnosticSeverity.Error);l.code={value:"See docs",target:p.Uri.parse("https://code.visualstudio.com/api/references/when-clause-contexts")},i.push(l)}}return i}async lintReadme(){for(const e of this.readmeQ){if(this.readmeQ.delete(e),e.isClosed)continue;const t=this.getUriFolder(e.uri);let r=this.folderToPackageJsonInfo[t.toString()];if(!r){const e=await this.loadPackageJson(t);r=this.readPackageJsonInfo(t,e)}if(!r.isExtension)return void this.diagnosticsCollection.set(e.uri,[]);const s=e.getText();this.markdownIt||(this.markdownIt=new((await Promise.resolve().then((()=>o(n(2922))))).default));const i=this.markdownIt.parse(s,{}),a=function t(n,r=0,i=s.length){const o=n.map((t=>t.map?{token:t,begin:e.offsetAt(new p.Position(t.map[0],0)),end:r=e.offsetAt(new p.Position(t.map[1],0))}:"image"===t.type&&this.locateToken(s,r,i,t,t.attrGet("src"))||this.locateToken(s,r,i,t,t.content)||{token:t,begin:r,end:r}));return o.concat(...o.filter((e=>e.token.children&&e.token.children.length)).map((e=>t.call(this,e.token.children,e.begin,e.end))))}.call(this,i),c=[];let l;a.filter((e=>"image"===e.token.type&&e.token.attrGet("src"))).map((t=>{const n=t.token.attrGet("src"),i=s.indexOf(n,t.begin);if(-1!==i&&i<t.end)this.addDiagnostics(c,e,i,i+n.length,n,M.MARKDOWN,r);else{const i=t.token.content,o=s.indexOf(i,t.begin);-1!==o&&o<t.end&&this.addDiagnostics(c,e,o,o+i.length,n,M.MARKDOWN,r)}}));for(const t of a)if("text"===t.token.type&&t.token.content){this.parse5||(this.parse5=await Promise.resolve().then((()=>o(n(63)))));const i=new this.parse5.SAXParser({locationInfo:!0});i.on("startTag",((n,i,o,a)=>{if("img"===n){const n=i.find((e=>"src"===e.name));if(n&&n.value&&a){const i=s.indexOf(n.value,t.begin+a.startOffset);-1!==i&&i<t.end&&this.addDiagnostics(c,e,i,i+n.value.length,n.value,M.MARKDOWN,r)}}else if("svg"===n&&a){const n=t.begin+a.startOffset,r=t.begin+a.endOffset,s=new p.Range(e.positionAt(n),e.positionAt(r));l=new p.Diagnostic(s,k,p.DiagnosticSeverity.Warning),c.push(l)}})),i.on("endTag",((n,r)=>{if("svg"===n&&l&&r){const n=t.begin+r.endOffset;l.range=new p.Range(l.range.start,e.positionAt(n))}})),i.write(t.token.content),i.end()}this.diagnosticsCollection.set(e.uri,c)}}locateToken(e,t,n,r,s){if(s){const i=e.indexOf(s,t);if(-1!==i){const e=i+s.length;if(e<=n)return t=e,{token:r,begin:i,end:e}}}}readPackageJsonInfo(e,t){const n=t&&(0,u.findNodeAtLocation)(t,["engines","vscode"]),r="string"===n?.type?(0,h.normalizeVersion)((0,h.parseVersion)(n.value)):null,s=t&&(0,u.findNodeAtLocation)(t,["repository","url"]),i=s&&y(s.value),o=t&&function(e){const t=new Set,n=(0,u.findNodeAtLocation)(e,["contributes","commands"]);n?.children?.forEach((e=>{const n=(0,u.findNodeAtLocation)(e,["command"]);n&&"string"===n.type&&t.add(`onCommand:${n.value}`)}));const r=(0,u.findNodeAtLocation)(e,["contributes","authentication"]);r?.children?.forEach((e=>{const n=(0,u.findNodeAtLocation)(e,["id"]);n&&"string"===n.type&&t.add(`onAuthenticationRequest:${n.value}`)}));const s=(0,u.findNodeAtLocation)(e,["contributes","languages"]);s?.children?.forEach((e=>{const n=(0,u.findNodeAtLocation)(e,["id"]),r=(0,u.findNodeAtLocation)(e,["configuration"]);n&&"string"===n.type&&r&&"string"===r.type&&t.add(`onLanguage:${n.value}`)}));const i=(0,u.findNodeAtLocation)(e,["contributes","customEditors"]);i?.children?.forEach((e=>{const n=(0,u.findNodeAtLocation)(e,["viewType"]);n&&"string"===n.type&&t.add(`onCustomEditor:${n.value}`)}));const o=(0,u.findNodeAtLocation)(e,["contributes","views"]);o?.children?.forEach((e=>{const n=e.children?.find((e=>"array"===e.type));n?.children?.forEach((e=>{const n=(0,u.findNodeAtLocation)(e,["id"]);n&&"string"===n.type&&t.add(`onView:${n.value}`)}))}));const a=(0,u.findNodeAtLocation)(e,["contributes","walkthroughs"]);a?.children?.forEach((e=>{const n=(0,u.findNodeAtLocation)(e,["id"]);n&&"string"===n.type&&t.add(`onWalkthrough:${n.value}`)}));const c=(0,u.findNodeAtLocation)(e,["contributes","notebookRenderer"]);c?.children?.forEach((e=>{const n=(0,u.findNodeAtLocation)(e,["id"]);n&&"string"===n.type&&t.add(`onRenderer:${n.value}`)}));const l=(0,u.findNodeAtLocation)(e,["contributes","terminal","profiles"]);l?.children?.forEach((e=>{const n=(0,u.findNodeAtLocation)(e,["id"]);n&&"string"===n.type&&t.add(`onTerminalProfile:${n.value}`)}));const p=(0,u.findNodeAtLocation)(e,["contributes","terminal","quickFixes"]);p?.children?.forEach((e=>{const n=(0,u.findNodeAtLocation)(e,["id"]);n&&"string"===n.type&&t.add(`onTerminalQuickFixRequest:${n.value}`)}));const h=(0,u.findNodeAtLocation)(e,["contributes","taskDefinitions"]);return h?.children?.forEach((e=>{const n=(0,u.findNodeAtLocation)(e,["type"]);n&&"string"===n.type&&t.add(`onTaskType:${n.value}`)})),t}(t),a={isExtension:!(!n||"string"!==n.type),hasHttpsRepository:!!(s&&"string"===s.type&&s.value&&i&&"https"===i.scheme.toLowerCase()),repository:i,implicitActivationEvents:o,engineVersion:r},c=e.toString(),l=this.folderToPackageJsonInfo[c];return!l||l.isExtension===a.isExtension&&l.hasHttpsRepository===a.hasHttpsRepository||this.packageJsonChanged(e),this.folderToPackageJsonInfo[c]=a,a}async loadPackageJson(e){if("git"===e.scheme)return;const t=e.with({path:a.posix.join(e.path,"package.json")});try{const e=await p.workspace.fs.readFile(t);return(0,u.parseTree)(Buffer.from(e).toString("utf-8"))}catch(e){return}}packageJsonChanged(e){delete this.folderToPackageJsonInfo[e.toString()];const t=e.toString().toLowerCase();p.workspace.textDocuments.filter((e=>this.getUriFolder(e.uri).toString().toLowerCase()===t)).forEach((e=>this.queueReadme(e)))}getUriFolder(e){return e.with({path:a.posix.dirname(e.path)})}addDiagnostics(e,t,n,r,s,i,o){const a=/^\w[\w\d+.-]*:/.test(s),c=y(s,o.repository?o.repository.toString():t.uri.toString());if(!c)return;const l=c.scheme.toLowerCase();if(a&&"https"!==l&&"data"!==l){const s=new p.Range(t.positionAt(n),t.positionAt(r));e.push(new p.Diagnostic(s,C,p.DiagnosticSeverity.Warning))}if(a&&"data"===l){const s=new p.Range(t.positionAt(n),t.positionAt(r));e.push(new p.Diagnostic(s,O,p.DiagnosticSeverity.Warning))}if(!a&&!o.hasHttpsRepository&&i!==M.ICON){const s=new p.Range(t.positionAt(n),t.positionAt(r)),o=i===M.BADGE?L:S;e.push(new p.Diagnostic(s,o,p.DiagnosticSeverity.Warning))}if(c.path.toLowerCase().endsWith(".svg")&&!function(e){return m.includes(e.authority.toLowerCase())||E.some((t=>t.test(e.toString())))}(c)){const s=new p.Range(t.positionAt(n),t.positionAt(r));e.push(new p.Diagnostic(s,N,p.DiagnosticSeverity.Warning))}}clear(e){this.diagnosticsCollection.delete(e.uri),this.packageJsonQ.delete(e)}dispose(){this.disposables.forEach((e=>e.dispose())),this.disposables=[]}}},9437:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.JsonStringScanner=void 0,t.JsonStringScanner=class{constructor(e,t){this.text=e,this.resultChars=0,this.pos=0,this.pos=t}getOffsetInEncoded(e){let t=this.pos;for(;;){if(this.resultChars>e)return t;if(92!==this.text.charCodeAt(this.pos))t=this.pos,this.pos++,this.resultChars++;else switch(t=this.pos,this.pos++,this.text.charCodeAt(this.pos++)){case 34:case 92:case 47:case 98:case 102:case 110:case 114:case 116:this.resultChars+=1;break;case 117:{const e=this.scanHexDigits(4,!0);e>=0&&(this.resultChars+=String.fromCharCode(e).length);break}}}}scanHexDigits(e,t){let n=0,r=0;for(;n<e||!t;){const e=this.text.charCodeAt(this.pos);if(e>=48&&e<=57)r=16*r+e-48;else if(e>=65&&e<=70)r=16*r+e-65+10;else{if(!(e>=97&&e<=102))break;r=16*r+e-97+10}this.pos++,n++}return n<e&&(r=-1),r}}},7972:function(e,t,n){"use strict";var r,s=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var s=Object.getOwnPropertyDescriptor(t,n);s&&!("get"in s?!t.__esModule:s.writable||s.configurable)||(s={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,s)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),i=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),o=this&&this.__importStar||(r=function(e){return r=Object.getOwnPropertyNames||function(e){var t=[];for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[t.length]=n);return t},r(e)},function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n=r(e),o=0;o<n.length;o++)"default"!==n[o]&&s(t,e,n[o]);return i(t,e),t});Object.defineProperty(t,"__esModule",{value:!0}),t.PackageDocument=void 0;const a=o(n(1398)),c=n(5887),l=n(4712);t.PackageDocument=class{constructor(e){this.document=e}provideCompletionItems(e,t){const n=(0,c.getLocation)(this.document.getText(),this.document.offsetAt(e));if(n.path.length>=2&&"configurationDefaults"===n.path[1])return this.provideLanguageOverridesCompletionItems(n,e)}provideCodeActions(e,t,n){const r=[];for(const e of t.diagnostics)if(e.message===l.implicitActivationEvent||e.message===l.redundantImplicitActivationEvent){const t=new a.CodeAction(a.l10n.t("Remove activation event"),a.CodeActionKind.QuickFix);t.edit=new a.WorkspaceEdit;const n=e.range.with(e.range.end,e.range.end.translate(0,1));","===this.document.getText(n)?t.edit.delete(this.document.uri,e.range.with(void 0,e.range.end.translate(0,1))):t.edit.delete(this.document.uri,e.range),r.push(t)}return r}provideLanguageOverridesCompletionItems(e,t){let n=this.getReplaceRange(e,t);const r=this.document.getText(n);if(2===e.path.length){let e='"[${1:language}]": {\n\t"$0"\n}';return r&&r.startsWith('"')&&(n=new a.Range(new a.Position(n.start.line,n.start.character+1),n.end),e=e.substring(1)),Promise.resolve([this.newSnippetCompletionItem({label:a.l10n.t("Language specific editor settings"),documentation:a.l10n.t("Override editor settings for language"),snippet:e,range:n})])}return 3===e.path.length&&e.previousNode&&"string"==typeof e.previousNode.value&&e.previousNode.value.startsWith("[")?(n=new a.Range(new a.Position(n.start.line,n.start.character+2),n.end),a.languages.getLanguages().then((e=>e.map((e=>this.newSimpleCompletionItem(e,n,"",e+']"')))))):Promise.resolve([])}getReplaceRange(e,t){const n=e.previousNode;if(n){const e=this.document.positionAt(n.offset),r=this.document.positionAt(n.offset+n.length);if(e.isBeforeOrEqual(t)&&r.isAfterOrEqual(t))return new a.Range(e,r)}return new a.Range(t,t)}newSimpleCompletionItem(e,t,n,r){const s=new a.CompletionItem(e);return s.kind=a.CompletionItemKind.Value,s.detail=n,s.insertText=r||e,s.range=t,s}newSnippetCompletionItem(e){const t=new a.CompletionItem(e.label);return t.kind=a.CompletionItemKind.Value,t.documentation=e.documentation,t.insertText=new a.SnippetString(e.snippet),t.range=e.range,t}}},1398:e=>{"use strict";e.exports=require("vscode")},9896:e=>{"use strict";e.exports=require("fs")},6928:e=>{"use strict";e.exports=require("path")},4876:e=>{"use strict";e.exports=require("punycode")},2203:e=>{"use strict";e.exports=require("stream")},7016:e=>{"use strict";e.exports=require("url")},9023:e=>{"use strict";e.exports=require("util")},4374:e=>{"use strict";e.exports=JSON.parse('{"Aacute":"Á","aacute":"á","Abreve":"Ă","abreve":"ă","ac":"∾","acd":"∿","acE":"∾̳","Acirc":"Â","acirc":"â","acute":"´","Acy":"А","acy":"а","AElig":"Æ","aelig":"æ","af":"⁡","Afr":"𝔄","afr":"𝔞","Agrave":"À","agrave":"à","alefsym":"ℵ","aleph":"ℵ","Alpha":"Α","alpha":"α","Amacr":"Ā","amacr":"ā","amalg":"⨿","amp":"&","AMP":"&","andand":"⩕","And":"⩓","and":"∧","andd":"⩜","andslope":"⩘","andv":"⩚","ang":"∠","ange":"⦤","angle":"∠","angmsdaa":"⦨","angmsdab":"⦩","angmsdac":"⦪","angmsdad":"⦫","angmsdae":"⦬","angmsdaf":"⦭","angmsdag":"⦮","angmsdah":"⦯","angmsd":"∡","angrt":"∟","angrtvb":"⊾","angrtvbd":"⦝","angsph":"∢","angst":"Å","angzarr":"⍼","Aogon":"Ą","aogon":"ą","Aopf":"𝔸","aopf":"𝕒","apacir":"⩯","ap":"≈","apE":"⩰","ape":"≊","apid":"≋","apos":"\'","ApplyFunction":"⁡","approx":"≈","approxeq":"≊","Aring":"Å","aring":"å","Ascr":"𝒜","ascr":"𝒶","Assign":"≔","ast":"*","asymp":"≈","asympeq":"≍","Atilde":"Ã","atilde":"ã","Auml":"Ä","auml":"ä","awconint":"∳","awint":"⨑","backcong":"≌","backepsilon":"϶","backprime":"‵","backsim":"∽","backsimeq":"⋍","Backslash":"∖","Barv":"⫧","barvee":"⊽","barwed":"⌅","Barwed":"⌆","barwedge":"⌅","bbrk":"⎵","bbrktbrk":"⎶","bcong":"≌","Bcy":"Б","bcy":"б","bdquo":"„","becaus":"∵","because":"∵","Because":"∵","bemptyv":"⦰","bepsi":"϶","bernou":"ℬ","Bernoullis":"ℬ","Beta":"Β","beta":"β","beth":"ℶ","between":"≬","Bfr":"𝔅","bfr":"𝔟","bigcap":"⋂","bigcirc":"◯","bigcup":"⋃","bigodot":"⨀","bigoplus":"⨁","bigotimes":"⨂","bigsqcup":"⨆","bigstar":"★","bigtriangledown":"▽","bigtriangleup":"△","biguplus":"⨄","bigvee":"⋁","bigwedge":"⋀","bkarow":"⤍","blacklozenge":"⧫","blacksquare":"▪","blacktriangle":"▴","blacktriangledown":"▾","blacktriangleleft":"◂","blacktriangleright":"▸","blank":"␣","blk12":"▒","blk14":"░","blk34":"▓","block":"█","bne":"=⃥","bnequiv":"≡⃥","bNot":"⫭","bnot":"⌐","Bopf":"𝔹","bopf":"𝕓","bot":"⊥","bottom":"⊥","bowtie":"⋈","boxbox":"⧉","boxdl":"┐","boxdL":"╕","boxDl":"╖","boxDL":"╗","boxdr":"┌","boxdR":"╒","boxDr":"╓","boxDR":"╔","boxh":"─","boxH":"═","boxhd":"┬","boxHd":"╤","boxhD":"╥","boxHD":"╦","boxhu":"┴","boxHu":"╧","boxhU":"╨","boxHU":"╩","boxminus":"⊟","boxplus":"⊞","boxtimes":"⊠","boxul":"┘","boxuL":"╛","boxUl":"╜","boxUL":"╝","boxur":"└","boxuR":"╘","boxUr":"╙","boxUR":"╚","boxv":"│","boxV":"║","boxvh":"┼","boxvH":"╪","boxVh":"╫","boxVH":"╬","boxvl":"┤","boxvL":"╡","boxVl":"╢","boxVL":"╣","boxvr":"├","boxvR":"╞","boxVr":"╟","boxVR":"╠","bprime":"‵","breve":"˘","Breve":"˘","brvbar":"¦","bscr":"𝒷","Bscr":"ℬ","bsemi":"⁏","bsim":"∽","bsime":"⋍","bsolb":"⧅","bsol":"\\\\","bsolhsub":"⟈","bull":"•","bullet":"•","bump":"≎","bumpE":"⪮","bumpe":"≏","Bumpeq":"≎","bumpeq":"≏","Cacute":"Ć","cacute":"ć","capand":"⩄","capbrcup":"⩉","capcap":"⩋","cap":"∩","Cap":"⋒","capcup":"⩇","capdot":"⩀","CapitalDifferentialD":"ⅅ","caps":"∩︀","caret":"⁁","caron":"ˇ","Cayleys":"ℭ","ccaps":"⩍","Ccaron":"Č","ccaron":"č","Ccedil":"Ç","ccedil":"ç","Ccirc":"Ĉ","ccirc":"ĉ","Cconint":"∰","ccups":"⩌","ccupssm":"⩐","Cdot":"Ċ","cdot":"ċ","cedil":"¸","Cedilla":"¸","cemptyv":"⦲","cent":"¢","centerdot":"·","CenterDot":"·","cfr":"𝔠","Cfr":"ℭ","CHcy":"Ч","chcy":"ч","check":"✓","checkmark":"✓","Chi":"Χ","chi":"χ","circ":"ˆ","circeq":"≗","circlearrowleft":"↺","circlearrowright":"↻","circledast":"⊛","circledcirc":"⊚","circleddash":"⊝","CircleDot":"⊙","circledR":"®","circledS":"Ⓢ","CircleMinus":"⊖","CirclePlus":"⊕","CircleTimes":"⊗","cir":"○","cirE":"⧃","cire":"≗","cirfnint":"⨐","cirmid":"⫯","cirscir":"⧂","ClockwiseContourIntegral":"∲","CloseCurlyDoubleQuote":"”","CloseCurlyQuote":"’","clubs":"♣","clubsuit":"♣","colon":":","Colon":"∷","Colone":"⩴","colone":"≔","coloneq":"≔","comma":",","commat":"@","comp":"∁","compfn":"∘","complement":"∁","complexes":"ℂ","cong":"≅","congdot":"⩭","Congruent":"≡","conint":"∮","Conint":"∯","ContourIntegral":"∮","copf":"𝕔","Copf":"ℂ","coprod":"∐","Coproduct":"∐","copy":"©","COPY":"©","copysr":"℗","CounterClockwiseContourIntegral":"∳","crarr":"↵","cross":"✗","Cross":"⨯","Cscr":"𝒞","cscr":"𝒸","csub":"⫏","csube":"⫑","csup":"⫐","csupe":"⫒","ctdot":"⋯","cudarrl":"⤸","cudarrr":"⤵","cuepr":"⋞","cuesc":"⋟","cularr":"↶","cularrp":"⤽","cupbrcap":"⩈","cupcap":"⩆","CupCap":"≍","cup":"∪","Cup":"⋓","cupcup":"⩊","cupdot":"⊍","cupor":"⩅","cups":"∪︀","curarr":"↷","curarrm":"⤼","curlyeqprec":"⋞","curlyeqsucc":"⋟","curlyvee":"⋎","curlywedge":"⋏","curren":"¤","curvearrowleft":"↶","curvearrowright":"↷","cuvee":"⋎","cuwed":"⋏","cwconint":"∲","cwint":"∱","cylcty":"⌭","dagger":"†","Dagger":"‡","daleth":"ℸ","darr":"↓","Darr":"↡","dArr":"⇓","dash":"‐","Dashv":"⫤","dashv":"⊣","dbkarow":"⤏","dblac":"˝","Dcaron":"Ď","dcaron":"ď","Dcy":"Д","dcy":"д","ddagger":"‡","ddarr":"⇊","DD":"ⅅ","dd":"ⅆ","DDotrahd":"⤑","ddotseq":"⩷","deg":"°","Del":"∇","Delta":"Δ","delta":"δ","demptyv":"⦱","dfisht":"⥿","Dfr":"𝔇","dfr":"𝔡","dHar":"⥥","dharl":"⇃","dharr":"⇂","DiacriticalAcute":"´","DiacriticalDot":"˙","DiacriticalDoubleAcute":"˝","DiacriticalGrave":"`","DiacriticalTilde":"˜","diam":"⋄","diamond":"⋄","Diamond":"⋄","diamondsuit":"♦","diams":"♦","die":"¨","DifferentialD":"ⅆ","digamma":"ϝ","disin":"⋲","div":"÷","divide":"÷","divideontimes":"⋇","divonx":"⋇","DJcy":"Ђ","djcy":"ђ","dlcorn":"⌞","dlcrop":"⌍","dollar":"$","Dopf":"𝔻","dopf":"𝕕","Dot":"¨","dot":"˙","DotDot":"⃜","doteq":"≐","doteqdot":"≑","DotEqual":"≐","dotminus":"∸","dotplus":"∔","dotsquare":"⊡","doublebarwedge":"⌆","DoubleContourIntegral":"∯","DoubleDot":"¨","DoubleDownArrow":"⇓","DoubleLeftArrow":"⇐","DoubleLeftRightArrow":"⇔","DoubleLeftTee":"⫤","DoubleLongLeftArrow":"⟸","DoubleLongLeftRightArrow":"⟺","DoubleLongRightArrow":"⟹","DoubleRightArrow":"⇒","DoubleRightTee":"⊨","DoubleUpArrow":"⇑","DoubleUpDownArrow":"⇕","DoubleVerticalBar":"∥","DownArrowBar":"⤓","downarrow":"↓","DownArrow":"↓","Downarrow":"⇓","DownArrowUpArrow":"⇵","DownBreve":"̑","downdownarrows":"⇊","downharpoonleft":"⇃","downharpoonright":"⇂","DownLeftRightVector":"⥐","DownLeftTeeVector":"⥞","DownLeftVectorBar":"⥖","DownLeftVector":"↽","DownRightTeeVector":"⥟","DownRightVectorBar":"⥗","DownRightVector":"⇁","DownTeeArrow":"↧","DownTee":"⊤","drbkarow":"⤐","drcorn":"⌟","drcrop":"⌌","Dscr":"𝒟","dscr":"𝒹","DScy":"Ѕ","dscy":"ѕ","dsol":"⧶","Dstrok":"Đ","dstrok":"đ","dtdot":"⋱","dtri":"▿","dtrif":"▾","duarr":"⇵","duhar":"⥯","dwangle":"⦦","DZcy":"Џ","dzcy":"џ","dzigrarr":"⟿","Eacute":"É","eacute":"é","easter":"⩮","Ecaron":"Ě","ecaron":"ě","Ecirc":"Ê","ecirc":"ê","ecir":"≖","ecolon":"≕","Ecy":"Э","ecy":"э","eDDot":"⩷","Edot":"Ė","edot":"ė","eDot":"≑","ee":"ⅇ","efDot":"≒","Efr":"𝔈","efr":"𝔢","eg":"⪚","Egrave":"È","egrave":"è","egs":"⪖","egsdot":"⪘","el":"⪙","Element":"∈","elinters":"⏧","ell":"ℓ","els":"⪕","elsdot":"⪗","Emacr":"Ē","emacr":"ē","empty":"∅","emptyset":"∅","EmptySmallSquare":"◻","emptyv":"∅","EmptyVerySmallSquare":"▫","emsp13":" ","emsp14":" ","emsp":" ","ENG":"Ŋ","eng":"ŋ","ensp":" ","Eogon":"Ę","eogon":"ę","Eopf":"𝔼","eopf":"𝕖","epar":"⋕","eparsl":"⧣","eplus":"⩱","epsi":"ε","Epsilon":"Ε","epsilon":"ε","epsiv":"ϵ","eqcirc":"≖","eqcolon":"≕","eqsim":"≂","eqslantgtr":"⪖","eqslantless":"⪕","Equal":"⩵","equals":"=","EqualTilde":"≂","equest":"≟","Equilibrium":"⇌","equiv":"≡","equivDD":"⩸","eqvparsl":"⧥","erarr":"⥱","erDot":"≓","escr":"ℯ","Escr":"ℰ","esdot":"≐","Esim":"⩳","esim":"≂","Eta":"Η","eta":"η","ETH":"Ð","eth":"ð","Euml":"Ë","euml":"ë","euro":"€","excl":"!","exist":"∃","Exists":"∃","expectation":"ℰ","exponentiale":"ⅇ","ExponentialE":"ⅇ","fallingdotseq":"≒","Fcy":"Ф","fcy":"ф","female":"♀","ffilig":"ﬃ","fflig":"ﬀ","ffllig":"ﬄ","Ffr":"𝔉","ffr":"𝔣","filig":"ﬁ","FilledSmallSquare":"◼","FilledVerySmallSquare":"▪","fjlig":"fj","flat":"♭","fllig":"ﬂ","fltns":"▱","fnof":"ƒ","Fopf":"𝔽","fopf":"𝕗","forall":"∀","ForAll":"∀","fork":"⋔","forkv":"⫙","Fouriertrf":"ℱ","fpartint":"⨍","frac12":"½","frac13":"⅓","frac14":"¼","frac15":"⅕","frac16":"⅙","frac18":"⅛","frac23":"⅔","frac25":"⅖","frac34":"¾","frac35":"⅗","frac38":"⅜","frac45":"⅘","frac56":"⅚","frac58":"⅝","frac78":"⅞","frasl":"⁄","frown":"⌢","fscr":"𝒻","Fscr":"ℱ","gacute":"ǵ","Gamma":"Γ","gamma":"γ","Gammad":"Ϝ","gammad":"ϝ","gap":"⪆","Gbreve":"Ğ","gbreve":"ğ","Gcedil":"Ģ","Gcirc":"Ĝ","gcirc":"ĝ","Gcy":"Г","gcy":"г","Gdot":"Ġ","gdot":"ġ","ge":"≥","gE":"≧","gEl":"⪌","gel":"⋛","geq":"≥","geqq":"≧","geqslant":"⩾","gescc":"⪩","ges":"⩾","gesdot":"⪀","gesdoto":"⪂","gesdotol":"⪄","gesl":"⋛︀","gesles":"⪔","Gfr":"𝔊","gfr":"𝔤","gg":"≫","Gg":"⋙","ggg":"⋙","gimel":"ℷ","GJcy":"Ѓ","gjcy":"ѓ","gla":"⪥","gl":"≷","glE":"⪒","glj":"⪤","gnap":"⪊","gnapprox":"⪊","gne":"⪈","gnE":"≩","gneq":"⪈","gneqq":"≩","gnsim":"⋧","Gopf":"𝔾","gopf":"𝕘","grave":"`","GreaterEqual":"≥","GreaterEqualLess":"⋛","GreaterFullEqual":"≧","GreaterGreater":"⪢","GreaterLess":"≷","GreaterSlantEqual":"⩾","GreaterTilde":"≳","Gscr":"𝒢","gscr":"ℊ","gsim":"≳","gsime":"⪎","gsiml":"⪐","gtcc":"⪧","gtcir":"⩺","gt":">","GT":">","Gt":"≫","gtdot":"⋗","gtlPar":"⦕","gtquest":"⩼","gtrapprox":"⪆","gtrarr":"⥸","gtrdot":"⋗","gtreqless":"⋛","gtreqqless":"⪌","gtrless":"≷","gtrsim":"≳","gvertneqq":"≩︀","gvnE":"≩︀","Hacek":"ˇ","hairsp":" ","half":"½","hamilt":"ℋ","HARDcy":"Ъ","hardcy":"ъ","harrcir":"⥈","harr":"↔","hArr":"⇔","harrw":"↭","Hat":"^","hbar":"ℏ","Hcirc":"Ĥ","hcirc":"ĥ","hearts":"♥","heartsuit":"♥","hellip":"…","hercon":"⊹","hfr":"𝔥","Hfr":"ℌ","HilbertSpace":"ℋ","hksearow":"⤥","hkswarow":"⤦","hoarr":"⇿","homtht":"∻","hookleftarrow":"↩","hookrightarrow":"↪","hopf":"𝕙","Hopf":"ℍ","horbar":"―","HorizontalLine":"─","hscr":"𝒽","Hscr":"ℋ","hslash":"ℏ","Hstrok":"Ħ","hstrok":"ħ","HumpDownHump":"≎","HumpEqual":"≏","hybull":"⁃","hyphen":"‐","Iacute":"Í","iacute":"í","ic":"⁣","Icirc":"Î","icirc":"î","Icy":"И","icy":"и","Idot":"İ","IEcy":"Е","iecy":"е","iexcl":"¡","iff":"⇔","ifr":"𝔦","Ifr":"ℑ","Igrave":"Ì","igrave":"ì","ii":"ⅈ","iiiint":"⨌","iiint":"∭","iinfin":"⧜","iiota":"℩","IJlig":"Ĳ","ijlig":"ĳ","Imacr":"Ī","imacr":"ī","image":"ℑ","ImaginaryI":"ⅈ","imagline":"ℐ","imagpart":"ℑ","imath":"ı","Im":"ℑ","imof":"⊷","imped":"Ƶ","Implies":"⇒","incare":"℅","in":"∈","infin":"∞","infintie":"⧝","inodot":"ı","intcal":"⊺","int":"∫","Int":"∬","integers":"ℤ","Integral":"∫","intercal":"⊺","Intersection":"⋂","intlarhk":"⨗","intprod":"⨼","InvisibleComma":"⁣","InvisibleTimes":"⁢","IOcy":"Ё","iocy":"ё","Iogon":"Į","iogon":"į","Iopf":"𝕀","iopf":"𝕚","Iota":"Ι","iota":"ι","iprod":"⨼","iquest":"¿","iscr":"𝒾","Iscr":"ℐ","isin":"∈","isindot":"⋵","isinE":"⋹","isins":"⋴","isinsv":"⋳","isinv":"∈","it":"⁢","Itilde":"Ĩ","itilde":"ĩ","Iukcy":"І","iukcy":"і","Iuml":"Ï","iuml":"ï","Jcirc":"Ĵ","jcirc":"ĵ","Jcy":"Й","jcy":"й","Jfr":"𝔍","jfr":"𝔧","jmath":"ȷ","Jopf":"𝕁","jopf":"𝕛","Jscr":"𝒥","jscr":"𝒿","Jsercy":"Ј","jsercy":"ј","Jukcy":"Є","jukcy":"є","Kappa":"Κ","kappa":"κ","kappav":"ϰ","Kcedil":"Ķ","kcedil":"ķ","Kcy":"К","kcy":"к","Kfr":"𝔎","kfr":"𝔨","kgreen":"ĸ","KHcy":"Х","khcy":"х","KJcy":"Ќ","kjcy":"ќ","Kopf":"𝕂","kopf":"𝕜","Kscr":"𝒦","kscr":"𝓀","lAarr":"⇚","Lacute":"Ĺ","lacute":"ĺ","laemptyv":"⦴","lagran":"ℒ","Lambda":"Λ","lambda":"λ","lang":"⟨","Lang":"⟪","langd":"⦑","langle":"⟨","lap":"⪅","Laplacetrf":"ℒ","laquo":"«","larrb":"⇤","larrbfs":"⤟","larr":"←","Larr":"↞","lArr":"⇐","larrfs":"⤝","larrhk":"↩","larrlp":"↫","larrpl":"⤹","larrsim":"⥳","larrtl":"↢","latail":"⤙","lAtail":"⤛","lat":"⪫","late":"⪭","lates":"⪭︀","lbarr":"⤌","lBarr":"⤎","lbbrk":"❲","lbrace":"{","lbrack":"[","lbrke":"⦋","lbrksld":"⦏","lbrkslu":"⦍","Lcaron":"Ľ","lcaron":"ľ","Lcedil":"Ļ","lcedil":"ļ","lceil":"⌈","lcub":"{","Lcy":"Л","lcy":"л","ldca":"⤶","ldquo":"“","ldquor":"„","ldrdhar":"⥧","ldrushar":"⥋","ldsh":"↲","le":"≤","lE":"≦","LeftAngleBracket":"⟨","LeftArrowBar":"⇤","leftarrow":"←","LeftArrow":"←","Leftarrow":"⇐","LeftArrowRightArrow":"⇆","leftarrowtail":"↢","LeftCeiling":"⌈","LeftDoubleBracket":"⟦","LeftDownTeeVector":"⥡","LeftDownVectorBar":"⥙","LeftDownVector":"⇃","LeftFloor":"⌊","leftharpoondown":"↽","leftharpoonup":"↼","leftleftarrows":"⇇","leftrightarrow":"↔","LeftRightArrow":"↔","Leftrightarrow":"⇔","leftrightarrows":"⇆","leftrightharpoons":"⇋","leftrightsquigarrow":"↭","LeftRightVector":"⥎","LeftTeeArrow":"↤","LeftTee":"⊣","LeftTeeVector":"⥚","leftthreetimes":"⋋","LeftTriangleBar":"⧏","LeftTriangle":"⊲","LeftTriangleEqual":"⊴","LeftUpDownVector":"⥑","LeftUpTeeVector":"⥠","LeftUpVectorBar":"⥘","LeftUpVector":"↿","LeftVectorBar":"⥒","LeftVector":"↼","lEg":"⪋","leg":"⋚","leq":"≤","leqq":"≦","leqslant":"⩽","lescc":"⪨","les":"⩽","lesdot":"⩿","lesdoto":"⪁","lesdotor":"⪃","lesg":"⋚︀","lesges":"⪓","lessapprox":"⪅","lessdot":"⋖","lesseqgtr":"⋚","lesseqqgtr":"⪋","LessEqualGreater":"⋚","LessFullEqual":"≦","LessGreater":"≶","lessgtr":"≶","LessLess":"⪡","lesssim":"≲","LessSlantEqual":"⩽","LessTilde":"≲","lfisht":"⥼","lfloor":"⌊","Lfr":"𝔏","lfr":"𝔩","lg":"≶","lgE":"⪑","lHar":"⥢","lhard":"↽","lharu":"↼","lharul":"⥪","lhblk":"▄","LJcy":"Љ","ljcy":"љ","llarr":"⇇","ll":"≪","Ll":"⋘","llcorner":"⌞","Lleftarrow":"⇚","llhard":"⥫","lltri":"◺","Lmidot":"Ŀ","lmidot":"ŀ","lmoustache":"⎰","lmoust":"⎰","lnap":"⪉","lnapprox":"⪉","lne":"⪇","lnE":"≨","lneq":"⪇","lneqq":"≨","lnsim":"⋦","loang":"⟬","loarr":"⇽","lobrk":"⟦","longleftarrow":"⟵","LongLeftArrow":"⟵","Longleftarrow":"⟸","longleftrightarrow":"⟷","LongLeftRightArrow":"⟷","Longleftrightarrow":"⟺","longmapsto":"⟼","longrightarrow":"⟶","LongRightArrow":"⟶","Longrightarrow":"⟹","looparrowleft":"↫","looparrowright":"↬","lopar":"⦅","Lopf":"𝕃","lopf":"𝕝","loplus":"⨭","lotimes":"⨴","lowast":"∗","lowbar":"_","LowerLeftArrow":"↙","LowerRightArrow":"↘","loz":"◊","lozenge":"◊","lozf":"⧫","lpar":"(","lparlt":"⦓","lrarr":"⇆","lrcorner":"⌟","lrhar":"⇋","lrhard":"⥭","lrm":"‎","lrtri":"⊿","lsaquo":"‹","lscr":"𝓁","Lscr":"ℒ","lsh":"↰","Lsh":"↰","lsim":"≲","lsime":"⪍","lsimg":"⪏","lsqb":"[","lsquo":"‘","lsquor":"‚","Lstrok":"Ł","lstrok":"ł","ltcc":"⪦","ltcir":"⩹","lt":"<","LT":"<","Lt":"≪","ltdot":"⋖","lthree":"⋋","ltimes":"⋉","ltlarr":"⥶","ltquest":"⩻","ltri":"◃","ltrie":"⊴","ltrif":"◂","ltrPar":"⦖","lurdshar":"⥊","luruhar":"⥦","lvertneqq":"≨︀","lvnE":"≨︀","macr":"¯","male":"♂","malt":"✠","maltese":"✠","Map":"⤅","map":"↦","mapsto":"↦","mapstodown":"↧","mapstoleft":"↤","mapstoup":"↥","marker":"▮","mcomma":"⨩","Mcy":"М","mcy":"м","mdash":"—","mDDot":"∺","measuredangle":"∡","MediumSpace":" ","Mellintrf":"ℳ","Mfr":"𝔐","mfr":"𝔪","mho":"℧","micro":"µ","midast":"*","midcir":"⫰","mid":"∣","middot":"·","minusb":"⊟","minus":"−","minusd":"∸","minusdu":"⨪","MinusPlus":"∓","mlcp":"⫛","mldr":"…","mnplus":"∓","models":"⊧","Mopf":"𝕄","mopf":"𝕞","mp":"∓","mscr":"𝓂","Mscr":"ℳ","mstpos":"∾","Mu":"Μ","mu":"μ","multimap":"⊸","mumap":"⊸","nabla":"∇","Nacute":"Ń","nacute":"ń","nang":"∠⃒","nap":"≉","napE":"⩰̸","napid":"≋̸","napos":"ŉ","napprox":"≉","natural":"♮","naturals":"ℕ","natur":"♮","nbsp":" ","nbump":"≎̸","nbumpe":"≏̸","ncap":"⩃","Ncaron":"Ň","ncaron":"ň","Ncedil":"Ņ","ncedil":"ņ","ncong":"≇","ncongdot":"⩭̸","ncup":"⩂","Ncy":"Н","ncy":"н","ndash":"–","nearhk":"⤤","nearr":"↗","neArr":"⇗","nearrow":"↗","ne":"≠","nedot":"≐̸","NegativeMediumSpace":"​","NegativeThickSpace":"​","NegativeThinSpace":"​","NegativeVeryThinSpace":"​","nequiv":"≢","nesear":"⤨","nesim":"≂̸","NestedGreaterGreater":"≫","NestedLessLess":"≪","NewLine":"\\n","nexist":"∄","nexists":"∄","Nfr":"𝔑","nfr":"𝔫","ngE":"≧̸","nge":"≱","ngeq":"≱","ngeqq":"≧̸","ngeqslant":"⩾̸","nges":"⩾̸","nGg":"⋙̸","ngsim":"≵","nGt":"≫⃒","ngt":"≯","ngtr":"≯","nGtv":"≫̸","nharr":"↮","nhArr":"⇎","nhpar":"⫲","ni":"∋","nis":"⋼","nisd":"⋺","niv":"∋","NJcy":"Њ","njcy":"њ","nlarr":"↚","nlArr":"⇍","nldr":"‥","nlE":"≦̸","nle":"≰","nleftarrow":"↚","nLeftarrow":"⇍","nleftrightarrow":"↮","nLeftrightarrow":"⇎","nleq":"≰","nleqq":"≦̸","nleqslant":"⩽̸","nles":"⩽̸","nless":"≮","nLl":"⋘̸","nlsim":"≴","nLt":"≪⃒","nlt":"≮","nltri":"⋪","nltrie":"⋬","nLtv":"≪̸","nmid":"∤","NoBreak":"⁠","NonBreakingSpace":" ","nopf":"𝕟","Nopf":"ℕ","Not":"⫬","not":"¬","NotCongruent":"≢","NotCupCap":"≭","NotDoubleVerticalBar":"∦","NotElement":"∉","NotEqual":"≠","NotEqualTilde":"≂̸","NotExists":"∄","NotGreater":"≯","NotGreaterEqual":"≱","NotGreaterFullEqual":"≧̸","NotGreaterGreater":"≫̸","NotGreaterLess":"≹","NotGreaterSlantEqual":"⩾̸","NotGreaterTilde":"≵","NotHumpDownHump":"≎̸","NotHumpEqual":"≏̸","notin":"∉","notindot":"⋵̸","notinE":"⋹̸","notinva":"∉","notinvb":"⋷","notinvc":"⋶","NotLeftTriangleBar":"⧏̸","NotLeftTriangle":"⋪","NotLeftTriangleEqual":"⋬","NotLess":"≮","NotLessEqual":"≰","NotLessGreater":"≸","NotLessLess":"≪̸","NotLessSlantEqual":"⩽̸","NotLessTilde":"≴","NotNestedGreaterGreater":"⪢̸","NotNestedLessLess":"⪡̸","notni":"∌","notniva":"∌","notnivb":"⋾","notnivc":"⋽","NotPrecedes":"⊀","NotPrecedesEqual":"⪯̸","NotPrecedesSlantEqual":"⋠","NotReverseElement":"∌","NotRightTriangleBar":"⧐̸","NotRightTriangle":"⋫","NotRightTriangleEqual":"⋭","NotSquareSubset":"⊏̸","NotSquareSubsetEqual":"⋢","NotSquareSuperset":"⊐̸","NotSquareSupersetEqual":"⋣","NotSubset":"⊂⃒","NotSubsetEqual":"⊈","NotSucceeds":"⊁","NotSucceedsEqual":"⪰̸","NotSucceedsSlantEqual":"⋡","NotSucceedsTilde":"≿̸","NotSuperset":"⊃⃒","NotSupersetEqual":"⊉","NotTilde":"≁","NotTildeEqual":"≄","NotTildeFullEqual":"≇","NotTildeTilde":"≉","NotVerticalBar":"∤","nparallel":"∦","npar":"∦","nparsl":"⫽⃥","npart":"∂̸","npolint":"⨔","npr":"⊀","nprcue":"⋠","nprec":"⊀","npreceq":"⪯̸","npre":"⪯̸","nrarrc":"⤳̸","nrarr":"↛","nrArr":"⇏","nrarrw":"↝̸","nrightarrow":"↛","nRightarrow":"⇏","nrtri":"⋫","nrtrie":"⋭","nsc":"⊁","nsccue":"⋡","nsce":"⪰̸","Nscr":"𝒩","nscr":"𝓃","nshortmid":"∤","nshortparallel":"∦","nsim":"≁","nsime":"≄","nsimeq":"≄","nsmid":"∤","nspar":"∦","nsqsube":"⋢","nsqsupe":"⋣","nsub":"⊄","nsubE":"⫅̸","nsube":"⊈","nsubset":"⊂⃒","nsubseteq":"⊈","nsubseteqq":"⫅̸","nsucc":"⊁","nsucceq":"⪰̸","nsup":"⊅","nsupE":"⫆̸","nsupe":"⊉","nsupset":"⊃⃒","nsupseteq":"⊉","nsupseteqq":"⫆̸","ntgl":"≹","Ntilde":"Ñ","ntilde":"ñ","ntlg":"≸","ntriangleleft":"⋪","ntrianglelefteq":"⋬","ntriangleright":"⋫","ntrianglerighteq":"⋭","Nu":"Ν","nu":"ν","num":"#","numero":"№","numsp":" ","nvap":"≍⃒","nvdash":"⊬","nvDash":"⊭","nVdash":"⊮","nVDash":"⊯","nvge":"≥⃒","nvgt":">⃒","nvHarr":"⤄","nvinfin":"⧞","nvlArr":"⤂","nvle":"≤⃒","nvlt":"<⃒","nvltrie":"⊴⃒","nvrArr":"⤃","nvrtrie":"⊵⃒","nvsim":"∼⃒","nwarhk":"⤣","nwarr":"↖","nwArr":"⇖","nwarrow":"↖","nwnear":"⤧","Oacute":"Ó","oacute":"ó","oast":"⊛","Ocirc":"Ô","ocirc":"ô","ocir":"⊚","Ocy":"О","ocy":"о","odash":"⊝","Odblac":"Ő","odblac":"ő","odiv":"⨸","odot":"⊙","odsold":"⦼","OElig":"Œ","oelig":"œ","ofcir":"⦿","Ofr":"𝔒","ofr":"𝔬","ogon":"˛","Ograve":"Ò","ograve":"ò","ogt":"⧁","ohbar":"⦵","ohm":"Ω","oint":"∮","olarr":"↺","olcir":"⦾","olcross":"⦻","oline":"‾","olt":"⧀","Omacr":"Ō","omacr":"ō","Omega":"Ω","omega":"ω","Omicron":"Ο","omicron":"ο","omid":"⦶","ominus":"⊖","Oopf":"𝕆","oopf":"𝕠","opar":"⦷","OpenCurlyDoubleQuote":"“","OpenCurlyQuote":"‘","operp":"⦹","oplus":"⊕","orarr":"↻","Or":"⩔","or":"∨","ord":"⩝","order":"ℴ","orderof":"ℴ","ordf":"ª","ordm":"º","origof":"⊶","oror":"⩖","orslope":"⩗","orv":"⩛","oS":"Ⓢ","Oscr":"𝒪","oscr":"ℴ","Oslash":"Ø","oslash":"ø","osol":"⊘","Otilde":"Õ","otilde":"õ","otimesas":"⨶","Otimes":"⨷","otimes":"⊗","Ouml":"Ö","ouml":"ö","ovbar":"⌽","OverBar":"‾","OverBrace":"⏞","OverBracket":"⎴","OverParenthesis":"⏜","para":"¶","parallel":"∥","par":"∥","parsim":"⫳","parsl":"⫽","part":"∂","PartialD":"∂","Pcy":"П","pcy":"п","percnt":"%","period":".","permil":"‰","perp":"⊥","pertenk":"‱","Pfr":"𝔓","pfr":"𝔭","Phi":"Φ","phi":"φ","phiv":"ϕ","phmmat":"ℳ","phone":"☎","Pi":"Π","pi":"π","pitchfork":"⋔","piv":"ϖ","planck":"ℏ","planckh":"ℎ","plankv":"ℏ","plusacir":"⨣","plusb":"⊞","pluscir":"⨢","plus":"+","plusdo":"∔","plusdu":"⨥","pluse":"⩲","PlusMinus":"±","plusmn":"±","plussim":"⨦","plustwo":"⨧","pm":"±","Poincareplane":"ℌ","pointint":"⨕","popf":"𝕡","Popf":"ℙ","pound":"£","prap":"⪷","Pr":"⪻","pr":"≺","prcue":"≼","precapprox":"⪷","prec":"≺","preccurlyeq":"≼","Precedes":"≺","PrecedesEqual":"⪯","PrecedesSlantEqual":"≼","PrecedesTilde":"≾","preceq":"⪯","precnapprox":"⪹","precneqq":"⪵","precnsim":"⋨","pre":"⪯","prE":"⪳","precsim":"≾","prime":"′","Prime":"″","primes":"ℙ","prnap":"⪹","prnE":"⪵","prnsim":"⋨","prod":"∏","Product":"∏","profalar":"⌮","profline":"⌒","profsurf":"⌓","prop":"∝","Proportional":"∝","Proportion":"∷","propto":"∝","prsim":"≾","prurel":"⊰","Pscr":"𝒫","pscr":"𝓅","Psi":"Ψ","psi":"ψ","puncsp":" ","Qfr":"𝔔","qfr":"𝔮","qint":"⨌","qopf":"𝕢","Qopf":"ℚ","qprime":"⁗","Qscr":"𝒬","qscr":"𝓆","quaternions":"ℍ","quatint":"⨖","quest":"?","questeq":"≟","quot":"\\"","QUOT":"\\"","rAarr":"⇛","race":"∽̱","Racute":"Ŕ","racute":"ŕ","radic":"√","raemptyv":"⦳","rang":"⟩","Rang":"⟫","rangd":"⦒","range":"⦥","rangle":"⟩","raquo":"»","rarrap":"⥵","rarrb":"⇥","rarrbfs":"⤠","rarrc":"⤳","rarr":"→","Rarr":"↠","rArr":"⇒","rarrfs":"⤞","rarrhk":"↪","rarrlp":"↬","rarrpl":"⥅","rarrsim":"⥴","Rarrtl":"⤖","rarrtl":"↣","rarrw":"↝","ratail":"⤚","rAtail":"⤜","ratio":"∶","rationals":"ℚ","rbarr":"⤍","rBarr":"⤏","RBarr":"⤐","rbbrk":"❳","rbrace":"}","rbrack":"]","rbrke":"⦌","rbrksld":"⦎","rbrkslu":"⦐","Rcaron":"Ř","rcaron":"ř","Rcedil":"Ŗ","rcedil":"ŗ","rceil":"⌉","rcub":"}","Rcy":"Р","rcy":"р","rdca":"⤷","rdldhar":"⥩","rdquo":"”","rdquor":"”","rdsh":"↳","real":"ℜ","realine":"ℛ","realpart":"ℜ","reals":"ℝ","Re":"ℜ","rect":"▭","reg":"®","REG":"®","ReverseElement":"∋","ReverseEquilibrium":"⇋","ReverseUpEquilibrium":"⥯","rfisht":"⥽","rfloor":"⌋","rfr":"𝔯","Rfr":"ℜ","rHar":"⥤","rhard":"⇁","rharu":"⇀","rharul":"⥬","Rho":"Ρ","rho":"ρ","rhov":"ϱ","RightAngleBracket":"⟩","RightArrowBar":"⇥","rightarrow":"→","RightArrow":"→","Rightarrow":"⇒","RightArrowLeftArrow":"⇄","rightarrowtail":"↣","RightCeiling":"⌉","RightDoubleBracket":"⟧","RightDownTeeVector":"⥝","RightDownVectorBar":"⥕","RightDownVector":"⇂","RightFloor":"⌋","rightharpoondown":"⇁","rightharpoonup":"⇀","rightleftarrows":"⇄","rightleftharpoons":"⇌","rightrightarrows":"⇉","rightsquigarrow":"↝","RightTeeArrow":"↦","RightTee":"⊢","RightTeeVector":"⥛","rightthreetimes":"⋌","RightTriangleBar":"⧐","RightTriangle":"⊳","RightTriangleEqual":"⊵","RightUpDownVector":"⥏","RightUpTeeVector":"⥜","RightUpVectorBar":"⥔","RightUpVector":"↾","RightVectorBar":"⥓","RightVector":"⇀","ring":"˚","risingdotseq":"≓","rlarr":"⇄","rlhar":"⇌","rlm":"‏","rmoustache":"⎱","rmoust":"⎱","rnmid":"⫮","roang":"⟭","roarr":"⇾","robrk":"⟧","ropar":"⦆","ropf":"𝕣","Ropf":"ℝ","roplus":"⨮","rotimes":"⨵","RoundImplies":"⥰","rpar":")","rpargt":"⦔","rppolint":"⨒","rrarr":"⇉","Rrightarrow":"⇛","rsaquo":"›","rscr":"𝓇","Rscr":"ℛ","rsh":"↱","Rsh":"↱","rsqb":"]","rsquo":"’","rsquor":"’","rthree":"⋌","rtimes":"⋊","rtri":"▹","rtrie":"⊵","rtrif":"▸","rtriltri":"⧎","RuleDelayed":"⧴","ruluhar":"⥨","rx":"℞","Sacute":"Ś","sacute":"ś","sbquo":"‚","scap":"⪸","Scaron":"Š","scaron":"š","Sc":"⪼","sc":"≻","sccue":"≽","sce":"⪰","scE":"⪴","Scedil":"Ş","scedil":"ş","Scirc":"Ŝ","scirc":"ŝ","scnap":"⪺","scnE":"⪶","scnsim":"⋩","scpolint":"⨓","scsim":"≿","Scy":"С","scy":"с","sdotb":"⊡","sdot":"⋅","sdote":"⩦","searhk":"⤥","searr":"↘","seArr":"⇘","searrow":"↘","sect":"§","semi":";","seswar":"⤩","setminus":"∖","setmn":"∖","sext":"✶","Sfr":"𝔖","sfr":"𝔰","sfrown":"⌢","sharp":"♯","SHCHcy":"Щ","shchcy":"щ","SHcy":"Ш","shcy":"ш","ShortDownArrow":"↓","ShortLeftArrow":"←","shortmid":"∣","shortparallel":"∥","ShortRightArrow":"→","ShortUpArrow":"↑","shy":"­","Sigma":"Σ","sigma":"σ","sigmaf":"ς","sigmav":"ς","sim":"∼","simdot":"⩪","sime":"≃","simeq":"≃","simg":"⪞","simgE":"⪠","siml":"⪝","simlE":"⪟","simne":"≆","simplus":"⨤","simrarr":"⥲","slarr":"←","SmallCircle":"∘","smallsetminus":"∖","smashp":"⨳","smeparsl":"⧤","smid":"∣","smile":"⌣","smt":"⪪","smte":"⪬","smtes":"⪬︀","SOFTcy":"Ь","softcy":"ь","solbar":"⌿","solb":"⧄","sol":"/","Sopf":"𝕊","sopf":"𝕤","spades":"♠","spadesuit":"♠","spar":"∥","sqcap":"⊓","sqcaps":"⊓︀","sqcup":"⊔","sqcups":"⊔︀","Sqrt":"√","sqsub":"⊏","sqsube":"⊑","sqsubset":"⊏","sqsubseteq":"⊑","sqsup":"⊐","sqsupe":"⊒","sqsupset":"⊐","sqsupseteq":"⊒","square":"□","Square":"□","SquareIntersection":"⊓","SquareSubset":"⊏","SquareSubsetEqual":"⊑","SquareSuperset":"⊐","SquareSupersetEqual":"⊒","SquareUnion":"⊔","squarf":"▪","squ":"□","squf":"▪","srarr":"→","Sscr":"𝒮","sscr":"𝓈","ssetmn":"∖","ssmile":"⌣","sstarf":"⋆","Star":"⋆","star":"☆","starf":"★","straightepsilon":"ϵ","straightphi":"ϕ","strns":"¯","sub":"⊂","Sub":"⋐","subdot":"⪽","subE":"⫅","sube":"⊆","subedot":"⫃","submult":"⫁","subnE":"⫋","subne":"⊊","subplus":"⪿","subrarr":"⥹","subset":"⊂","Subset":"⋐","subseteq":"⊆","subseteqq":"⫅","SubsetEqual":"⊆","subsetneq":"⊊","subsetneqq":"⫋","subsim":"⫇","subsub":"⫕","subsup":"⫓","succapprox":"⪸","succ":"≻","succcurlyeq":"≽","Succeeds":"≻","SucceedsEqual":"⪰","SucceedsSlantEqual":"≽","SucceedsTilde":"≿","succeq":"⪰","succnapprox":"⪺","succneqq":"⪶","succnsim":"⋩","succsim":"≿","SuchThat":"∋","sum":"∑","Sum":"∑","sung":"♪","sup1":"¹","sup2":"²","sup3":"³","sup":"⊃","Sup":"⋑","supdot":"⪾","supdsub":"⫘","supE":"⫆","supe":"⊇","supedot":"⫄","Superset":"⊃","SupersetEqual":"⊇","suphsol":"⟉","suphsub":"⫗","suplarr":"⥻","supmult":"⫂","supnE":"⫌","supne":"⊋","supplus":"⫀","supset":"⊃","Supset":"⋑","supseteq":"⊇","supseteqq":"⫆","supsetneq":"⊋","supsetneqq":"⫌","supsim":"⫈","supsub":"⫔","supsup":"⫖","swarhk":"⤦","swarr":"↙","swArr":"⇙","swarrow":"↙","swnwar":"⤪","szlig":"ß","Tab":"\\t","target":"⌖","Tau":"Τ","tau":"τ","tbrk":"⎴","Tcaron":"Ť","tcaron":"ť","Tcedil":"Ţ","tcedil":"ţ","Tcy":"Т","tcy":"т","tdot":"⃛","telrec":"⌕","Tfr":"𝔗","tfr":"𝔱","there4":"∴","therefore":"∴","Therefore":"∴","Theta":"Θ","theta":"θ","thetasym":"ϑ","thetav":"ϑ","thickapprox":"≈","thicksim":"∼","ThickSpace":"  ","ThinSpace":" ","thinsp":" ","thkap":"≈","thksim":"∼","THORN":"Þ","thorn":"þ","tilde":"˜","Tilde":"∼","TildeEqual":"≃","TildeFullEqual":"≅","TildeTilde":"≈","timesbar":"⨱","timesb":"⊠","times":"×","timesd":"⨰","tint":"∭","toea":"⤨","topbot":"⌶","topcir":"⫱","top":"⊤","Topf":"𝕋","topf":"𝕥","topfork":"⫚","tosa":"⤩","tprime":"‴","trade":"™","TRADE":"™","triangle":"▵","triangledown":"▿","triangleleft":"◃","trianglelefteq":"⊴","triangleq":"≜","triangleright":"▹","trianglerighteq":"⊵","tridot":"◬","trie":"≜","triminus":"⨺","TripleDot":"⃛","triplus":"⨹","trisb":"⧍","tritime":"⨻","trpezium":"⏢","Tscr":"𝒯","tscr":"𝓉","TScy":"Ц","tscy":"ц","TSHcy":"Ћ","tshcy":"ћ","Tstrok":"Ŧ","tstrok":"ŧ","twixt":"≬","twoheadleftarrow":"↞","twoheadrightarrow":"↠","Uacute":"Ú","uacute":"ú","uarr":"↑","Uarr":"↟","uArr":"⇑","Uarrocir":"⥉","Ubrcy":"Ў","ubrcy":"ў","Ubreve":"Ŭ","ubreve":"ŭ","Ucirc":"Û","ucirc":"û","Ucy":"У","ucy":"у","udarr":"⇅","Udblac":"Ű","udblac":"ű","udhar":"⥮","ufisht":"⥾","Ufr":"𝔘","ufr":"𝔲","Ugrave":"Ù","ugrave":"ù","uHar":"⥣","uharl":"↿","uharr":"↾","uhblk":"▀","ulcorn":"⌜","ulcorner":"⌜","ulcrop":"⌏","ultri":"◸","Umacr":"Ū","umacr":"ū","uml":"¨","UnderBar":"_","UnderBrace":"⏟","UnderBracket":"⎵","UnderParenthesis":"⏝","Union":"⋃","UnionPlus":"⊎","Uogon":"Ų","uogon":"ų","Uopf":"𝕌","uopf":"𝕦","UpArrowBar":"⤒","uparrow":"↑","UpArrow":"↑","Uparrow":"⇑","UpArrowDownArrow":"⇅","updownarrow":"↕","UpDownArrow":"↕","Updownarrow":"⇕","UpEquilibrium":"⥮","upharpoonleft":"↿","upharpoonright":"↾","uplus":"⊎","UpperLeftArrow":"↖","UpperRightArrow":"↗","upsi":"υ","Upsi":"ϒ","upsih":"ϒ","Upsilon":"Υ","upsilon":"υ","UpTeeArrow":"↥","UpTee":"⊥","upuparrows":"⇈","urcorn":"⌝","urcorner":"⌝","urcrop":"⌎","Uring":"Ů","uring":"ů","urtri":"◹","Uscr":"𝒰","uscr":"𝓊","utdot":"⋰","Utilde":"Ũ","utilde":"ũ","utri":"▵","utrif":"▴","uuarr":"⇈","Uuml":"Ü","uuml":"ü","uwangle":"⦧","vangrt":"⦜","varepsilon":"ϵ","varkappa":"ϰ","varnothing":"∅","varphi":"ϕ","varpi":"ϖ","varpropto":"∝","varr":"↕","vArr":"⇕","varrho":"ϱ","varsigma":"ς","varsubsetneq":"⊊︀","varsubsetneqq":"⫋︀","varsupsetneq":"⊋︀","varsupsetneqq":"⫌︀","vartheta":"ϑ","vartriangleleft":"⊲","vartriangleright":"⊳","vBar":"⫨","Vbar":"⫫","vBarv":"⫩","Vcy":"В","vcy":"в","vdash":"⊢","vDash":"⊨","Vdash":"⊩","VDash":"⊫","Vdashl":"⫦","veebar":"⊻","vee":"∨","Vee":"⋁","veeeq":"≚","vellip":"⋮","verbar":"|","Verbar":"‖","vert":"|","Vert":"‖","VerticalBar":"∣","VerticalLine":"|","VerticalSeparator":"❘","VerticalTilde":"≀","VeryThinSpace":" ","Vfr":"𝔙","vfr":"𝔳","vltri":"⊲","vnsub":"⊂⃒","vnsup":"⊃⃒","Vopf":"𝕍","vopf":"𝕧","vprop":"∝","vrtri":"⊳","Vscr":"𝒱","vscr":"𝓋","vsubnE":"⫋︀","vsubne":"⊊︀","vsupnE":"⫌︀","vsupne":"⊋︀","Vvdash":"⊪","vzigzag":"⦚","Wcirc":"Ŵ","wcirc":"ŵ","wedbar":"⩟","wedge":"∧","Wedge":"⋀","wedgeq":"≙","weierp":"℘","Wfr":"𝔚","wfr":"𝔴","Wopf":"𝕎","wopf":"𝕨","wp":"℘","wr":"≀","wreath":"≀","Wscr":"𝒲","wscr":"𝓌","xcap":"⋂","xcirc":"◯","xcup":"⋃","xdtri":"▽","Xfr":"𝔛","xfr":"𝔵","xharr":"⟷","xhArr":"⟺","Xi":"Ξ","xi":"ξ","xlarr":"⟵","xlArr":"⟸","xmap":"⟼","xnis":"⋻","xodot":"⨀","Xopf":"𝕏","xopf":"𝕩","xoplus":"⨁","xotime":"⨂","xrarr":"⟶","xrArr":"⟹","Xscr":"𝒳","xscr":"𝓍","xsqcup":"⨆","xuplus":"⨄","xutri":"△","xvee":"⋁","xwedge":"⋀","Yacute":"Ý","yacute":"ý","YAcy":"Я","yacy":"я","Ycirc":"Ŷ","ycirc":"ŷ","Ycy":"Ы","ycy":"ы","yen":"¥","Yfr":"𝔜","yfr":"𝔶","YIcy":"Ї","yicy":"ї","Yopf":"𝕐","yopf":"𝕪","Yscr":"𝒴","yscr":"𝓎","YUcy":"Ю","yucy":"ю","yuml":"ÿ","Yuml":"Ÿ","Zacute":"Ź","zacute":"ź","Zcaron":"Ž","zcaron":"ž","Zcy":"З","zcy":"з","Zdot":"Ż","zdot":"ż","zeetrf":"ℨ","ZeroWidthSpace":"​","Zeta":"Ζ","zeta":"ζ","zfr":"𝔷","Zfr":"ℨ","ZHcy":"Ж","zhcy":"ж","zigrarr":"⇝","zopf":"𝕫","Zopf":"ℤ","Zscr":"𝒵","zscr":"𝓏","zwj":"‍","zwnj":"‌"}')}},t={};function n(r){var s=t[r];if(void 0!==s)return s.exports;var i=t[r]={exports:{}};return e[r].call(i.exports,i,i.exports,n),i.exports}n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var r=n(5059),s=exports;for(var i in r)s[i]=r[i];r.__esModule&&Object.defineProperty(s,"__esModule",{value:!0})})();
//# sourceMappingURL=https://main.vscode-cdn.net/sourcemaps/dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/extensions/extension-editing/dist/extensionEditingMain.js.map