export const id=555;export const ids=[555];export const modules={6555:(e,r,t)=>{t.d(r,{graphql:()=>d});var s=t(3698),a=t(5407),o=class extends Error{constructor(e,r,t){super("Request failed due to following response errors:\n"+t.errors.map((e=>` - ${e.message}`)).join("\n")),this.request=e,this.headers=r,this.response=t,this.errors=t.errors,this.data=t.data,Error.captureStackTrace&&Error.captureStackTrace(this,this.constructor)}name="GraphqlResponseError";errors;data},n=["method","baseUrl","url","headers","request","query","mediaType","operationName"],i=["query","method","url"],c=/\/api\/v3\/?$/;var d=function e(r,t){const s=r.defaults(t);return Object.assign(((e,r)=>function(e,r,t){if(t){if("string"==typeof r&&"query"in t)return Promise.reject(new Error('[@octokit/graphql] "query" cannot be used as variable name'));for(const e in t)if(i.includes(e))return Promise.reject(new Error(`[@octokit/graphql] "${e}" cannot be used as variable name`))}const s="string"==typeof r?Object.assign({query:r},t):r,a=Object.keys(s).reduce(((e,r)=>n.includes(r)?(e[r]=s[r],e):(e.variables||(e.variables={}),e.variables[r]=s[r],e)),{}),d=s.baseUrl||e.endpoint.DEFAULTS.baseUrl;return c.test(d)&&(a.url=d.replace(c,"/api/graphql")),e(a).then((e=>{if(e.data.errors){const r={};for(const t of Object.keys(e.headers))r[t]=e.headers[t];throw new o(a,r,e.data)}return e.data.data}))}(s,e,r)),{defaults:e.bind(null,s),endpoint:s.endpoint})}(s.E,{headers:{"user-agent":`octokit-graphql.js/0.0.0-development ${(0,a.$)()}`},method:"POST",url:"/graphql"})}};
//# sourceMappingURL=https://main.vscode-cdn.net/sourcemaps/dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/extensions/github/dist/555.js.map