{"comments": {"lineComment": "//", "blockComment": ["/*", "*/"]}, "brackets": [["{", "}"], ["[", "]"], ["(", ")"]], "autoClosingPairs": [["{", "}"], ["[", "]"], ["(", ")"], {"open": "`", "close": "`", "notIn": ["string"]}, {"open": "\"", "close": "\"", "notIn": ["string"]}, {"open": "'", "close": "'", "notIn": ["string", "comment"]}], "surroundingPairs": [["{", "}"], ["[", "]"], ["(", ")"], ["\"", "\""], ["'", "'"], ["`", "`"]], "indentationRules": {"increaseIndentPattern": "^.*(\\bcase\\b.*:|\\bdefault\\b:|(\\b(func|if|else|switch|select|for|struct)\\b.*)?{[^}\"'`]*|\\([^)\"'`]*)$", "decreaseIndentPattern": "^\\s*(\\bcase\\b.*:|\\bdefault\\b:|}[)}]*[),]?|\\)[,]?)$"}, "folding": {"markers": {"start": "^\\s*//\\s*#?region\\b", "end": "^\\s*//\\s*#?endregion\\b"}}, "onEnterRules": [{"beforeText": {"pattern": "//.*"}, "afterText": {"pattern": "^(?!\\s*$).+"}, "action": {"indent": "none", "appendText": "// "}}]}