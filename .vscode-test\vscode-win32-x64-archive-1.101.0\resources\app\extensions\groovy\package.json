{"name": "groovy", "displayName": "%displayName%", "description": "%description%", "version": "1.0.0", "publisher": "vscode", "license": "MIT", "engines": {"vscode": "*"}, "scripts": {"update-grammar": "node ../node_modules/vscode-grammar-updater/bin textmate/groovy.tmbundle Syntaxes/Groovy.tmLanguage ./syntaxes/groovy.tmLanguage.json"}, "categories": ["Programming Languages"], "contributes": {"languages": [{"id": "groovy", "aliases": ["Groovy", "groovy"], "extensions": [".groovy", ".gvy", ".gradle", ".jen<PERSON>le", ".nf"], "filenames": ["Jenkins<PERSON><PERSON>"], "filenamePatterns": ["Jenkinsfile*"], "firstLine": "^#!.*\\bgroovy\\b", "configuration": "./language-configuration.json"}], "grammars": [{"language": "groovy", "scopeName": "source.groovy", "path": "./syntaxes/groovy.tmLanguage.json"}], "snippets": [{"language": "groovy", "path": "./snippets/groovy.code-snippets"}]}, "repository": {"type": "git", "url": "https://github.com/microsoft/vscode.git"}}