{"name": "ini", "displayName": "%displayName%", "description": "%description%", "version": "1.0.0", "private": true, "publisher": "vscode", "license": "MIT", "engines": {"vscode": "*"}, "scripts": {"update-grammar": "node ../node_modules/vscode-grammar-updater/bin textmate/ini.tmbundle Syntaxes/Ini.plist ./syntaxes/ini.tmLanguage.json"}, "categories": ["Programming Languages"], "contributes": {"languages": [{"id": "ini", "extensions": [".ini"], "aliases": ["Ini", "ini"], "configuration": "./ini.language-configuration.json"}, {"id": "properties", "extensions": [".conf", ".properties", ".cfg", ".directory", ".gitattributes", ".gitconfig", ".git<PERSON><PERSON><PERSON>", ".editorconfig", ".repo"], "filenames": ["gitconfig", ".env"], "filenamePatterns": ["**/.config/git/config", "**/.git/config", ".*.env"], "aliases": ["Properties", "properties"], "configuration": "./properties.language-configuration.json"}], "grammars": [{"language": "ini", "scopeName": "source.ini", "path": "./syntaxes/ini.tmLanguage.json"}, {"language": "properties", "scopeName": "source.ini", "path": "./syntaxes/ini.tmLanguage.json"}]}, "repository": {"type": "git", "url": "https://github.com/microsoft/vscode.git"}}