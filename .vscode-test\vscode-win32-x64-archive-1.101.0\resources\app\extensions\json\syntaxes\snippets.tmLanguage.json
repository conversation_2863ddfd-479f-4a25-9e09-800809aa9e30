{"information_for_contributors": ["This file has been converted from https://github.com/jeff-hykin/better-snippet-syntax/blob/master/autogenerated/jsonc.tmLanguage.json", "If you want to provide a fix or improvement, please create a pull request against the original repository.", "Once accepted there, we are happy to receive an update request."], "version": "https://github.com/jeff-hykin/better-snippet-syntax/commit/2b1bb124cb2b9c75c3c80eae1b8f3a043841d654", "name": "Snippets", "scopeName": "source.json.comments.snippets", "patterns": [{"include": "#value"}], "repository": {"array": {"begin": "\\[", "beginCaptures": {"0": {"name": "punctuation.definition.array.begin.json.comments.snippets"}}, "end": "\\]", "endCaptures": {"0": {"name": "punctuation.definition.array.end.json.comments.snippets"}}, "name": "meta.structure.array.json.comments.snippets", "patterns": [{"include": "#value"}, {"match": ",", "name": "punctuation.separator.array.json.comments.snippets"}, {"match": "[^\\s\\]]", "name": "invalid.illegal.expected-array-separator.json.comments.snippets"}]}, "basic_escape": {"match": "\\\\(?:[\"\\\\\\/bfnrt]|(?:u[0-9a-fA-F]{4}))", "name": "constant.character.escape.json.comments.snippets"}, "bnf_any": {"match": "(?:\\}|((?:(?:(?:(?:(?:(?:((?:(\\$)([0-9]+)))|((?:(?:(\\$)(\\{))([0-9]+)(\\}))))|((?:(?:(\\$)(\\{))([0-9]+)((?:(\\/)((?:(?:(?:(?:(\\\\)(\\\\\\/))|(?:(\\\\\\\\\\\\)(\\\\\\/)))|[^\\/\\n])+))(\\/)(((?:(?:(?:(?:(?:(?:(?:(?:\\$(?:(?<!\\w)(?:TM_SELECTED_TEXT|TM_CURRENT_LINE|TM_CURRENT_WORD|TM_LINE_INDEX|TM_LINE_NUMBER|TM_FILENAME|TM_FILENAME_BASE|TM_DIRECTORY|TM_FILEPATH|RELATIVE_FILEPATH|CLIPBOARD|WORKSPACE_NAME|WORKSPACE_FOLDER|CURSOR_INDEX|CURSOR_NUMBER|CURRENT_YEAR|CURRENT_YEAR_SHORT|CURRENT_MONTH|CURRENT_MONTH_NAME|CURRENT_MONTH_NAME_SHORT|CURRENT_DATE|CURRENT_DAY_NAME|CURRENT_DAY_NAME_SHORT|CURRENT_HOUR|CURRENT_MINUTE|CURRENT_SECOND|CURRENT_SECONDS_UNIX|CURRENT_TIMEZONE_OFFSET|RANDOM|RANDOM_HEX|UUID|BLOCK_COMMENT_START|BLOCK_COMMENT_END|LINE_COMMENT)(?!\\w)))|(?:\\$(?:[0-9]+)))|(?:(?:\\$\\{)(?:[0-9]+):(?:\\/(?:upcase|downcase|capitalize|camelcase|pascalcase))\\}))|(?:(?:\\$\\{)(?:[0-9]+):\\+(?:(?:(?:(?:(?!\\\\)(?:(?:(?:\\\\\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:[^\\{\\$\"\\\\]|(?=\")))|(?:(?:(?:\\\\\\\\\\\\\\\\)+)(?:[^\\{\\$\"\\\\]|(?=\")))))|(?:(?<!\\\\)(?:(?:(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\$))|(?:\\\\\\$)))|(?:(?<!\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\}))|(?:(?:\\\\\\\\)(?:\\\\\\\\))|(?:\\\\(?:[\"\\\\\\/bfnrt]|(?:u[0-9a-fA-F]{4})))|(?:\\\\.)|[^\\\\\\n\\}\"])?)(?:(?:(?:(?!\\\\)(?:(?:(?:\\\\\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:[^\\{\\$\"\\\\]|(?=\")))|(?:(?:(?:\\\\\\\\\\\\\\\\)+)(?:[^\\{\\$\"\\\\]|(?=\")))))|(?:(?<!\\\\)(?:(?:(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\$))|(?:\\\\\\$)))|(?:(?<!\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\}))|(?:(?:\\\\\\\\)(?:\\\\\\\\))|(?:\\\\(?:[\"\\\\\\/bfnrt]|(?:u[0-9a-fA-F]{4})))|(?:\\\\.)|[^\\\\\\n\\}\"])*?))\\}))|(?:(?:\\$\\{)(?:[0-9]+):\\?(?:(?:(?:(?:(?!\\\\)(?:(?:(?:\\\\\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:[^\\{\\$\"\\\\]|(?=\")))|(?:(?:(?:\\\\\\\\\\\\\\\\)+)(?:[^\\{\\$\"\\\\]|(?=\")))))|(?:(?<!\\\\)(?:(?:(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\$))|(?:\\\\\\$)))|(?:(?<!\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\}))|(?:(?:\\\\\\\\)(?:\\\\\\\\))|(?:\\\\(?:[\"\\\\\\/bfnrt]|(?:u[0-9a-fA-F]{4})))|(?:\\\\.)|[^\\\\\\n\\}\"])?)(?:(?:(?:(?!\\\\)(?:(?:(?:\\\\\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:[^\\{\\$\"\\\\]|(?=\")))|(?:(?:(?:\\\\\\\\\\\\\\\\)+)(?:[^\\{\\$\"\\\\]|(?=\")))))|(?:(?<!\\\\)(?:(?:(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\$))|(?:\\\\\\$)))|(?:(?<!\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\}))|(?:(?:\\\\\\\\)(?:\\\\\\\\))|(?:\\\\(?:[\"\\\\\\/bfnrt]|(?:u[0-9a-fA-F]{4})))|(?:\\\\.)|[^\\\\\\n\\}\"])*?)):(?:(?:(?:(?:(?!\\\\)(?:(?:(?:\\\\\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:[^\\{\\$\"\\\\]|(?=\")))|(?:(?:(?:\\\\\\\\\\\\\\\\)+)(?:[^\\{\\$\"\\\\]|(?=\")))))|(?:(?<!\\\\)(?:(?:(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\$))|(?:\\\\\\$)))|(?:(?<!\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\}))|(?:(?:\\\\\\\\)(?:\\\\\\\\))|(?:\\\\(?:[\"\\\\\\/bfnrt]|(?:u[0-9a-fA-F]{4})))|(?:\\\\.)|[^\\\\\\n\\}\"])?)(?:(?:(?:(?!\\\\)(?:(?:(?:\\\\\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:[^\\{\\$\"\\\\]|(?=\")))|(?:(?:(?:\\\\\\\\\\\\\\\\)+)(?:[^\\{\\$\"\\\\]|(?=\")))))|(?:(?<!\\\\)(?:(?:(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\$))|(?:\\\\\\$)))|(?:(?<!\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\}))|(?:(?:\\\\\\\\)(?:\\\\\\\\))|(?:\\\\(?:[\"\\\\\\/bfnrt]|(?:u[0-9a-fA-F]{4})))|(?:\\\\.)|[^\\\\\\n\\}\"])*?))\\}))|(?:(?:\\$\\{)(?:[0-9]+):\\-(?:(?:(?:(?:(?!\\\\)(?:(?:(?:\\\\\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:[^\\{\\$\"\\\\]|(?=\")))|(?:(?:(?:\\\\\\\\\\\\\\\\)+)(?:[^\\{\\$\"\\\\]|(?=\")))))|(?:(?<!\\\\)(?:(?:(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\$))|(?:\\\\\\$)))|(?:(?<!\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\}))|(?:(?:\\\\\\\\)(?:\\\\\\\\))|(?:\\\\(?:[\"\\\\\\/bfnrt]|(?:u[0-9a-fA-F]{4})))|(?:\\\\.)|[^\\\\\\n\\}\"])?)(?:(?:(?:(?!\\\\)(?:(?:(?:\\\\\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:[^\\{\\$\"\\\\]|(?=\")))|(?:(?:(?:\\\\\\\\\\\\\\\\)+)(?:[^\\{\\$\"\\\\]|(?=\")))))|(?:(?<!\\\\)(?:(?:(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\$))|(?:\\\\\\$)))|(?:(?<!\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\}))|(?:(?:\\\\\\\\)(?:\\\\\\\\))|(?:\\\\(?:[\"\\\\\\/bfnrt]|(?:u[0-9a-fA-F]{4})))|(?:\\\\.)|[^\\\\\\n\\}\"])*?))\\}))|(?:(?:\\$\\{)(?:[0-9]+):(?:(?:(?:(?:(?!\\\\)(?:(?:(?:\\\\\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:[^\\{\\$\"\\\\]|(?=\")))|(?:(?:(?:\\\\\\\\\\\\\\\\)+)(?:[^\\{\\$\"\\\\]|(?=\")))))|(?:(?<!\\\\)(?:(?:(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\$))|(?:\\\\\\$)))|(?:(?<!\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\}))|(?:(?:\\\\\\\\)(?:\\\\\\\\))|(?:\\\\(?:[\"\\\\\\/bfnrt]|(?:u[0-9a-fA-F]{4})))|(?:\\\\.)|[^\\\\\\n\\}\"])?)(?:(?:(?:(?!\\\\)(?:(?:(?:\\\\\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:[^\\{\\$\"\\\\]|(?=\")))|(?:(?:(?:\\\\\\\\\\\\\\\\)+)(?:[^\\{\\$\"\\\\]|(?=\")))))|(?:(?<!\\\\)(?:(?:(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\$))|(?:\\\\\\$)))|(?:(?<!\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\}))|(?:(?:\\\\\\\\)(?:\\\\\\\\))|(?:\\\\(?:[\"\\\\\\/bfnrt]|(?:u[0-9a-fA-F]{4})))|(?:\\\\.)|[^\\\\\\n\\}\"])*?))\\}))|(?:(?:(?:\\\\(?:\\\\\\/))|(?:(?:\\\\\\\\\\\\)(?:\\\\\\/)))|(?:(?:(?:(?:(?!\\\\)(?:(?:(?:\\\\\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:[^\\{\\$\"\\\\]|(?=\")))|(?:(?:(?:\\\\\\\\\\\\\\\\)+)(?:[^\\{\\$\"\\\\]|(?=\")))))|(?:(?<!\\\\)(?:(?:(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\$))|(?:\\\\\\$)))|(?:(?<!\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\}))|(?:(?:\\\\\\\\)(?:\\\\\\\\))|(?:\\\\(?:[\"\\\\\\/bfnrt]|(?:u[0-9a-fA-F]{4})))|(?:\\\\.)|[^\\\\\\n\\}\"])|[^\\n\\r])*)))*))(\\/)([igmyu]{0,5})))(\\}))))|((?:(?:(\\$)(\\{))([0-9]+)(\\|)(((?:(?:(?:(\\\\\\\\)(\\\\\\\\))|(?:(\\/\\/)(?:\\,|\\|))|((?!\\\\)(?:(?:(?:\\\\\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:[^\\{\\$\"\\\\]|(?=\")))|(?:(?:(?:\\\\\\\\\\\\\\\\)+)(?:[^\\{\\$\"\\\\]|(?=\")))))|((?<!\\\\)(?:(?:(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\$))|(?:\\\\\\$)))|((?<!\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\}))|(\\\\(?:[\"\\\\\\/bfnrt]|(?:u[0-9a-fA-F]{4})))|[^,}\\|])+))(?:(?:,\\g<33>)*?))(\\|)(\\}))))|((?:(?:(\\$)(\\{))([0-9]+)(:)(?:(?:(?:(?:(?:\\$(?:[0-9]+))|(?:(?:\\$\\{)(?:[0-9]+)\\}))|(?:(?:\\$\\{)(?:[0-9]+)(?:\\/((?:(?:(?:(?:\\\\(?:\\\\\\/))|(?:(?:\\\\\\\\\\\\)(?:\\\\\\/)))|[^\\/\\n])+))\\/((?:(?:(?:(?:(?:(?:(?:(?:(?:\\$(?:(?<!\\w)(?:TM_SELECTED_TEXT|TM_CURRENT_LINE|TM_CURRENT_WORD|TM_LINE_INDEX|TM_LINE_NUMBER|TM_FILENAME|TM_FILENAME_BASE|TM_DIRECTORY|TM_FILEPATH|RELATIVE_FILEPATH|CLIPBOARD|WORKSPACE_NAME|WORKSPACE_FOLDER|CURSOR_INDEX|CURSOR_NUMBER|CURRENT_YEAR|CURRENT_YEAR_SHORT|CURRENT_MONTH|CURRENT_MONTH_NAME|CURRENT_MONTH_NAME_SHORT|CURRENT_DATE|CURRENT_DAY_NAME|CURRENT_DAY_NAME_SHORT|CURRENT_HOUR|CURRENT_MINUTE|CURRENT_SECOND|CURRENT_SECONDS_UNIX|CURRENT_TIMEZONE_OFFSET|RANDOM|RANDOM_HEX|UUID|BLOCK_COMMENT_START|BLOCK_COMMENT_END|LINE_COMMENT)(?!\\w)))|(?:\\$(?:[0-9]+)))|(?:(?:\\$\\{)(?:[0-9]+):(?:\\/(?:upcase|downcase|capitalize|camelcase|pascalcase))\\}))|(?:(?:\\$\\{)(?:[0-9]+):\\+((?:(?:((?!\\\\)(?:(?:(?:\\\\\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:[^\\{\\$\"\\\\]|(?=\")))|(?:(?:(?:\\\\\\\\\\\\\\\\)+)(?:[^\\{\\$\"\\\\]|(?=\")))))|((?<!\\\\)(?:(?:(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\$))|(?:\\\\\\$)))|((?<!\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\}))|(?:(?:\\\\\\\\)(?:\\\\\\\\))|(?:\\\\(?:[\"\\\\\\/bfnrt]|(?:u[0-9a-fA-F]{4})))|(?:\\\\.)|[^\\\\\\n\\}\"])?)(?:(?:((?!\\\\)(?:(?:(?:\\\\\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:[^\\{\\$\"\\\\]|(?=\")))|(?:(?:(?:\\\\\\\\\\\\\\\\)+)(?:[^\\{\\$\"\\\\]|(?=\")))))|((?<!\\\\)(?:(?:(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\$))|(?:\\\\\\$)))|((?<!\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\}))|(?:(?:\\\\\\\\)(?:\\\\\\\\))|(?:\\\\(?:[\"\\\\\\/bfnrt]|(?:u[0-9a-fA-F]{4})))|(?:\\\\.)|[^\\\\\\n\\}\"])*?))\\}))|(?:(?:\\$\\{)(?:[0-9]+):\\?((?:(?:((?!\\\\)(?:(?:(?:\\\\\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:[^\\{\\$\"\\\\]|(?=\")))|(?:(?:(?:\\\\\\\\\\\\\\\\)+)(?:[^\\{\\$\"\\\\]|(?=\")))))|((?<!\\\\)(?:(?:(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\$))|(?:\\\\\\$)))|((?<!\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\}))|(?:(?:\\\\\\\\)(?:\\\\\\\\))|(?:\\\\(?:[\"\\\\\\/bfnrt]|(?:u[0-9a-fA-F]{4})))|(?:\\\\.)|[^\\\\\\n\\}\"])?)(?:(?:((?!\\\\)(?:(?:(?:\\\\\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:[^\\{\\$\"\\\\]|(?=\")))|(?:(?:(?:\\\\\\\\\\\\\\\\)+)(?:[^\\{\\$\"\\\\]|(?=\")))))|((?<!\\\\)(?:(?:(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\$))|(?:\\\\\\$)))|((?<!\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\}))|(?:(?:\\\\\\\\)(?:\\\\\\\\))|(?:\\\\(?:[\"\\\\\\/bfnrt]|(?:u[0-9a-fA-F]{4})))|(?:\\\\.)|[^\\\\\\n\\}\"])*?)):((?:(?:((?!\\\\)(?:(?:(?:\\\\\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:[^\\{\\$\"\\\\]|(?=\")))|(?:(?:(?:\\\\\\\\\\\\\\\\)+)(?:[^\\{\\$\"\\\\]|(?=\")))))|((?<!\\\\)(?:(?:(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\$))|(?:\\\\\\$)))|((?<!\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\}))|(?:(?:\\\\\\\\)(?:\\\\\\\\))|(?:\\\\(?:[\"\\\\\\/bfnrt]|(?:u[0-9a-fA-F]{4})))|(?:\\\\.)|[^\\\\\\n\\}\"])?)(?:(?:((?!\\\\)(?:(?:(?:\\\\\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:[^\\{\\$\"\\\\]|(?=\")))|(?:(?:(?:\\\\\\\\\\\\\\\\)+)(?:[^\\{\\$\"\\\\]|(?=\")))))|((?<!\\\\)(?:(?:(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\$))|(?:\\\\\\$)))|((?<!\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\}))|(?:(?:\\\\\\\\)(?:\\\\\\\\))|(?:\\\\(?:[\"\\\\\\/bfnrt]|(?:u[0-9a-fA-F]{4})))|(?:\\\\.)|[^\\\\\\n\\}\"])*?))\\}))|(?:(?:\\$\\{)(?:[0-9]+):\\-((?:(?:((?!\\\\)(?:(?:(?:\\\\\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:[^\\{\\$\"\\\\]|(?=\")))|(?:(?:(?:\\\\\\\\\\\\\\\\)+)(?:[^\\{\\$\"\\\\]|(?=\")))))|((?<!\\\\)(?:(?:(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\$))|(?:\\\\\\$)))|((?<!\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\}))|(?:(?:\\\\\\\\)(?:\\\\\\\\))|(?:\\\\(?:[\"\\\\\\/bfnrt]|(?:u[0-9a-fA-F]{4})))|(?:\\\\.)|[^\\\\\\n\\}\"])?)(?:(?:((?!\\\\)(?:(?:(?:\\\\\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:[^\\{\\$\"\\\\]|(?=\")))|(?:(?:(?:\\\\\\\\\\\\\\\\)+)(?:[^\\{\\$\"\\\\]|(?=\")))))|((?<!\\\\)(?:(?:(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\$))|(?:\\\\\\$)))|((?<!\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\}))|(?:(?:\\\\\\\\)(?:\\\\\\\\))|(?:\\\\(?:[\"\\\\\\/bfnrt]|(?:u[0-9a-fA-F]{4})))|(?:\\\\.)|[^\\\\\\n\\}\"])*?))\\}))|(?:(?:\\$\\{)(?:[0-9]+):((?:(?:((?!\\\\)(?:(?:(?:\\\\\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:[^\\{\\$\"\\\\]|(?=\")))|(?:(?:(?:\\\\\\\\\\\\\\\\)+)(?:[^\\{\\$\"\\\\]|(?=\")))))|((?<!\\\\)(?:(?:(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\$))|(?:\\\\\\$)))|((?<!\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\}))|(?:(?:\\\\\\\\)(?:\\\\\\\\))|(?:\\\\(?:[\"\\\\\\/bfnrt]|(?:u[0-9a-fA-F]{4})))|(?:\\\\.)|[^\\\\\\n\\}\"])?)(?:(?:((?!\\\\)(?:(?:(?:\\\\\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:[^\\{\\$\"\\\\]|(?=\")))|(?:(?:(?:\\\\\\\\\\\\\\\\)+)(?:[^\\{\\$\"\\\\]|(?=\")))))|((?<!\\\\)(?:(?:(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\$))|(?:\\\\\\$)))|((?<!\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\}))|(?:(?:\\\\\\\\)(?:\\\\\\\\))|(?:\\\\(?:[\"\\\\\\/bfnrt]|(?:u[0-9a-fA-F]{4})))|(?:\\\\.)|[^\\\\\\n\\}\"])*?))\\}))|(?:(?:(?:\\\\(?:\\\\\\/))|(?:(?:\\\\\\\\\\\\)(?:\\\\\\/)))|((?:(?:(?:((?!\\\\)(?:(?:(?:\\\\\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:[^\\{\\$\"\\\\]|(?=\")))|(?:(?:(?:\\\\\\\\\\\\\\\\)+)(?:[^\\{\\$\"\\\\]|(?=\")))))|((?<!\\\\)(?:(?:(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\$))|(?:\\\\\\$)))|((?<!\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\}))|(?:(?:\\\\\\\\)(?:\\\\\\\\))|(?:\\\\(?:[\"\\\\\\/bfnrt]|(?:u[0-9a-fA-F]{4})))|(?:\\\\.)|[^\\\\\\n\\}\"])|[^\\n\\r])*))))*))\\/(?:[igmyu]{0,5}))\\}))|\\g<1>)+)(\\}))))|(?:(?:(?:((?:(\\$)((?<!\\w)(?:[_a-zA-Z][_a-zA-Z0-9]*)(?!\\w))))|((?:(?:(\\$)(\\{))((?<!\\w)(?:[_a-zA-Z][_a-zA-Z0-9]*)(?!\\w))(\\}))))|((?:(?:(\\$)(\\{))((?<!\\w)(?:[_a-zA-Z][_a-zA-Z0-9]*)(?!\\w))(:)((?:\\g<1>+))(\\}))))|((?:(?:(\\$)(\\{))((?<!\\w)(?:[_a-zA-Z][_a-zA-Z0-9]*)(?!\\w))((?:(?:\\/(?:(?:(?:(?:(\\\\)(\\\\\\/))|(?:(\\\\\\\\\\\\)(\\\\\\/)))|[^\\/])+?)\\/(?:.*?)\\/(?:[igmyu]{0,5}))|((?:(\\/)((?:(?:(?:(?:(\\\\)(\\\\\\/))|(?:(\\\\\\\\\\\\)(\\\\\\/)))|[^\\/\\n])+))(\\/)(((?:(?:(?:(?:(?:(?:(?:(?:\\$(?:(?<!\\w)(?:TM_SELECTED_TEXT|TM_CURRENT_LINE|TM_CURRENT_WORD|TM_LINE_INDEX|TM_LINE_NUMBER|TM_FILENAME|TM_FILENAME_BASE|TM_DIRECTORY|TM_FILEPATH|RELATIVE_FILEPATH|CLIPBOARD|WORKSPACE_NAME|WORKSPACE_FOLDER|CURSOR_INDEX|CURSOR_NUMBER|CURRENT_YEAR|CURRENT_YEAR_SHORT|CURRENT_MONTH|CURRENT_MONTH_NAME|CURRENT_MONTH_NAME_SHORT|CURRENT_DATE|CURRENT_DAY_NAME|CURRENT_DAY_NAME_SHORT|CURRENT_HOUR|CURRENT_MINUTE|CURRENT_SECOND|CURRENT_SECONDS_UNIX|CURRENT_TIMEZONE_OFFSET|RANDOM|RANDOM_HEX|UUID|BLOCK_COMMENT_START|BLOCK_COMMENT_END|LINE_COMMENT)(?!\\w)))|(?:\\$(?:[0-9]+)))|(?:(?:\\$\\{)(?:[0-9]+):(?:\\/(?:upcase|downcase|capitalize|camelcase|pascalcase))\\}))|(?:(?:\\$\\{)(?:[0-9]+):\\+(?:(?:(?:(?:(?!\\\\)(?:(?:(?:\\\\\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:[^\\{\\$\"\\\\]|(?=\")))|(?:(?:(?:\\\\\\\\\\\\\\\\)+)(?:[^\\{\\$\"\\\\]|(?=\")))))|(?:(?<!\\\\)(?:(?:(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\$))|(?:\\\\\\$)))|(?:(?<!\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\}))|(?:(?:\\\\\\\\)(?:\\\\\\\\))|(?:\\\\(?:[\"\\\\\\/bfnrt]|(?:u[0-9a-fA-F]{4})))|(?:\\\\.)|[^\\\\\\n\\}\"])?)(?:(?:(?:(?!\\\\)(?:(?:(?:\\\\\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:[^\\{\\$\"\\\\]|(?=\")))|(?:(?:(?:\\\\\\\\\\\\\\\\)+)(?:[^\\{\\$\"\\\\]|(?=\")))))|(?:(?<!\\\\)(?:(?:(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\$))|(?:\\\\\\$)))|(?:(?<!\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\}))|(?:(?:\\\\\\\\)(?:\\\\\\\\))|(?:\\\\(?:[\"\\\\\\/bfnrt]|(?:u[0-9a-fA-F]{4})))|(?:\\\\.)|[^\\\\\\n\\}\"])*?))\\}))|(?:(?:\\$\\{)(?:[0-9]+):\\?(?:(?:(?:(?:(?!\\\\)(?:(?:(?:\\\\\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:[^\\{\\$\"\\\\]|(?=\")))|(?:(?:(?:\\\\\\\\\\\\\\\\)+)(?:[^\\{\\$\"\\\\]|(?=\")))))|(?:(?<!\\\\)(?:(?:(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\$))|(?:\\\\\\$)))|(?:(?<!\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\}))|(?:(?:\\\\\\\\)(?:\\\\\\\\))|(?:\\\\(?:[\"\\\\\\/bfnrt]|(?:u[0-9a-fA-F]{4})))|(?:\\\\.)|[^\\\\\\n\\}\"])?)(?:(?:(?:(?!\\\\)(?:(?:(?:\\\\\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:[^\\{\\$\"\\\\]|(?=\")))|(?:(?:(?:\\\\\\\\\\\\\\\\)+)(?:[^\\{\\$\"\\\\]|(?=\")))))|(?:(?<!\\\\)(?:(?:(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\$))|(?:\\\\\\$)))|(?:(?<!\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\}))|(?:(?:\\\\\\\\)(?:\\\\\\\\))|(?:\\\\(?:[\"\\\\\\/bfnrt]|(?:u[0-9a-fA-F]{4})))|(?:\\\\.)|[^\\\\\\n\\}\"])*?)):(?:(?:(?:(?:(?!\\\\)(?:(?:(?:\\\\\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:[^\\{\\$\"\\\\]|(?=\")))|(?:(?:(?:\\\\\\\\\\\\\\\\)+)(?:[^\\{\\$\"\\\\]|(?=\")))))|(?:(?<!\\\\)(?:(?:(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\$))|(?:\\\\\\$)))|(?:(?<!\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\}))|(?:(?:\\\\\\\\)(?:\\\\\\\\))|(?:\\\\(?:[\"\\\\\\/bfnrt]|(?:u[0-9a-fA-F]{4})))|(?:\\\\.)|[^\\\\\\n\\}\"])?)(?:(?:(?:(?!\\\\)(?:(?:(?:\\\\\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:[^\\{\\$\"\\\\]|(?=\")))|(?:(?:(?:\\\\\\\\\\\\\\\\)+)(?:[^\\{\\$\"\\\\]|(?=\")))))|(?:(?<!\\\\)(?:(?:(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\$))|(?:\\\\\\$)))|(?:(?<!\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\}))|(?:(?:\\\\\\\\)(?:\\\\\\\\))|(?:\\\\(?:[\"\\\\\\/bfnrt]|(?:u[0-9a-fA-F]{4})))|(?:\\\\.)|[^\\\\\\n\\}\"])*?))\\}))|(?:(?:\\$\\{)(?:[0-9]+):\\-(?:(?:(?:(?:(?!\\\\)(?:(?:(?:\\\\\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:[^\\{\\$\"\\\\]|(?=\")))|(?:(?:(?:\\\\\\\\\\\\\\\\)+)(?:[^\\{\\$\"\\\\]|(?=\")))))|(?:(?<!\\\\)(?:(?:(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\$))|(?:\\\\\\$)))|(?:(?<!\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\}))|(?:(?:\\\\\\\\)(?:\\\\\\\\))|(?:\\\\(?:[\"\\\\\\/bfnrt]|(?:u[0-9a-fA-F]{4})))|(?:\\\\.)|[^\\\\\\n\\}\"])?)(?:(?:(?:(?!\\\\)(?:(?:(?:\\\\\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:[^\\{\\$\"\\\\]|(?=\")))|(?:(?:(?:\\\\\\\\\\\\\\\\)+)(?:[^\\{\\$\"\\\\]|(?=\")))))|(?:(?<!\\\\)(?:(?:(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\$))|(?:\\\\\\$)))|(?:(?<!\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\}))|(?:(?:\\\\\\\\)(?:\\\\\\\\))|(?:\\\\(?:[\"\\\\\\/bfnrt]|(?:u[0-9a-fA-F]{4})))|(?:\\\\.)|[^\\\\\\n\\}\"])*?))\\}))|(?:(?:\\$\\{)(?:[0-9]+):(?:(?:(?:(?:(?!\\\\)(?:(?:(?:\\\\\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:[^\\{\\$\"\\\\]|(?=\")))|(?:(?:(?:\\\\\\\\\\\\\\\\)+)(?:[^\\{\\$\"\\\\]|(?=\")))))|(?:(?<!\\\\)(?:(?:(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\$))|(?:\\\\\\$)))|(?:(?<!\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\}))|(?:(?:\\\\\\\\)(?:\\\\\\\\))|(?:\\\\(?:[\"\\\\\\/bfnrt]|(?:u[0-9a-fA-F]{4})))|(?:\\\\.)|[^\\\\\\n\\}\"])?)(?:(?:(?:(?!\\\\)(?:(?:(?:\\\\\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:[^\\{\\$\"\\\\]|(?=\")))|(?:(?:(?:\\\\\\\\\\\\\\\\)+)(?:[^\\{\\$\"\\\\]|(?=\")))))|(?:(?<!\\\\)(?:(?:(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\$))|(?:\\\\\\$)))|(?:(?<!\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\}))|(?:(?:\\\\\\\\)(?:\\\\\\\\))|(?:\\\\(?:[\"\\\\\\/bfnrt]|(?:u[0-9a-fA-F]{4})))|(?:\\\\.)|[^\\\\\\n\\}\"])*?))\\}))|(?:(?:(?:\\\\(?:\\\\\\/))|(?:(?:\\\\\\\\\\\\)(?:\\\\\\/)))|(?:(?:(?:(?:(?!\\\\)(?:(?:(?:\\\\\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:[^\\{\\$\"\\\\]|(?=\")))|(?:(?:(?:\\\\\\\\\\\\\\\\)+)(?:[^\\{\\$\"\\\\]|(?=\")))))|(?:(?<!\\\\)(?:(?:(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\$))|(?:\\\\\\$)))|(?:(?<!\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\}))|(?:(?:\\\\\\\\)(?:\\\\\\\\))|(?:\\\\(?:[\"\\\\\\/bfnrt]|(?:u[0-9a-fA-F]{4})))|(?:\\\\.)|[^\\\\\\n\\}\"])|[^\\n\\r])*)))*))(\\/)([igmyu]{0,5})))))(\\})))))|(((?:(?:((?!\\\\)(?:(?:(?:\\\\\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:[^\\{\\$\"\\\\]|(?=\")))|(?:(?:(?:\\\\\\\\\\\\\\\\)+)(?:[^\\{\\$\"\\\\]|(?=\")))))|((?<!\\\\)(?:(?:(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\$))|(?:\\\\\\$)))|((?<!\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\}))|(?:(\\\\\\\\)(\\\\\\\\))|(\\\\(?:[\"\\\\\\/bfnrt]|(?:u[0-9a-fA-F]{4})))|((?:\\\\.))|([^\\\\\\n\\}\"]))?)(?:(?:((?!\\\\)(?:(?:(?:\\\\\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:[^\\{\\$\"\\\\]|(?=\")))|(?:(?:(?:\\\\\\\\\\\\\\\\)+)(?:[^\\{\\$\"\\\\]|(?=\")))))|((?<!\\\\)(?:(?:(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\$))|(?:\\\\\\$)))|((?<!\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\}))|(?:(\\\\\\\\)(\\\\\\\\))|(\\\\(?:[\"\\\\\\/bfnrt]|(?:u[0-9a-fA-F]{4})))|((?:\\\\.))|([^\\\\\\n\\}\"]))*?))))))", "captures": {"2": {"name": "meta.insertion.simple.numeric.json.comments.snippets meta.insertion.tabstop.simple.json.comments.snippets"}, "3": {"name": "punctuation.section.insertion.dollar.simple.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.section.insertion.dollar.simple.json.comments.snippets"}, "4": {"name": "variable.other.normal.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.variable.other.normal.numeric.json.comments.snippets"}, "5": {"name": "meta.insertion.brackets.json.comments.snippets meta.insertion.tabstop.bracket.json.comments.snippets"}, "6": {"name": "punctuation.section.insertion.dollar.brackets.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.section.insertion.dollar.brackets.json.comments.snippets"}, "7": {"name": "punctuation.section.insertion.bracket.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.section.insertion.bracket.json.comments.snippets"}, "8": {"name": "variable.other.normal.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.variable.other.normal.numeric.json.comments.snippets"}, "9": {"name": "punctuation.section.insertion.bracket.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.section.insertion.bracket.json.comments.snippets"}, "10": {"name": "meta.insertion.brackets.json.comments.snippets meta.insertion.tabstop.transform.json.comments.snippets"}, "11": {"name": "punctuation.section.insertion.dollar.brackets.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.section.insertion.dollar.brackets.json.comments.snippets"}, "12": {"name": "punctuation.section.insertion.bracket.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.section.insertion.bracket.json.comments.snippets"}, "13": {"name": "variable.other.normal.json.comments.snippets custom.variable.other.normal.numeric.json.comments.snippets"}, "14": {"name": "meta.insertion.transform.json.comments.snippets string.regexp.json.comments.snippets"}, "15": {"name": "punctuation.section.regexp.json.comments.snippets"}, "16": {"patterns": [{"include": "source.syntax.regexp.tmLanguage"}, {"match": "(?:(?:(\\\\)(\\\\\\/))|(?:(\\\\\\\\\\\\)(\\\\\\/)))", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}, "2": {"name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}, "3": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}, "4": {"name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}}}, {"include": "#simple_escape_context"}]}, "17": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}, "18": {"name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}, "19": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}, "20": {"name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}, "21": {"name": "punctuation.section.regexp.json.comments.snippets"}, "22": {"patterns": [{"match": "\\$\\d+", "name": "variable.language.capture.json.comments.snippets"}, {"match": "\\$\\{\\d+\\}", "name": "variable.language.capture.json.comments.snippets"}, {"include": "#bnf_format"}, {"include": "#regex_backslash_escape"}, {"include": "#bnf_text"}]}, "23": {"patterns": [{"match": "(?:(?:(?:(?:(?:(?:(?:(?:(\\$)((?<!\\w)(?:TM_SELECTED_TEXT|TM_CURRENT_LINE|TM_CURRENT_WORD|TM_LINE_INDEX|TM_LINE_NUMBER|TM_FILENAME|TM_FILENAME_BASE|TM_DIRECTORY|TM_FILEPATH|RELATIVE_FILEPATH|CLIPBOARD|WORKSPACE_NAME|WORKSPACE_FOLDER|CURSOR_INDEX|CURSOR_NUMBER|CURRENT_YEAR|CURRENT_YEAR_SHORT|CURRENT_MONTH|CURRENT_MONTH_NAME|CURRENT_MONTH_NAME_SHORT|CURRENT_DATE|CURRENT_DAY_NAME|CURRENT_DAY_NAME_SHORT|CURRENT_HOUR|CURRENT_MINUTE|CURRENT_SECOND|CURRENT_SECONDS_UNIX|CURRENT_TIMEZONE_OFFSET|RANDOM|RANDOM_HEX|UUID|BLOCK_COMMENT_START|BLOCK_COMMENT_END|LINE_COMMENT)(?!\\w)))|((?:(\\$)([0-9]+))))|((?:(?:(\\$)(\\{))([0-9]+)(:)(?:(\\/)(upcase|downcase|capitalize|camelcase|pascalcase))(\\}))))|((?:(?:(\\$)(\\{))([0-9]+)(:)(\\+)((?:(?:((?!\\\\)(?:(?:(?:\\\\\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:[^\\{\\$\"\\\\]|(?=\")))|(?:(?:(?:\\\\\\\\\\\\\\\\)+)(?:[^\\{\\$\"\\\\]|(?=\")))))|((?<!\\\\)(?:(?:(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\$))|(?:\\\\\\$)))|((?<!\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\}))|(?:(\\\\\\\\)(\\\\\\\\))|(\\\\(?:[\"\\\\\\/bfnrt]|(?:u[0-9a-fA-F]{4})))|((?:\\\\.))|([^\\\\\\n\\}\"]))?)(?:(?:((?!\\\\)(?:(?:(?:\\\\\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:[^\\{\\$\"\\\\]|(?=\")))|(?:(?:(?:\\\\\\\\\\\\\\\\)+)(?:[^\\{\\$\"\\\\]|(?=\")))))|((?<!\\\\)(?:(?:(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\$))|(?:\\\\\\$)))|((?<!\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\}))|(?:(\\\\\\\\)(\\\\\\\\))|(\\\\(?:[\"\\\\\\/bfnrt]|(?:u[0-9a-fA-F]{4})))|((?:\\\\.))|([^\\\\\\n\\}\"]))*?))(\\}))))|((?:(?:(\\$)(\\{))([0-9]+)(:)(\\?)((?:(?:((?!\\\\)(?:(?:(?:\\\\\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:[^\\{\\$\"\\\\]|(?=\")))|(?:(?:(?:\\\\\\\\\\\\\\\\)+)(?:[^\\{\\$\"\\\\]|(?=\")))))|((?<!\\\\)(?:(?:(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\$))|(?:\\\\\\$)))|((?<!\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\}))|(?:(\\\\\\\\)(\\\\\\\\))|(\\\\(?:[\"\\\\\\/bfnrt]|(?:u[0-9a-fA-F]{4})))|((?:\\\\.))|([^\\\\\\n\\}\"]))?)(?:(?:((?!\\\\)(?:(?:(?:\\\\\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:[^\\{\\$\"\\\\]|(?=\")))|(?:(?:(?:\\\\\\\\\\\\\\\\)+)(?:[^\\{\\$\"\\\\]|(?=\")))))|((?<!\\\\)(?:(?:(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\$))|(?:\\\\\\$)))|((?<!\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\}))|(?:(\\\\\\\\)(\\\\\\\\))|(\\\\(?:[\"\\\\\\/bfnrt]|(?:u[0-9a-fA-F]{4})))|((?:\\\\.))|([^\\\\\\n\\}\"]))*?))(:)((?:(?:((?!\\\\)(?:(?:(?:\\\\\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:[^\\{\\$\"\\\\]|(?=\")))|(?:(?:(?:\\\\\\\\\\\\\\\\)+)(?:[^\\{\\$\"\\\\]|(?=\")))))|((?<!\\\\)(?:(?:(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\$))|(?:\\\\\\$)))|((?<!\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\}))|(?:(\\\\\\\\)(\\\\\\\\))|(\\\\(?:[\"\\\\\\/bfnrt]|(?:u[0-9a-fA-F]{4})))|((?:\\\\.))|([^\\\\\\n\\}\"]))?)(?:(?:((?!\\\\)(?:(?:(?:\\\\\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:[^\\{\\$\"\\\\]|(?=\")))|(?:(?:(?:\\\\\\\\\\\\\\\\)+)(?:[^\\{\\$\"\\\\]|(?=\")))))|((?<!\\\\)(?:(?:(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\$))|(?:\\\\\\$)))|((?<!\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\}))|(?:(\\\\\\\\)(\\\\\\\\))|(\\\\(?:[\"\\\\\\/bfnrt]|(?:u[0-9a-fA-F]{4})))|((?:\\\\.))|([^\\\\\\n\\}\"]))*?))(\\}))))|((?:(?:(\\$)(\\{))([0-9]+)(:)(\\-)((?:(?:((?!\\\\)(?:(?:(?:\\\\\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:[^\\{\\$\"\\\\]|(?=\")))|(?:(?:(?:\\\\\\\\\\\\\\\\)+)(?:[^\\{\\$\"\\\\]|(?=\")))))|((?<!\\\\)(?:(?:(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\$))|(?:\\\\\\$)))|((?<!\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\}))|(?:(\\\\\\\\)(\\\\\\\\))|(\\\\(?:[\"\\\\\\/bfnrt]|(?:u[0-9a-fA-F]{4})))|((?:\\\\.))|([^\\\\\\n\\}\"]))?)(?:(?:((?!\\\\)(?:(?:(?:\\\\\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:[^\\{\\$\"\\\\]|(?=\")))|(?:(?:(?:\\\\\\\\\\\\\\\\)+)(?:[^\\{\\$\"\\\\]|(?=\")))))|((?<!\\\\)(?:(?:(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\$))|(?:\\\\\\$)))|((?<!\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\}))|(?:(\\\\\\\\)(\\\\\\\\))|(\\\\(?:[\"\\\\\\/bfnrt]|(?:u[0-9a-fA-F]{4})))|((?:\\\\.))|([^\\\\\\n\\}\"]))*?))(\\}))))|((?:(?:(\\$)(\\{))([0-9]+)(:)((?:(?:((?!\\\\)(?:(?:(?:\\\\\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:[^\\{\\$\"\\\\]|(?=\")))|(?:(?:(?:\\\\\\\\\\\\\\\\)+)(?:[^\\{\\$\"\\\\]|(?=\")))))|((?<!\\\\)(?:(?:(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\$))|(?:\\\\\\$)))|((?<!\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\}))|(?:(\\\\\\\\)(\\\\\\\\))|(\\\\(?:[\"\\\\\\/bfnrt]|(?:u[0-9a-fA-F]{4})))|((?:\\\\.))|([^\\\\\\n\\}\"]))?)(?:(?:((?!\\\\)(?:(?:(?:\\\\\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:[^\\{\\$\"\\\\]|(?=\")))|(?:(?:(?:\\\\\\\\\\\\\\\\)+)(?:[^\\{\\$\"\\\\]|(?=\")))))|((?<!\\\\)(?:(?:(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\$))|(?:\\\\\\$)))|((?<!\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\}))|(?:(\\\\\\\\)(\\\\\\\\))|(\\\\(?:[\"\\\\\\/bfnrt]|(?:u[0-9a-fA-F]{4})))|((?:\\\\.))|([^\\\\\\n\\}\"]))*?))(\\}))))|(?:(?:(?:(\\\\)(\\\\\\/))|(?:(\\\\\\\\\\\\)(\\\\\\/)))|((?:(?:(?:((?!\\\\)(?:(?:(?:\\\\\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:[^\\{\\$\"\\\\]|(?=\")))|(?:(?:(?:\\\\\\\\\\\\\\\\)+)(?:[^\\{\\$\"\\\\]|(?=\")))))|((?<!\\\\)(?:(?:(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\$))|(?:\\\\\\$)))|((?<!\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\}))|(?:(\\\\\\\\)(\\\\\\\\))|(\\\\(?:[\"\\\\\\/bfnrt]|(?:u[0-9a-fA-F]{4})))|((?:\\\\.))|([^\\\\\\n\\}\"]))|[^\\n\\r])*))))", "captures": {"1": {"name": "meta.insertion.simple.json.comments.snippets punctuation.section.insertion.dollar.simple.json.comments.snippets keyword.operator.insertion.json.comments.snippets variable.language.this.json.comments.snippets"}, "2": {"name": "meta.insertion.simple.json.comments.snippets keyword.operator.insertion.json.comments.snippets variable.language.this.json.comments.snippets"}, "3": {"name": "meta.insertion.simple.numeric.json.comments.snippets meta.insertion.format.simple.json.comments.snippets"}, "4": {"name": "punctuation.section.insertion.dollar.simple.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.section.insertion.dollar.simple.json.comments.snippets"}, "5": {"name": "variable.other.normal.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.variable.other.normal.numeric.json.comments.snippets"}, "6": {"name": "meta.insertion.brackets.json.comments.snippets meta.insertion.format.transform.json.comments.snippets"}, "7": {"name": "punctuation.section.insertion.dollar.brackets.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.section.insertion.dollar.brackets.json.comments.snippets"}, "8": {"name": "punctuation.section.insertion.bracket.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.section.insertion.bracket.json.comments.snippets"}, "9": {"name": "variable.other.normal.json.comments.snippets custom.variable.other.normal.numeric.json.comments.snippets"}, "10": {"name": "punctuation.section.insertion.json.comments.snippets punctuation.separator.colon.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.separator.colon.json.comments.snippets"}, "11": {"name": "punctuation.section.regexp.json.comments.snippets support.type.built-in.json.comments.snippets variable.language.special.transform.json.comments.snippets"}, "12": {"name": "support.type.built-in.json.comments.snippets variable.language.special.transform.json.comments.snippets"}, "13": {"name": "punctuation.section.insertion.bracket.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.section.insertion.bracket.json.comments.snippets"}, "14": {"name": "meta.insertion.brackets.json.comments.snippets meta.insertion.format.plus.json.comments.snippets"}, "15": {"name": "punctuation.section.insertion.dollar.brackets.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.section.insertion.dollar.brackets.json.comments.snippets"}, "16": {"name": "punctuation.section.insertion.bracket.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.section.insertion.bracket.json.comments.snippets"}, "17": {"name": "variable.other.normal.json.comments.snippets custom.variable.other.normal.numeric.json.comments.snippets"}, "18": {"name": "punctuation.section.insertion.json.comments.snippets punctuation.separator.colon.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.separator.colon.json.comments.snippets"}, "19": {"name": "punctuation.separator.plus.json.comments.snippets"}, "20": {"patterns": [{"include": "#special_variables"}, {"include": "#simple_escape_context"}]}, "21": {"patterns": [{"include": "#quad_backslash_match"}, {"include": "#dollar_sign_escape"}, {"include": "#bracket_escape"}, {"include": "#basic_escape"}, {"include": "#invalid_escape"}, {"include": "#normal_characters"}]}, "22": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\$", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}, {"include": "#invalid_escape"}]}, "23": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\}", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}]}, "24": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}, "25": {"name": "constant.character.escape.json.comments.snippets"}, "26": {"name": "constant.character.escape.json.comments.snippets"}, "27": {"name": "constant.character.escape.json.comments.snippets invalid.illegal.unrecognized-string-escape.json.comments.snippets"}, "28": {"name": "string.quoted.double.json.comments.snippets"}, "29": {"patterns": [{"include": "#quad_backslash_match"}, {"include": "#dollar_sign_escape"}, {"include": "#bracket_escape"}, {"include": "#basic_escape"}, {"include": "#invalid_escape"}, {"include": "#normal_characters"}]}, "30": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\$", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}, {"include": "#invalid_escape"}]}, "31": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\}", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}]}, "32": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}, "33": {"name": "constant.character.escape.json.comments.snippets"}, "34": {"name": "constant.character.escape.json.comments.snippets"}, "35": {"name": "constant.character.escape.json.comments.snippets invalid.illegal.unrecognized-string-escape.json.comments.snippets"}, "36": {"name": "string.quoted.double.json.comments.snippets"}, "37": {"name": "punctuation.section.insertion.bracket.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.section.insertion.bracket.json.comments.snippets"}, "38": {"name": "meta.insertion.brackets.json.comments.snippets meta.insertion.format.conditional.json.comments.snippets"}, "39": {"name": "punctuation.section.insertion.dollar.brackets.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.section.insertion.dollar.brackets.json.comments.snippets"}, "40": {"name": "punctuation.section.insertion.bracket.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.section.insertion.bracket.json.comments.snippets"}, "41": {"name": "variable.other.normal.json.comments.snippets custom.variable.other.normal.numeric.json.comments.snippets"}, "42": {"name": "punctuation.section.insertion.json.comments.snippets punctuation.separator.colon.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.separator.colon.json.comments.snippets"}, "43": {"name": "punctuation.separator.conditional.json.comments.snippets keyword.operator.ternary.json.comments.snippets"}, "44": {"patterns": [{"include": "#special_variables"}, {"include": "#simple_escape_context"}]}, "45": {"patterns": [{"include": "#quad_backslash_match"}, {"include": "#dollar_sign_escape"}, {"include": "#bracket_escape"}, {"include": "#basic_escape"}, {"include": "#invalid_escape"}, {"include": "#normal_characters"}]}, "46": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\$", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}, {"include": "#invalid_escape"}]}, "47": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\}", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}]}, "48": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}, "49": {"name": "constant.character.escape.json.comments.snippets"}, "50": {"name": "constant.character.escape.json.comments.snippets"}, "51": {"name": "constant.character.escape.json.comments.snippets invalid.illegal.unrecognized-string-escape.json.comments.snippets"}, "52": {"name": "string.quoted.double.json.comments.snippets"}, "53": {"patterns": [{"include": "#quad_backslash_match"}, {"include": "#dollar_sign_escape"}, {"include": "#bracket_escape"}, {"include": "#basic_escape"}, {"include": "#invalid_escape"}, {"include": "#normal_characters"}]}, "54": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\$", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}, {"include": "#invalid_escape"}]}, "55": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\}", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}]}, "56": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}, "57": {"name": "constant.character.escape.json.comments.snippets"}, "58": {"name": "constant.character.escape.json.comments.snippets"}, "59": {"name": "constant.character.escape.json.comments.snippets invalid.illegal.unrecognized-string-escape.json.comments.snippets"}, "60": {"name": "string.quoted.double.json.comments.snippets"}, "61": {"name": "keyword.operator.ternary.json.comments.snippets"}, "62": {"patterns": [{"include": "#special_variables"}, {"include": "#simple_escape_context"}]}, "63": {"patterns": [{"include": "#quad_backslash_match"}, {"include": "#dollar_sign_escape"}, {"include": "#bracket_escape"}, {"include": "#basic_escape"}, {"include": "#invalid_escape"}, {"include": "#normal_characters"}]}, "64": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\$", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}, {"include": "#invalid_escape"}]}, "65": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\}", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}]}, "66": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}, "67": {"name": "constant.character.escape.json.comments.snippets"}, "68": {"name": "constant.character.escape.json.comments.snippets"}, "69": {"name": "constant.character.escape.json.comments.snippets invalid.illegal.unrecognized-string-escape.json.comments.snippets"}, "70": {"name": "string.quoted.double.json.comments.snippets"}, "71": {"patterns": [{"include": "#quad_backslash_match"}, {"include": "#dollar_sign_escape"}, {"include": "#bracket_escape"}, {"include": "#basic_escape"}, {"include": "#invalid_escape"}, {"include": "#normal_characters"}]}, "72": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\$", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}, {"include": "#invalid_escape"}]}, "73": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\}", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}]}, "74": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}, "75": {"name": "constant.character.escape.json.comments.snippets"}, "76": {"name": "constant.character.escape.json.comments.snippets"}, "77": {"name": "constant.character.escape.json.comments.snippets invalid.illegal.unrecognized-string-escape.json.comments.snippets"}, "78": {"name": "string.quoted.double.json.comments.snippets"}, "79": {"name": "punctuation.section.insertion.bracket.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.section.insertion.bracket.json.comments.snippets"}, "80": {"name": "meta.insertion.brackets.json.comments.snippets meta.insertion.format.remove.json.comments.snippets"}, "81": {"name": "punctuation.section.insertion.dollar.brackets.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.section.insertion.dollar.brackets.json.comments.snippets"}, "82": {"name": "punctuation.section.insertion.bracket.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.section.insertion.bracket.json.comments.snippets"}, "83": {"name": "variable.other.normal.json.comments.snippets custom.variable.other.normal.numeric.json.comments.snippets"}, "84": {"name": "punctuation.section.insertion.json.comments.snippets punctuation.separator.colon.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.separator.colon.json.comments.snippets"}, "85": {"name": "punctuation.separator.dash.json.comments.snippets"}, "86": {"patterns": [{"include": "#special_variables"}, {"include": "#simple_escape_context"}]}, "87": {"patterns": [{"include": "#quad_backslash_match"}, {"include": "#dollar_sign_escape"}, {"include": "#bracket_escape"}, {"include": "#basic_escape"}, {"include": "#invalid_escape"}, {"include": "#normal_characters"}]}, "88": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\$", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}, {"include": "#invalid_escape"}]}, "89": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\}", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}]}, "90": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}, "91": {"name": "constant.character.escape.json.comments.snippets"}, "92": {"name": "constant.character.escape.json.comments.snippets"}, "93": {"name": "constant.character.escape.json.comments.snippets invalid.illegal.unrecognized-string-escape.json.comments.snippets"}, "94": {"name": "string.quoted.double.json.comments.snippets"}, "95": {"patterns": [{"include": "#quad_backslash_match"}, {"include": "#dollar_sign_escape"}, {"include": "#bracket_escape"}, {"include": "#basic_escape"}, {"include": "#invalid_escape"}, {"include": "#normal_characters"}]}, "96": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\$", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}, {"include": "#invalid_escape"}]}, "97": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\}", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}]}, "98": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}, "99": {"name": "constant.character.escape.json.comments.snippets"}, "100": {"name": "constant.character.escape.json.comments.snippets"}, "101": {"name": "constant.character.escape.json.comments.snippets invalid.illegal.unrecognized-string-escape.json.comments.snippets"}, "102": {"name": "string.quoted.double.json.comments.snippets"}, "103": {"name": "punctuation.section.insertion.bracket.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.section.insertion.bracket.json.comments.snippets"}, "104": {"name": "meta.insertion.brackets.json.comments.snippets meta.insertion.format.default.json.comments.snippets"}, "105": {"name": "punctuation.section.insertion.dollar.brackets.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.section.insertion.dollar.brackets.json.comments.snippets"}, "106": {"name": "punctuation.section.insertion.bracket.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.section.insertion.bracket.json.comments.snippets"}, "107": {"name": "variable.other.normal.json.comments.snippets custom.variable.other.normal.numeric.json.comments.snippets"}, "108": {"name": "punctuation.section.insertion.json.comments.snippets punctuation.separator.colon.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.separator.colon.json.comments.snippets"}, "109": {"patterns": [{"include": "#special_variables"}, {"include": "#simple_escape_context"}]}, "110": {"patterns": [{"include": "#quad_backslash_match"}, {"include": "#dollar_sign_escape"}, {"include": "#bracket_escape"}, {"include": "#basic_escape"}, {"include": "#invalid_escape"}, {"include": "#normal_characters"}]}, "111": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\$", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}, {"include": "#invalid_escape"}]}, "112": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\}", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}]}, "113": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}, "114": {"name": "constant.character.escape.json.comments.snippets"}, "115": {"name": "constant.character.escape.json.comments.snippets"}, "116": {"name": "constant.character.escape.json.comments.snippets invalid.illegal.unrecognized-string-escape.json.comments.snippets"}, "117": {"name": "string.quoted.double.json.comments.snippets"}, "118": {"patterns": [{"include": "#quad_backslash_match"}, {"include": "#dollar_sign_escape"}, {"include": "#bracket_escape"}, {"include": "#basic_escape"}, {"include": "#invalid_escape"}, {"include": "#normal_characters"}]}, "119": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\$", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}, {"include": "#invalid_escape"}]}, "120": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\}", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}]}, "121": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}, "122": {"name": "constant.character.escape.json.comments.snippets"}, "123": {"name": "constant.character.escape.json.comments.snippets"}, "124": {"name": "constant.character.escape.json.comments.snippets invalid.illegal.unrecognized-string-escape.json.comments.snippets"}, "125": {"name": "string.quoted.double.json.comments.snippets"}, "126": {"name": "punctuation.section.insertion.bracket.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.section.insertion.bracket.json.comments.snippets"}, "127": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}, "128": {"name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}, "129": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}, "130": {"name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}, "131": {"patterns": [{"include": "#special_variables"}, {"include": "#simple_escape_context"}]}, "132": {"patterns": [{"include": "#quad_backslash_match"}, {"include": "#dollar_sign_escape"}, {"include": "#bracket_escape"}, {"include": "#basic_escape"}, {"include": "#invalid_escape"}, {"include": "#normal_characters"}]}, "133": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\$", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}, {"include": "#invalid_escape"}]}, "134": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\}", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}]}, "135": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}, "136": {"name": "constant.character.escape.json.comments.snippets"}, "137": {"name": "constant.character.escape.json.comments.snippets"}, "138": {"name": "constant.character.escape.json.comments.snippets invalid.illegal.unrecognized-string-escape.json.comments.snippets"}, "139": {"name": "string.quoted.double.json.comments.snippets"}}}]}, "24": {"name": "punctuation.section.regexp.json.comments.snippets"}, "25": {"name": "keyword.other.flag.json.comments.snippets"}, "26": {"name": "punctuation.section.insertion.bracket.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.section.insertion.bracket.json.comments.snippets"}, "27": {"name": "meta.insertion.brackets.json.comments.snippets meta.insertion.choice.json.comments.snippets"}, "28": {"name": "punctuation.section.insertion.dollar.brackets.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.section.insertion.dollar.brackets.json.comments.snippets"}, "29": {"name": "punctuation.section.insertion.bracket.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.section.insertion.bracket.json.comments.snippets"}, "30": {"name": "variable.other.normal.json.comments.snippets custom.variable.other.normal.numeric.json.comments.snippets"}, "31": {"name": "punctuation.separator.choice.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.separator.choice.json.comments.snippets"}, "32": {"patterns": [{"match": ",", "name": "meta.insertion.choice.json.comments.snippets punctuation.separator.comma.json.comments.snippets"}, {"include": "#choice_option"}]}, "33": {"name": "meta.insertion.choice.json.comments.snippets constant.other.option.json.comments.snippets"}, "34": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}, "35": {"name": "constant.character.escape.json.comments.snippets"}, "36": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}, "37": {"patterns": [{"include": "#quad_backslash_match"}, {"include": "#dollar_sign_escape"}, {"include": "#bracket_escape"}, {"include": "#basic_escape"}, {"include": "#invalid_escape"}, {"include": "#normal_characters"}]}, "38": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\$", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}, {"include": "#invalid_escape"}]}, "39": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\}", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}]}, "40": {"name": "constant.character.escape.json.comments.snippets"}, "41": {"name": "punctuation.separator.choice.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.separator.choice.json.comments.snippets"}, "42": {"name": "punctuation.section.insertion.bracket.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.section.insertion.bracket.json.comments.snippets"}, "43": {"name": "meta.insertion.brackets.json.comments.snippets meta.insertion.placeholder.json.comments.snippets", "patterns": [{"match": "(?:(\\$)(\\{))([0-9]+)(:)(.+)", "captures": {"1": {"name": "punctuation.section.insertion.dollar.brackets.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.section.insertion.dollar.brackets.json.comments.snippets"}, "2": {"name": "punctuation.section.insertion.bracket.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.section.insertion.bracket.json.comments.snippets"}, "3": {"name": "variable.other.normal.json.comments.snippets custom.variable.other.normal.numeric.json.comments.snippets"}, "4": {"name": "punctuation.section.insertion.json.comments.snippets punctuation.separator.colon.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.separator.colon.json.comments.snippets"}, "5": {"patterns": [{"include": "#bracket_insertion_ender"}, {"include": "#bnf_any"}]}}}]}, "44": {"name": "punctuation.section.insertion.dollar.brackets.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.section.insertion.dollar.brackets.json.comments.snippets"}, "45": {"name": "punctuation.section.insertion.bracket.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.section.insertion.bracket.json.comments.snippets"}, "46": {"name": "variable.other.normal.json.comments.snippets custom.variable.other.normal.numeric.json.comments.snippets"}, "47": {"name": "punctuation.section.insertion.json.comments.snippets punctuation.separator.colon.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.separator.colon.json.comments.snippets"}, "48": {"patterns": [{"include": "source.syntax.regexp.tmLanguage"}, {"match": "(?:(?:(\\\\)(\\\\\\/))|(?:(\\\\\\\\\\\\)(\\\\\\/)))", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}, "2": {"name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}, "3": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}, "4": {"name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}}}, {"include": "#simple_escape_context"}]}, "49": {"patterns": [{"match": "\\$\\d+", "name": "variable.language.capture.json.comments.snippets"}, {"match": "\\$\\{\\d+\\}", "name": "variable.language.capture.json.comments.snippets"}, {"include": "#bnf_format"}, {"include": "#regex_backslash_escape"}, {"include": "#bnf_text"}]}, "50": {"patterns": [{"include": "#special_variables"}, {"include": "#simple_escape_context"}]}, "51": {"patterns": [{"include": "#quad_backslash_match"}, {"include": "#dollar_sign_escape"}, {"include": "#bracket_escape"}, {"include": "#basic_escape"}, {"include": "#invalid_escape"}, {"include": "#normal_characters"}]}, "52": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\$", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}, {"include": "#invalid_escape"}]}, "53": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\}", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}]}, "54": {"patterns": [{"include": "#quad_backslash_match"}, {"include": "#dollar_sign_escape"}, {"include": "#bracket_escape"}, {"include": "#basic_escape"}, {"include": "#invalid_escape"}, {"include": "#normal_characters"}]}, "55": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\$", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}, {"include": "#invalid_escape"}]}, "56": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\}", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}]}, "57": {"patterns": [{"include": "#special_variables"}, {"include": "#simple_escape_context"}]}, "58": {"patterns": [{"include": "#quad_backslash_match"}, {"include": "#dollar_sign_escape"}, {"include": "#bracket_escape"}, {"include": "#basic_escape"}, {"include": "#invalid_escape"}, {"include": "#normal_characters"}]}, "59": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\$", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}, {"include": "#invalid_escape"}]}, "60": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\}", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}]}, "61": {"patterns": [{"include": "#quad_backslash_match"}, {"include": "#dollar_sign_escape"}, {"include": "#bracket_escape"}, {"include": "#basic_escape"}, {"include": "#invalid_escape"}, {"include": "#normal_characters"}]}, "62": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\$", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}, {"include": "#invalid_escape"}]}, "63": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\}", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}]}, "64": {"patterns": [{"include": "#special_variables"}, {"include": "#simple_escape_context"}]}, "65": {"patterns": [{"include": "#quad_backslash_match"}, {"include": "#dollar_sign_escape"}, {"include": "#bracket_escape"}, {"include": "#basic_escape"}, {"include": "#invalid_escape"}, {"include": "#normal_characters"}]}, "66": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\$", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}, {"include": "#invalid_escape"}]}, "67": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\}", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}]}, "68": {"patterns": [{"include": "#quad_backslash_match"}, {"include": "#dollar_sign_escape"}, {"include": "#bracket_escape"}, {"include": "#basic_escape"}, {"include": "#invalid_escape"}, {"include": "#normal_characters"}]}, "69": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\$", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}, {"include": "#invalid_escape"}]}, "70": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\}", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}]}, "71": {"patterns": [{"include": "#special_variables"}, {"include": "#simple_escape_context"}]}, "72": {"patterns": [{"include": "#quad_backslash_match"}, {"include": "#dollar_sign_escape"}, {"include": "#bracket_escape"}, {"include": "#basic_escape"}, {"include": "#invalid_escape"}, {"include": "#normal_characters"}]}, "73": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\$", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}, {"include": "#invalid_escape"}]}, "74": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\}", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}]}, "75": {"patterns": [{"include": "#quad_backslash_match"}, {"include": "#dollar_sign_escape"}, {"include": "#bracket_escape"}, {"include": "#basic_escape"}, {"include": "#invalid_escape"}, {"include": "#normal_characters"}]}, "76": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\$", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}, {"include": "#invalid_escape"}]}, "77": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\}", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}]}, "78": {"patterns": [{"include": "#special_variables"}, {"include": "#simple_escape_context"}]}, "79": {"patterns": [{"include": "#quad_backslash_match"}, {"include": "#dollar_sign_escape"}, {"include": "#bracket_escape"}, {"include": "#basic_escape"}, {"include": "#invalid_escape"}, {"include": "#normal_characters"}]}, "80": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\$", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}, {"include": "#invalid_escape"}]}, "81": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\}", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}]}, "82": {"patterns": [{"include": "#quad_backslash_match"}, {"include": "#dollar_sign_escape"}, {"include": "#bracket_escape"}, {"include": "#basic_escape"}, {"include": "#invalid_escape"}, {"include": "#normal_characters"}]}, "83": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\$", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}, {"include": "#invalid_escape"}]}, "84": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\}", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}]}, "85": {"patterns": [{"include": "#special_variables"}, {"include": "#simple_escape_context"}]}, "86": {"patterns": [{"include": "#quad_backslash_match"}, {"include": "#dollar_sign_escape"}, {"include": "#bracket_escape"}, {"include": "#basic_escape"}, {"include": "#invalid_escape"}, {"include": "#normal_characters"}]}, "87": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\$", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}, {"include": "#invalid_escape"}]}, "88": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\}", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}]}, "89": {"name": "punctuation.section.insertion.bracket.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.section.insertion.bracket.json.comments.snippets"}, "90": {"name": "meta.insertion.simple.json.comments.snippets meta.insertion.variable.simple.json.comments.snippets"}, "91": {"name": "punctuation.section.insertion.dollar.simple.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.section.insertion.dollar.simple.json.comments.snippets"}, "92": {"name": "variable.other.normal.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.variable.other.normal.named.json.comments.snippets"}, "93": {"name": "meta.insertion.brackets.json.comments.snippets meta.insertion.variable.bracket.json.comments.snippets"}, "94": {"name": "punctuation.section.insertion.dollar.brackets.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.section.insertion.dollar.brackets.json.comments.snippets"}, "95": {"name": "punctuation.section.insertion.bracket.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.section.insertion.bracket.json.comments.snippets"}, "96": {"name": "variable.other.normal.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.variable.other.normal.named.json.comments.snippets"}, "97": {"name": "punctuation.section.insertion.bracket.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.section.insertion.bracket.json.comments.snippets"}, "98": {"name": "meta.insertion.brackets.json.comments.snippets meta.insertion.variable.any.json.comments.snippets"}, "99": {"name": "punctuation.section.insertion.dollar.brackets.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.section.insertion.dollar.brackets.json.comments.snippets"}, "100": {"name": "punctuation.section.insertion.bracket.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.section.insertion.bracket.json.comments.snippets"}, "101": {"name": "variable.other.normal.json.comments.snippets custom.variable.other.normal.named.json.comments.snippets"}, "102": {"name": "punctuation.section.insertion.json.comments.snippets punctuation.separator.colon.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.separator.colon.json.comments.snippets"}, "103": {"patterns": [{"include": "#bnf_any"}]}, "104": {"name": "punctuation.section.insertion.bracket.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.section.insertion.bracket.json.comments.snippets"}, "105": {"name": "meta.insertion.brackets.json.comments.snippets meta.insertion.variable.transform.json.comments.snippets"}, "106": {"name": "punctuation.section.insertion.dollar.brackets.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.section.insertion.dollar.brackets.json.comments.snippets"}, "107": {"name": "punctuation.section.insertion.bracket.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.section.insertion.bracket.json.comments.snippets"}, "108": {"name": "variable.other.normal.json.comments.snippets custom.variable.other.normal.named.json.comments.snippets"}, "109": {"name": "meta.insertion.variable.json.comments.snippets", "patterns": [{"include": "#bnf_transform"}]}, "110": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}, "111": {"name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}, "112": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}, "113": {"name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}, "114": {"name": "meta.insertion.transform.json.comments.snippets string.regexp.json.comments.snippets"}, "115": {"name": "punctuation.section.regexp.json.comments.snippets"}, "116": {"patterns": [{"include": "source.syntax.regexp.tmLanguage"}, {"match": "(?:(?:(\\\\)(\\\\\\/))|(?:(\\\\\\\\\\\\)(\\\\\\/)))", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}, "2": {"name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}, "3": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}, "4": {"name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}}}, {"include": "#simple_escape_context"}]}, "117": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}, "118": {"name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}, "119": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}, "120": {"name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}, "121": {"name": "punctuation.section.regexp.json.comments.snippets"}, "122": {"patterns": [{"match": "\\$\\d+", "name": "variable.language.capture.json.comments.snippets"}, {"match": "\\$\\{\\d+\\}", "name": "variable.language.capture.json.comments.snippets"}, {"include": "#bnf_format"}, {"include": "#regex_backslash_escape"}, {"include": "#bnf_text"}]}, "123": {"patterns": [{"match": "(?:(?:(?:(?:(?:(?:(?:(?:(\\$)((?<!\\w)(?:TM_SELECTED_TEXT|TM_CURRENT_LINE|TM_CURRENT_WORD|TM_LINE_INDEX|TM_LINE_NUMBER|TM_FILENAME|TM_FILENAME_BASE|TM_DIRECTORY|TM_FILEPATH|RELATIVE_FILEPATH|CLIPBOARD|WORKSPACE_NAME|WORKSPACE_FOLDER|CURSOR_INDEX|CURSOR_NUMBER|CURRENT_YEAR|CURRENT_YEAR_SHORT|CURRENT_MONTH|CURRENT_MONTH_NAME|CURRENT_MONTH_NAME_SHORT|CURRENT_DATE|CURRENT_DAY_NAME|CURRENT_DAY_NAME_SHORT|CURRENT_HOUR|CURRENT_MINUTE|CURRENT_SECOND|CURRENT_SECONDS_UNIX|CURRENT_TIMEZONE_OFFSET|RANDOM|RANDOM_HEX|UUID|BLOCK_COMMENT_START|BLOCK_COMMENT_END|LINE_COMMENT)(?!\\w)))|((?:(\\$)([0-9]+))))|((?:(?:(\\$)(\\{))([0-9]+)(:)(?:(\\/)(upcase|downcase|capitalize|camelcase|pascalcase))(\\}))))|((?:(?:(\\$)(\\{))([0-9]+)(:)(\\+)((?:(?:((?!\\\\)(?:(?:(?:\\\\\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:[^\\{\\$\"\\\\]|(?=\")))|(?:(?:(?:\\\\\\\\\\\\\\\\)+)(?:[^\\{\\$\"\\\\]|(?=\")))))|((?<!\\\\)(?:(?:(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\$))|(?:\\\\\\$)))|((?<!\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\}))|(?:(\\\\\\\\)(\\\\\\\\))|(\\\\(?:[\"\\\\\\/bfnrt]|(?:u[0-9a-fA-F]{4})))|((?:\\\\.))|([^\\\\\\n\\}\"]))?)(?:(?:((?!\\\\)(?:(?:(?:\\\\\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:[^\\{\\$\"\\\\]|(?=\")))|(?:(?:(?:\\\\\\\\\\\\\\\\)+)(?:[^\\{\\$\"\\\\]|(?=\")))))|((?<!\\\\)(?:(?:(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\$))|(?:\\\\\\$)))|((?<!\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\}))|(?:(\\\\\\\\)(\\\\\\\\))|(\\\\(?:[\"\\\\\\/bfnrt]|(?:u[0-9a-fA-F]{4})))|((?:\\\\.))|([^\\\\\\n\\}\"]))*?))(\\}))))|((?:(?:(\\$)(\\{))([0-9]+)(:)(\\?)((?:(?:((?!\\\\)(?:(?:(?:\\\\\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:[^\\{\\$\"\\\\]|(?=\")))|(?:(?:(?:\\\\\\\\\\\\\\\\)+)(?:[^\\{\\$\"\\\\]|(?=\")))))|((?<!\\\\)(?:(?:(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\$))|(?:\\\\\\$)))|((?<!\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\}))|(?:(\\\\\\\\)(\\\\\\\\))|(\\\\(?:[\"\\\\\\/bfnrt]|(?:u[0-9a-fA-F]{4})))|((?:\\\\.))|([^\\\\\\n\\}\"]))?)(?:(?:((?!\\\\)(?:(?:(?:\\\\\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:[^\\{\\$\"\\\\]|(?=\")))|(?:(?:(?:\\\\\\\\\\\\\\\\)+)(?:[^\\{\\$\"\\\\]|(?=\")))))|((?<!\\\\)(?:(?:(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\$))|(?:\\\\\\$)))|((?<!\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\}))|(?:(\\\\\\\\)(\\\\\\\\))|(\\\\(?:[\"\\\\\\/bfnrt]|(?:u[0-9a-fA-F]{4})))|((?:\\\\.))|([^\\\\\\n\\}\"]))*?))(:)((?:(?:((?!\\\\)(?:(?:(?:\\\\\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:[^\\{\\$\"\\\\]|(?=\")))|(?:(?:(?:\\\\\\\\\\\\\\\\)+)(?:[^\\{\\$\"\\\\]|(?=\")))))|((?<!\\\\)(?:(?:(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\$))|(?:\\\\\\$)))|((?<!\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\}))|(?:(\\\\\\\\)(\\\\\\\\))|(\\\\(?:[\"\\\\\\/bfnrt]|(?:u[0-9a-fA-F]{4})))|((?:\\\\.))|([^\\\\\\n\\}\"]))?)(?:(?:((?!\\\\)(?:(?:(?:\\\\\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:[^\\{\\$\"\\\\]|(?=\")))|(?:(?:(?:\\\\\\\\\\\\\\\\)+)(?:[^\\{\\$\"\\\\]|(?=\")))))|((?<!\\\\)(?:(?:(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\$))|(?:\\\\\\$)))|((?<!\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\}))|(?:(\\\\\\\\)(\\\\\\\\))|(\\\\(?:[\"\\\\\\/bfnrt]|(?:u[0-9a-fA-F]{4})))|((?:\\\\.))|([^\\\\\\n\\}\"]))*?))(\\}))))|((?:(?:(\\$)(\\{))([0-9]+)(:)(\\-)((?:(?:((?!\\\\)(?:(?:(?:\\\\\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:[^\\{\\$\"\\\\]|(?=\")))|(?:(?:(?:\\\\\\\\\\\\\\\\)+)(?:[^\\{\\$\"\\\\]|(?=\")))))|((?<!\\\\)(?:(?:(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\$))|(?:\\\\\\$)))|((?<!\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\}))|(?:(\\\\\\\\)(\\\\\\\\))|(\\\\(?:[\"\\\\\\/bfnrt]|(?:u[0-9a-fA-F]{4})))|((?:\\\\.))|([^\\\\\\n\\}\"]))?)(?:(?:((?!\\\\)(?:(?:(?:\\\\\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:[^\\{\\$\"\\\\]|(?=\")))|(?:(?:(?:\\\\\\\\\\\\\\\\)+)(?:[^\\{\\$\"\\\\]|(?=\")))))|((?<!\\\\)(?:(?:(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\$))|(?:\\\\\\$)))|((?<!\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\}))|(?:(\\\\\\\\)(\\\\\\\\))|(\\\\(?:[\"\\\\\\/bfnrt]|(?:u[0-9a-fA-F]{4})))|((?:\\\\.))|([^\\\\\\n\\}\"]))*?))(\\}))))|((?:(?:(\\$)(\\{))([0-9]+)(:)((?:(?:((?!\\\\)(?:(?:(?:\\\\\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:[^\\{\\$\"\\\\]|(?=\")))|(?:(?:(?:\\\\\\\\\\\\\\\\)+)(?:[^\\{\\$\"\\\\]|(?=\")))))|((?<!\\\\)(?:(?:(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\$))|(?:\\\\\\$)))|((?<!\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\}))|(?:(\\\\\\\\)(\\\\\\\\))|(\\\\(?:[\"\\\\\\/bfnrt]|(?:u[0-9a-fA-F]{4})))|((?:\\\\.))|([^\\\\\\n\\}\"]))?)(?:(?:((?!\\\\)(?:(?:(?:\\\\\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:[^\\{\\$\"\\\\]|(?=\")))|(?:(?:(?:\\\\\\\\\\\\\\\\)+)(?:[^\\{\\$\"\\\\]|(?=\")))))|((?<!\\\\)(?:(?:(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\$))|(?:\\\\\\$)))|((?<!\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\}))|(?:(\\\\\\\\)(\\\\\\\\))|(\\\\(?:[\"\\\\\\/bfnrt]|(?:u[0-9a-fA-F]{4})))|((?:\\\\.))|([^\\\\\\n\\}\"]))*?))(\\}))))|(?:(?:(?:(\\\\)(\\\\\\/))|(?:(\\\\\\\\\\\\)(\\\\\\/)))|((?:(?:(?:((?!\\\\)(?:(?:(?:\\\\\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:[^\\{\\$\"\\\\]|(?=\")))|(?:(?:(?:\\\\\\\\\\\\\\\\)+)(?:[^\\{\\$\"\\\\]|(?=\")))))|((?<!\\\\)(?:(?:(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\$))|(?:\\\\\\$)))|((?<!\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\}))|(?:(\\\\\\\\)(\\\\\\\\))|(\\\\(?:[\"\\\\\\/bfnrt]|(?:u[0-9a-fA-F]{4})))|((?:\\\\.))|([^\\\\\\n\\}\"]))|[^\\n\\r])*))))", "captures": {"1": {"name": "meta.insertion.simple.json.comments.snippets punctuation.section.insertion.dollar.simple.json.comments.snippets keyword.operator.insertion.json.comments.snippets variable.language.this.json.comments.snippets"}, "2": {"name": "meta.insertion.simple.json.comments.snippets keyword.operator.insertion.json.comments.snippets variable.language.this.json.comments.snippets"}, "3": {"name": "meta.insertion.simple.numeric.json.comments.snippets meta.insertion.format.simple.json.comments.snippets"}, "4": {"name": "punctuation.section.insertion.dollar.simple.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.section.insertion.dollar.simple.json.comments.snippets"}, "5": {"name": "variable.other.normal.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.variable.other.normal.numeric.json.comments.snippets"}, "6": {"name": "meta.insertion.brackets.json.comments.snippets meta.insertion.format.transform.json.comments.snippets"}, "7": {"name": "punctuation.section.insertion.dollar.brackets.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.section.insertion.dollar.brackets.json.comments.snippets"}, "8": {"name": "punctuation.section.insertion.bracket.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.section.insertion.bracket.json.comments.snippets"}, "9": {"name": "variable.other.normal.json.comments.snippets custom.variable.other.normal.numeric.json.comments.snippets"}, "10": {"name": "punctuation.section.insertion.json.comments.snippets punctuation.separator.colon.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.separator.colon.json.comments.snippets"}, "11": {"name": "punctuation.section.regexp.json.comments.snippets support.type.built-in.json.comments.snippets variable.language.special.transform.json.comments.snippets"}, "12": {"name": "support.type.built-in.json.comments.snippets variable.language.special.transform.json.comments.snippets"}, "13": {"name": "punctuation.section.insertion.bracket.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.section.insertion.bracket.json.comments.snippets"}, "14": {"name": "meta.insertion.brackets.json.comments.snippets meta.insertion.format.plus.json.comments.snippets"}, "15": {"name": "punctuation.section.insertion.dollar.brackets.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.section.insertion.dollar.brackets.json.comments.snippets"}, "16": {"name": "punctuation.section.insertion.bracket.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.section.insertion.bracket.json.comments.snippets"}, "17": {"name": "variable.other.normal.json.comments.snippets custom.variable.other.normal.numeric.json.comments.snippets"}, "18": {"name": "punctuation.section.insertion.json.comments.snippets punctuation.separator.colon.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.separator.colon.json.comments.snippets"}, "19": {"name": "punctuation.separator.plus.json.comments.snippets"}, "20": {"patterns": [{"include": "#special_variables"}, {"include": "#simple_escape_context"}]}, "21": {"patterns": [{"include": "#quad_backslash_match"}, {"include": "#dollar_sign_escape"}, {"include": "#bracket_escape"}, {"include": "#basic_escape"}, {"include": "#invalid_escape"}, {"include": "#normal_characters"}]}, "22": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\$", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}, {"include": "#invalid_escape"}]}, "23": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\}", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}]}, "24": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}, "25": {"name": "constant.character.escape.json.comments.snippets"}, "26": {"name": "constant.character.escape.json.comments.snippets"}, "27": {"name": "constant.character.escape.json.comments.snippets invalid.illegal.unrecognized-string-escape.json.comments.snippets"}, "28": {"name": "string.quoted.double.json.comments.snippets"}, "29": {"patterns": [{"include": "#quad_backslash_match"}, {"include": "#dollar_sign_escape"}, {"include": "#bracket_escape"}, {"include": "#basic_escape"}, {"include": "#invalid_escape"}, {"include": "#normal_characters"}]}, "30": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\$", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}, {"include": "#invalid_escape"}]}, "31": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\}", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}]}, "32": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}, "33": {"name": "constant.character.escape.json.comments.snippets"}, "34": {"name": "constant.character.escape.json.comments.snippets"}, "35": {"name": "constant.character.escape.json.comments.snippets invalid.illegal.unrecognized-string-escape.json.comments.snippets"}, "36": {"name": "string.quoted.double.json.comments.snippets"}, "37": {"name": "punctuation.section.insertion.bracket.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.section.insertion.bracket.json.comments.snippets"}, "38": {"name": "meta.insertion.brackets.json.comments.snippets meta.insertion.format.conditional.json.comments.snippets"}, "39": {"name": "punctuation.section.insertion.dollar.brackets.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.section.insertion.dollar.brackets.json.comments.snippets"}, "40": {"name": "punctuation.section.insertion.bracket.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.section.insertion.bracket.json.comments.snippets"}, "41": {"name": "variable.other.normal.json.comments.snippets custom.variable.other.normal.numeric.json.comments.snippets"}, "42": {"name": "punctuation.section.insertion.json.comments.snippets punctuation.separator.colon.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.separator.colon.json.comments.snippets"}, "43": {"name": "punctuation.separator.conditional.json.comments.snippets keyword.operator.ternary.json.comments.snippets"}, "44": {"patterns": [{"include": "#special_variables"}, {"include": "#simple_escape_context"}]}, "45": {"patterns": [{"include": "#quad_backslash_match"}, {"include": "#dollar_sign_escape"}, {"include": "#bracket_escape"}, {"include": "#basic_escape"}, {"include": "#invalid_escape"}, {"include": "#normal_characters"}]}, "46": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\$", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}, {"include": "#invalid_escape"}]}, "47": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\}", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}]}, "48": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}, "49": {"name": "constant.character.escape.json.comments.snippets"}, "50": {"name": "constant.character.escape.json.comments.snippets"}, "51": {"name": "constant.character.escape.json.comments.snippets invalid.illegal.unrecognized-string-escape.json.comments.snippets"}, "52": {"name": "string.quoted.double.json.comments.snippets"}, "53": {"patterns": [{"include": "#quad_backslash_match"}, {"include": "#dollar_sign_escape"}, {"include": "#bracket_escape"}, {"include": "#basic_escape"}, {"include": "#invalid_escape"}, {"include": "#normal_characters"}]}, "54": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\$", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}, {"include": "#invalid_escape"}]}, "55": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\}", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}]}, "56": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}, "57": {"name": "constant.character.escape.json.comments.snippets"}, "58": {"name": "constant.character.escape.json.comments.snippets"}, "59": {"name": "constant.character.escape.json.comments.snippets invalid.illegal.unrecognized-string-escape.json.comments.snippets"}, "60": {"name": "string.quoted.double.json.comments.snippets"}, "61": {"name": "keyword.operator.ternary.json.comments.snippets"}, "62": {"patterns": [{"include": "#special_variables"}, {"include": "#simple_escape_context"}]}, "63": {"patterns": [{"include": "#quad_backslash_match"}, {"include": "#dollar_sign_escape"}, {"include": "#bracket_escape"}, {"include": "#basic_escape"}, {"include": "#invalid_escape"}, {"include": "#normal_characters"}]}, "64": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\$", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}, {"include": "#invalid_escape"}]}, "65": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\}", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}]}, "66": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}, "67": {"name": "constant.character.escape.json.comments.snippets"}, "68": {"name": "constant.character.escape.json.comments.snippets"}, "69": {"name": "constant.character.escape.json.comments.snippets invalid.illegal.unrecognized-string-escape.json.comments.snippets"}, "70": {"name": "string.quoted.double.json.comments.snippets"}, "71": {"patterns": [{"include": "#quad_backslash_match"}, {"include": "#dollar_sign_escape"}, {"include": "#bracket_escape"}, {"include": "#basic_escape"}, {"include": "#invalid_escape"}, {"include": "#normal_characters"}]}, "72": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\$", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}, {"include": "#invalid_escape"}]}, "73": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\}", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}]}, "74": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}, "75": {"name": "constant.character.escape.json.comments.snippets"}, "76": {"name": "constant.character.escape.json.comments.snippets"}, "77": {"name": "constant.character.escape.json.comments.snippets invalid.illegal.unrecognized-string-escape.json.comments.snippets"}, "78": {"name": "string.quoted.double.json.comments.snippets"}, "79": {"name": "punctuation.section.insertion.bracket.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.section.insertion.bracket.json.comments.snippets"}, "80": {"name": "meta.insertion.brackets.json.comments.snippets meta.insertion.format.remove.json.comments.snippets"}, "81": {"name": "punctuation.section.insertion.dollar.brackets.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.section.insertion.dollar.brackets.json.comments.snippets"}, "82": {"name": "punctuation.section.insertion.bracket.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.section.insertion.bracket.json.comments.snippets"}, "83": {"name": "variable.other.normal.json.comments.snippets custom.variable.other.normal.numeric.json.comments.snippets"}, "84": {"name": "punctuation.section.insertion.json.comments.snippets punctuation.separator.colon.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.separator.colon.json.comments.snippets"}, "85": {"name": "punctuation.separator.dash.json.comments.snippets"}, "86": {"patterns": [{"include": "#special_variables"}, {"include": "#simple_escape_context"}]}, "87": {"patterns": [{"include": "#quad_backslash_match"}, {"include": "#dollar_sign_escape"}, {"include": "#bracket_escape"}, {"include": "#basic_escape"}, {"include": "#invalid_escape"}, {"include": "#normal_characters"}]}, "88": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\$", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}, {"include": "#invalid_escape"}]}, "89": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\}", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}]}, "90": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}, "91": {"name": "constant.character.escape.json.comments.snippets"}, "92": {"name": "constant.character.escape.json.comments.snippets"}, "93": {"name": "constant.character.escape.json.comments.snippets invalid.illegal.unrecognized-string-escape.json.comments.snippets"}, "94": {"name": "string.quoted.double.json.comments.snippets"}, "95": {"patterns": [{"include": "#quad_backslash_match"}, {"include": "#dollar_sign_escape"}, {"include": "#bracket_escape"}, {"include": "#basic_escape"}, {"include": "#invalid_escape"}, {"include": "#normal_characters"}]}, "96": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\$", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}, {"include": "#invalid_escape"}]}, "97": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\}", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}]}, "98": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}, "99": {"name": "constant.character.escape.json.comments.snippets"}, "100": {"name": "constant.character.escape.json.comments.snippets"}, "101": {"name": "constant.character.escape.json.comments.snippets invalid.illegal.unrecognized-string-escape.json.comments.snippets"}, "102": {"name": "string.quoted.double.json.comments.snippets"}, "103": {"name": "punctuation.section.insertion.bracket.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.section.insertion.bracket.json.comments.snippets"}, "104": {"name": "meta.insertion.brackets.json.comments.snippets meta.insertion.format.default.json.comments.snippets"}, "105": {"name": "punctuation.section.insertion.dollar.brackets.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.section.insertion.dollar.brackets.json.comments.snippets"}, "106": {"name": "punctuation.section.insertion.bracket.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.section.insertion.bracket.json.comments.snippets"}, "107": {"name": "variable.other.normal.json.comments.snippets custom.variable.other.normal.numeric.json.comments.snippets"}, "108": {"name": "punctuation.section.insertion.json.comments.snippets punctuation.separator.colon.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.separator.colon.json.comments.snippets"}, "109": {"patterns": [{"include": "#special_variables"}, {"include": "#simple_escape_context"}]}, "110": {"patterns": [{"include": "#quad_backslash_match"}, {"include": "#dollar_sign_escape"}, {"include": "#bracket_escape"}, {"include": "#basic_escape"}, {"include": "#invalid_escape"}, {"include": "#normal_characters"}]}, "111": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\$", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}, {"include": "#invalid_escape"}]}, "112": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\}", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}]}, "113": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}, "114": {"name": "constant.character.escape.json.comments.snippets"}, "115": {"name": "constant.character.escape.json.comments.snippets"}, "116": {"name": "constant.character.escape.json.comments.snippets invalid.illegal.unrecognized-string-escape.json.comments.snippets"}, "117": {"name": "string.quoted.double.json.comments.snippets"}, "118": {"patterns": [{"include": "#quad_backslash_match"}, {"include": "#dollar_sign_escape"}, {"include": "#bracket_escape"}, {"include": "#basic_escape"}, {"include": "#invalid_escape"}, {"include": "#normal_characters"}]}, "119": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\$", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}, {"include": "#invalid_escape"}]}, "120": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\}", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}]}, "121": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}, "122": {"name": "constant.character.escape.json.comments.snippets"}, "123": {"name": "constant.character.escape.json.comments.snippets"}, "124": {"name": "constant.character.escape.json.comments.snippets invalid.illegal.unrecognized-string-escape.json.comments.snippets"}, "125": {"name": "string.quoted.double.json.comments.snippets"}, "126": {"name": "punctuation.section.insertion.bracket.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.section.insertion.bracket.json.comments.snippets"}, "127": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}, "128": {"name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}, "129": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}, "130": {"name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}, "131": {"patterns": [{"include": "#special_variables"}, {"include": "#simple_escape_context"}]}, "132": {"patterns": [{"include": "#quad_backslash_match"}, {"include": "#dollar_sign_escape"}, {"include": "#bracket_escape"}, {"include": "#basic_escape"}, {"include": "#invalid_escape"}, {"include": "#normal_characters"}]}, "133": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\$", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}, {"include": "#invalid_escape"}]}, "134": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\}", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}]}, "135": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}, "136": {"name": "constant.character.escape.json.comments.snippets"}, "137": {"name": "constant.character.escape.json.comments.snippets"}, "138": {"name": "constant.character.escape.json.comments.snippets invalid.illegal.unrecognized-string-escape.json.comments.snippets"}, "139": {"name": "string.quoted.double.json.comments.snippets"}}}]}, "124": {"name": "punctuation.section.regexp.json.comments.snippets"}, "125": {"name": "keyword.other.flag.json.comments.snippets"}, "126": {"name": "punctuation.section.insertion.bracket.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.section.insertion.bracket.json.comments.snippets"}, "127": {"name": "meta.insertion.text.json.comments.snippets"}, "128": {"patterns": [{"include": "#special_variables"}, {"include": "#simple_escape_context"}]}, "129": {"patterns": [{"include": "#quad_backslash_match"}, {"include": "#dollar_sign_escape"}, {"include": "#bracket_escape"}, {"include": "#basic_escape"}, {"include": "#invalid_escape"}, {"include": "#normal_characters"}]}, "130": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\$", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}, {"include": "#invalid_escape"}]}, "131": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\}", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}]}, "132": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}, "133": {"name": "constant.character.escape.json.comments.snippets"}, "134": {"name": "constant.character.escape.json.comments.snippets"}, "135": {"name": "constant.character.escape.json.comments.snippets invalid.illegal.unrecognized-string-escape.json.comments.snippets"}, "136": {"name": "string.quoted.double.json.comments.snippets"}, "137": {"patterns": [{"include": "#quad_backslash_match"}, {"include": "#dollar_sign_escape"}, {"include": "#bracket_escape"}, {"include": "#basic_escape"}, {"include": "#invalid_escape"}, {"include": "#normal_characters"}]}, "138": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\$", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}, {"include": "#invalid_escape"}]}, "139": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\}", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}]}, "140": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}, "141": {"name": "constant.character.escape.json.comments.snippets"}, "142": {"name": "constant.character.escape.json.comments.snippets"}, "143": {"name": "constant.character.escape.json.comments.snippets invalid.illegal.unrecognized-string-escape.json.comments.snippets"}, "144": {"name": "string.quoted.double.json.comments.snippets"}}, "name": "meta.any.json.comments.snippets"}, "bnf_choice": {"match": "(?:(?:(\\$)(\\{))([0-9]+)(\\|)(((?:(?:(?:(\\\\\\\\)(\\\\\\\\))|(?:(\\/\\/)(?:\\,|\\|))|((?!\\\\)(?:(?:(?:\\\\\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:[^\\{\\$\"\\\\]|(?=\")))|(?:(?:(?:\\\\\\\\\\\\\\\\)+)(?:[^\\{\\$\"\\\\]|(?=\")))))|((?<!\\\\)(?:(?:(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\$))|(?:\\\\\\$)))|((?<!\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\}))|(\\\\(?:[\"\\\\\\/bfnrt]|(?:u[0-9a-fA-F]{4})))|[^,}\\|])+))(?:(?:,\\g<6>)*?))(\\|)(\\}))", "captures": {"1": {"name": "punctuation.section.insertion.dollar.brackets.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.section.insertion.dollar.brackets.json.comments.snippets"}, "2": {"name": "punctuation.section.insertion.bracket.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.section.insertion.bracket.json.comments.snippets"}, "3": {"name": "variable.other.normal.json.comments.snippets custom.variable.other.normal.numeric.json.comments.snippets"}, "4": {"name": "punctuation.separator.choice.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.separator.choice.json.comments.snippets"}, "5": {"patterns": [{"match": ",", "name": "meta.insertion.choice.json.comments.snippets punctuation.separator.comma.json.comments.snippets"}, {"include": "#choice_option"}]}, "6": {"name": "meta.insertion.choice.json.comments.snippets constant.other.option.json.comments.snippets"}, "7": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}, "8": {"name": "constant.character.escape.json.comments.snippets"}, "9": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}, "10": {"patterns": [{"include": "#quad_backslash_match"}, {"include": "#dollar_sign_escape"}, {"include": "#bracket_escape"}, {"include": "#basic_escape"}, {"include": "#invalid_escape"}, {"include": "#normal_characters"}]}, "11": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\$", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}, {"include": "#invalid_escape"}]}, "12": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\}", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}]}, "13": {"name": "constant.character.escape.json.comments.snippets"}, "14": {"name": "punctuation.separator.choice.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.separator.choice.json.comments.snippets"}, "15": {"name": "punctuation.section.insertion.bracket.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.section.insertion.bracket.json.comments.snippets"}}, "name": "meta.insertion.brackets.json.comments.snippets meta.insertion.choice.json.comments.snippets"}, "bnf_format": {"match": "(?:(?:(?:(?:(?:(?:(?:(\\$)((?<!\\w)(?:TM_SELECTED_TEXT|TM_CURRENT_LINE|TM_CURRENT_WORD|TM_LINE_INDEX|TM_LINE_NUMBER|TM_FILENAME|TM_FILENAME_BASE|TM_DIRECTORY|TM_FILEPATH|RELATIVE_FILEPATH|CLIPBOARD|WORKSPACE_NAME|WORKSPACE_FOLDER|CURSOR_INDEX|CURSOR_NUMBER|CURRENT_YEAR|CURRENT_YEAR_SHORT|CURRENT_MONTH|CURRENT_MONTH_NAME|CURRENT_MONTH_NAME_SHORT|CURRENT_DATE|CURRENT_DAY_NAME|CURRENT_DAY_NAME_SHORT|CURRENT_HOUR|CURRENT_MINUTE|CURRENT_SECOND|CURRENT_SECONDS_UNIX|CURRENT_TIMEZONE_OFFSET|RANDOM|RANDOM_HEX|UUID|BLOCK_COMMENT_START|BLOCK_COMMENT_END|LINE_COMMENT)(?!\\w)))|((?:(\\$)([0-9]+))))|((?:(?:(\\$)(\\{))([0-9]+)(:)(?:(\\/)(upcase|downcase|capitalize|camelcase|pascalcase))(\\}))))|((?:(?:(\\$)(\\{))([0-9]+)(:)(\\+)((?:(?:((?!\\\\)(?:(?:(?:\\\\\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:[^\\{\\$\"\\\\]|(?=\")))|(?:(?:(?:\\\\\\\\\\\\\\\\)+)(?:[^\\{\\$\"\\\\]|(?=\")))))|((?<!\\\\)(?:(?:(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\$))|(?:\\\\\\$)))|((?<!\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\}))|(?:(\\\\\\\\)(\\\\\\\\))|(\\\\(?:[\"\\\\\\/bfnrt]|(?:u[0-9a-fA-F]{4})))|((?:\\\\.))|([^\\\\\\n\\}\"]))?)(?:(?:((?!\\\\)(?:(?:(?:\\\\\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:[^\\{\\$\"\\\\]|(?=\")))|(?:(?:(?:\\\\\\\\\\\\\\\\)+)(?:[^\\{\\$\"\\\\]|(?=\")))))|((?<!\\\\)(?:(?:(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\$))|(?:\\\\\\$)))|((?<!\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\}))|(?:(\\\\\\\\)(\\\\\\\\))|(\\\\(?:[\"\\\\\\/bfnrt]|(?:u[0-9a-fA-F]{4})))|((?:\\\\.))|([^\\\\\\n\\}\"]))*?))(\\}))))|((?:(?:(\\$)(\\{))([0-9]+)(:)(\\?)((?:(?:((?!\\\\)(?:(?:(?:\\\\\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:[^\\{\\$\"\\\\]|(?=\")))|(?:(?:(?:\\\\\\\\\\\\\\\\)+)(?:[^\\{\\$\"\\\\]|(?=\")))))|((?<!\\\\)(?:(?:(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\$))|(?:\\\\\\$)))|((?<!\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\}))|(?:(\\\\\\\\)(\\\\\\\\))|(\\\\(?:[\"\\\\\\/bfnrt]|(?:u[0-9a-fA-F]{4})))|((?:\\\\.))|([^\\\\\\n\\}\"]))?)(?:(?:((?!\\\\)(?:(?:(?:\\\\\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:[^\\{\\$\"\\\\]|(?=\")))|(?:(?:(?:\\\\\\\\\\\\\\\\)+)(?:[^\\{\\$\"\\\\]|(?=\")))))|((?<!\\\\)(?:(?:(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\$))|(?:\\\\\\$)))|((?<!\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\}))|(?:(\\\\\\\\)(\\\\\\\\))|(\\\\(?:[\"\\\\\\/bfnrt]|(?:u[0-9a-fA-F]{4})))|((?:\\\\.))|([^\\\\\\n\\}\"]))*?))(:)((?:(?:((?!\\\\)(?:(?:(?:\\\\\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:[^\\{\\$\"\\\\]|(?=\")))|(?:(?:(?:\\\\\\\\\\\\\\\\)+)(?:[^\\{\\$\"\\\\]|(?=\")))))|((?<!\\\\)(?:(?:(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\$))|(?:\\\\\\$)))|((?<!\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\}))|(?:(\\\\\\\\)(\\\\\\\\))|(\\\\(?:[\"\\\\\\/bfnrt]|(?:u[0-9a-fA-F]{4})))|((?:\\\\.))|([^\\\\\\n\\}\"]))?)(?:(?:((?!\\\\)(?:(?:(?:\\\\\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:[^\\{\\$\"\\\\]|(?=\")))|(?:(?:(?:\\\\\\\\\\\\\\\\)+)(?:[^\\{\\$\"\\\\]|(?=\")))))|((?<!\\\\)(?:(?:(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\$))|(?:\\\\\\$)))|((?<!\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\}))|(?:(\\\\\\\\)(\\\\\\\\))|(\\\\(?:[\"\\\\\\/bfnrt]|(?:u[0-9a-fA-F]{4})))|((?:\\\\.))|([^\\\\\\n\\}\"]))*?))(\\}))))|((?:(?:(\\$)(\\{))([0-9]+)(:)(\\-)((?:(?:((?!\\\\)(?:(?:(?:\\\\\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:[^\\{\\$\"\\\\]|(?=\")))|(?:(?:(?:\\\\\\\\\\\\\\\\)+)(?:[^\\{\\$\"\\\\]|(?=\")))))|((?<!\\\\)(?:(?:(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\$))|(?:\\\\\\$)))|((?<!\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\}))|(?:(\\\\\\\\)(\\\\\\\\))|(\\\\(?:[\"\\\\\\/bfnrt]|(?:u[0-9a-fA-F]{4})))|((?:\\\\.))|([^\\\\\\n\\}\"]))?)(?:(?:((?!\\\\)(?:(?:(?:\\\\\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:[^\\{\\$\"\\\\]|(?=\")))|(?:(?:(?:\\\\\\\\\\\\\\\\)+)(?:[^\\{\\$\"\\\\]|(?=\")))))|((?<!\\\\)(?:(?:(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\$))|(?:\\\\\\$)))|((?<!\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\}))|(?:(\\\\\\\\)(\\\\\\\\))|(\\\\(?:[\"\\\\\\/bfnrt]|(?:u[0-9a-fA-F]{4})))|((?:\\\\.))|([^\\\\\\n\\}\"]))*?))(\\}))))|((?:(?:(\\$)(\\{))([0-9]+)(:)((?:(?:((?!\\\\)(?:(?:(?:\\\\\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:[^\\{\\$\"\\\\]|(?=\")))|(?:(?:(?:\\\\\\\\\\\\\\\\)+)(?:[^\\{\\$\"\\\\]|(?=\")))))|((?<!\\\\)(?:(?:(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\$))|(?:\\\\\\$)))|((?<!\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\}))|(?:(\\\\\\\\)(\\\\\\\\))|(\\\\(?:[\"\\\\\\/bfnrt]|(?:u[0-9a-fA-F]{4})))|((?:\\\\.))|([^\\\\\\n\\}\"]))?)(?:(?:((?!\\\\)(?:(?:(?:\\\\\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:[^\\{\\$\"\\\\]|(?=\")))|(?:(?:(?:\\\\\\\\\\\\\\\\)+)(?:[^\\{\\$\"\\\\]|(?=\")))))|((?<!\\\\)(?:(?:(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\$))|(?:\\\\\\$)))|((?<!\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\}))|(?:(\\\\\\\\)(\\\\\\\\))|(\\\\(?:[\"\\\\\\/bfnrt]|(?:u[0-9a-fA-F]{4})))|((?:\\\\.))|([^\\\\\\n\\}\"]))*?))(\\}))))", "captures": {"1": {"name": "meta.insertion.simple.json.comments.snippets punctuation.section.insertion.dollar.simple.json.comments.snippets keyword.operator.insertion.json.comments.snippets variable.language.this.json.comments.snippets"}, "2": {"name": "meta.insertion.simple.json.comments.snippets keyword.operator.insertion.json.comments.snippets variable.language.this.json.comments.snippets"}, "3": {"name": "meta.insertion.simple.numeric.json.comments.snippets meta.insertion.format.simple.json.comments.snippets"}, "4": {"name": "punctuation.section.insertion.dollar.simple.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.section.insertion.dollar.simple.json.comments.snippets"}, "5": {"name": "variable.other.normal.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.variable.other.normal.numeric.json.comments.snippets"}, "6": {"name": "meta.insertion.brackets.json.comments.snippets meta.insertion.format.transform.json.comments.snippets"}, "7": {"name": "punctuation.section.insertion.dollar.brackets.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.section.insertion.dollar.brackets.json.comments.snippets"}, "8": {"name": "punctuation.section.insertion.bracket.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.section.insertion.bracket.json.comments.snippets"}, "9": {"name": "variable.other.normal.json.comments.snippets custom.variable.other.normal.numeric.json.comments.snippets"}, "10": {"name": "punctuation.section.insertion.json.comments.snippets punctuation.separator.colon.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.separator.colon.json.comments.snippets"}, "11": {"name": "punctuation.section.regexp.json.comments.snippets support.type.built-in.json.comments.snippets variable.language.special.transform.json.comments.snippets"}, "12": {"name": "support.type.built-in.json.comments.snippets variable.language.special.transform.json.comments.snippets"}, "13": {"name": "punctuation.section.insertion.bracket.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.section.insertion.bracket.json.comments.snippets"}, "14": {"name": "meta.insertion.brackets.json.comments.snippets meta.insertion.format.plus.json.comments.snippets"}, "15": {"name": "punctuation.section.insertion.dollar.brackets.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.section.insertion.dollar.brackets.json.comments.snippets"}, "16": {"name": "punctuation.section.insertion.bracket.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.section.insertion.bracket.json.comments.snippets"}, "17": {"name": "variable.other.normal.json.comments.snippets custom.variable.other.normal.numeric.json.comments.snippets"}, "18": {"name": "punctuation.section.insertion.json.comments.snippets punctuation.separator.colon.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.separator.colon.json.comments.snippets"}, "19": {"name": "punctuation.separator.plus.json.comments.snippets"}, "20": {"patterns": [{"include": "#special_variables"}, {"include": "#simple_escape_context"}]}, "21": {"patterns": [{"include": "#quad_backslash_match"}, {"include": "#dollar_sign_escape"}, {"include": "#bracket_escape"}, {"include": "#basic_escape"}, {"include": "#invalid_escape"}, {"include": "#normal_characters"}]}, "22": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\$", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}, {"include": "#invalid_escape"}]}, "23": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\}", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}]}, "24": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}, "25": {"name": "constant.character.escape.json.comments.snippets"}, "26": {"name": "constant.character.escape.json.comments.snippets"}, "27": {"name": "constant.character.escape.json.comments.snippets invalid.illegal.unrecognized-string-escape.json.comments.snippets"}, "28": {"name": "string.quoted.double.json.comments.snippets"}, "29": {"patterns": [{"include": "#quad_backslash_match"}, {"include": "#dollar_sign_escape"}, {"include": "#bracket_escape"}, {"include": "#basic_escape"}, {"include": "#invalid_escape"}, {"include": "#normal_characters"}]}, "30": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\$", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}, {"include": "#invalid_escape"}]}, "31": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\}", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}]}, "32": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}, "33": {"name": "constant.character.escape.json.comments.snippets"}, "34": {"name": "constant.character.escape.json.comments.snippets"}, "35": {"name": "constant.character.escape.json.comments.snippets invalid.illegal.unrecognized-string-escape.json.comments.snippets"}, "36": {"name": "string.quoted.double.json.comments.snippets"}, "37": {"name": "punctuation.section.insertion.bracket.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.section.insertion.bracket.json.comments.snippets"}, "38": {"name": "meta.insertion.brackets.json.comments.snippets meta.insertion.format.conditional.json.comments.snippets"}, "39": {"name": "punctuation.section.insertion.dollar.brackets.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.section.insertion.dollar.brackets.json.comments.snippets"}, "40": {"name": "punctuation.section.insertion.bracket.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.section.insertion.bracket.json.comments.snippets"}, "41": {"name": "variable.other.normal.json.comments.snippets custom.variable.other.normal.numeric.json.comments.snippets"}, "42": {"name": "punctuation.section.insertion.json.comments.snippets punctuation.separator.colon.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.separator.colon.json.comments.snippets"}, "43": {"name": "punctuation.separator.conditional.json.comments.snippets keyword.operator.ternary.json.comments.snippets"}, "44": {"patterns": [{"include": "#special_variables"}, {"include": "#simple_escape_context"}]}, "45": {"patterns": [{"include": "#quad_backslash_match"}, {"include": "#dollar_sign_escape"}, {"include": "#bracket_escape"}, {"include": "#basic_escape"}, {"include": "#invalid_escape"}, {"include": "#normal_characters"}]}, "46": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\$", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}, {"include": "#invalid_escape"}]}, "47": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\}", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}]}, "48": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}, "49": {"name": "constant.character.escape.json.comments.snippets"}, "50": {"name": "constant.character.escape.json.comments.snippets"}, "51": {"name": "constant.character.escape.json.comments.snippets invalid.illegal.unrecognized-string-escape.json.comments.snippets"}, "52": {"name": "string.quoted.double.json.comments.snippets"}, "53": {"patterns": [{"include": "#quad_backslash_match"}, {"include": "#dollar_sign_escape"}, {"include": "#bracket_escape"}, {"include": "#basic_escape"}, {"include": "#invalid_escape"}, {"include": "#normal_characters"}]}, "54": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\$", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}, {"include": "#invalid_escape"}]}, "55": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\}", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}]}, "56": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}, "57": {"name": "constant.character.escape.json.comments.snippets"}, "58": {"name": "constant.character.escape.json.comments.snippets"}, "59": {"name": "constant.character.escape.json.comments.snippets invalid.illegal.unrecognized-string-escape.json.comments.snippets"}, "60": {"name": "string.quoted.double.json.comments.snippets"}, "61": {"name": "keyword.operator.ternary.json.comments.snippets"}, "62": {"patterns": [{"include": "#special_variables"}, {"include": "#simple_escape_context"}]}, "63": {"patterns": [{"include": "#quad_backslash_match"}, {"include": "#dollar_sign_escape"}, {"include": "#bracket_escape"}, {"include": "#basic_escape"}, {"include": "#invalid_escape"}, {"include": "#normal_characters"}]}, "64": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\$", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}, {"include": "#invalid_escape"}]}, "65": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\}", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}]}, "66": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}, "67": {"name": "constant.character.escape.json.comments.snippets"}, "68": {"name": "constant.character.escape.json.comments.snippets"}, "69": {"name": "constant.character.escape.json.comments.snippets invalid.illegal.unrecognized-string-escape.json.comments.snippets"}, "70": {"name": "string.quoted.double.json.comments.snippets"}, "71": {"patterns": [{"include": "#quad_backslash_match"}, {"include": "#dollar_sign_escape"}, {"include": "#bracket_escape"}, {"include": "#basic_escape"}, {"include": "#invalid_escape"}, {"include": "#normal_characters"}]}, "72": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\$", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}, {"include": "#invalid_escape"}]}, "73": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\}", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}]}, "74": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}, "75": {"name": "constant.character.escape.json.comments.snippets"}, "76": {"name": "constant.character.escape.json.comments.snippets"}, "77": {"name": "constant.character.escape.json.comments.snippets invalid.illegal.unrecognized-string-escape.json.comments.snippets"}, "78": {"name": "string.quoted.double.json.comments.snippets"}, "79": {"name": "punctuation.section.insertion.bracket.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.section.insertion.bracket.json.comments.snippets"}, "80": {"name": "meta.insertion.brackets.json.comments.snippets meta.insertion.format.remove.json.comments.snippets"}, "81": {"name": "punctuation.section.insertion.dollar.brackets.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.section.insertion.dollar.brackets.json.comments.snippets"}, "82": {"name": "punctuation.section.insertion.bracket.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.section.insertion.bracket.json.comments.snippets"}, "83": {"name": "variable.other.normal.json.comments.snippets custom.variable.other.normal.numeric.json.comments.snippets"}, "84": {"name": "punctuation.section.insertion.json.comments.snippets punctuation.separator.colon.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.separator.colon.json.comments.snippets"}, "85": {"name": "punctuation.separator.dash.json.comments.snippets"}, "86": {"patterns": [{"include": "#special_variables"}, {"include": "#simple_escape_context"}]}, "87": {"patterns": [{"include": "#quad_backslash_match"}, {"include": "#dollar_sign_escape"}, {"include": "#bracket_escape"}, {"include": "#basic_escape"}, {"include": "#invalid_escape"}, {"include": "#normal_characters"}]}, "88": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\$", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}, {"include": "#invalid_escape"}]}, "89": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\}", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}]}, "90": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}, "91": {"name": "constant.character.escape.json.comments.snippets"}, "92": {"name": "constant.character.escape.json.comments.snippets"}, "93": {"name": "constant.character.escape.json.comments.snippets invalid.illegal.unrecognized-string-escape.json.comments.snippets"}, "94": {"name": "string.quoted.double.json.comments.snippets"}, "95": {"patterns": [{"include": "#quad_backslash_match"}, {"include": "#dollar_sign_escape"}, {"include": "#bracket_escape"}, {"include": "#basic_escape"}, {"include": "#invalid_escape"}, {"include": "#normal_characters"}]}, "96": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\$", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}, {"include": "#invalid_escape"}]}, "97": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\}", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}]}, "98": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}, "99": {"name": "constant.character.escape.json.comments.snippets"}, "100": {"name": "constant.character.escape.json.comments.snippets"}, "101": {"name": "constant.character.escape.json.comments.snippets invalid.illegal.unrecognized-string-escape.json.comments.snippets"}, "102": {"name": "string.quoted.double.json.comments.snippets"}, "103": {"name": "punctuation.section.insertion.bracket.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.section.insertion.bracket.json.comments.snippets"}, "104": {"name": "meta.insertion.brackets.json.comments.snippets meta.insertion.format.default.json.comments.snippets"}, "105": {"name": "punctuation.section.insertion.dollar.brackets.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.section.insertion.dollar.brackets.json.comments.snippets"}, "106": {"name": "punctuation.section.insertion.bracket.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.section.insertion.bracket.json.comments.snippets"}, "107": {"name": "variable.other.normal.json.comments.snippets custom.variable.other.normal.numeric.json.comments.snippets"}, "108": {"name": "punctuation.section.insertion.json.comments.snippets punctuation.separator.colon.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.separator.colon.json.comments.snippets"}, "109": {"patterns": [{"include": "#special_variables"}, {"include": "#simple_escape_context"}]}, "110": {"patterns": [{"include": "#quad_backslash_match"}, {"include": "#dollar_sign_escape"}, {"include": "#bracket_escape"}, {"include": "#basic_escape"}, {"include": "#invalid_escape"}, {"include": "#normal_characters"}]}, "111": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\$", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}, {"include": "#invalid_escape"}]}, "112": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\}", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}]}, "113": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}, "114": {"name": "constant.character.escape.json.comments.snippets"}, "115": {"name": "constant.character.escape.json.comments.snippets"}, "116": {"name": "constant.character.escape.json.comments.snippets invalid.illegal.unrecognized-string-escape.json.comments.snippets"}, "117": {"name": "string.quoted.double.json.comments.snippets"}, "118": {"patterns": [{"include": "#quad_backslash_match"}, {"include": "#dollar_sign_escape"}, {"include": "#bracket_escape"}, {"include": "#basic_escape"}, {"include": "#invalid_escape"}, {"include": "#normal_characters"}]}, "119": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\$", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}, {"include": "#invalid_escape"}]}, "120": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\}", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}]}, "121": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}, "122": {"name": "constant.character.escape.json.comments.snippets"}, "123": {"name": "constant.character.escape.json.comments.snippets"}, "124": {"name": "constant.character.escape.json.comments.snippets invalid.illegal.unrecognized-string-escape.json.comments.snippets"}, "125": {"name": "string.quoted.double.json.comments.snippets"}, "126": {"name": "punctuation.section.insertion.bracket.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.section.insertion.bracket.json.comments.snippets"}}}, "bnf_int": {"match": "[0-9]+", "name": "variable.other.normal.json.comments.snippets custom.variable.other.normal.numeric.json.comments.snippets"}, "bnf_int_simple": {"match": "[0-9]+", "name": "variable.other.normal.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.variable.other.normal.numeric.json.comments.snippets"}, "bnf_tabstop": {"match": "(?:(?:((?:(\\$)([0-9]+)))|((?:(?:(\\$)(\\{))([0-9]+)(\\}))))|((?:(?:(\\$)(\\{))([0-9]+)((?:(\\/)((?:(?:(?:(?:(\\\\)(\\\\\\/))|(?:(\\\\\\\\\\\\)(\\\\\\/)))|[^\\/\\n])+))(\\/)(((?:(?:(?:(?:(?:(?:(?:(?:\\$(?:(?<!\\w)(?:TM_SELECTED_TEXT|TM_CURRENT_LINE|TM_CURRENT_WORD|TM_LINE_INDEX|TM_LINE_NUMBER|TM_FILENAME|TM_FILENAME_BASE|TM_DIRECTORY|TM_FILEPATH|RELATIVE_FILEPATH|CLIPBOARD|WORKSPACE_NAME|WORKSPACE_FOLDER|CURSOR_INDEX|CURSOR_NUMBER|CURRENT_YEAR|CURRENT_YEAR_SHORT|CURRENT_MONTH|CURRENT_MONTH_NAME|CURRENT_MONTH_NAME_SHORT|CURRENT_DATE|CURRENT_DAY_NAME|CURRENT_DAY_NAME_SHORT|CURRENT_HOUR|CURRENT_MINUTE|CURRENT_SECOND|CURRENT_SECONDS_UNIX|CURRENT_TIMEZONE_OFFSET|RANDOM|RANDOM_HEX|UUID|BLOCK_COMMENT_START|BLOCK_COMMENT_END|LINE_COMMENT)(?!\\w)))|(?:\\$(?:[0-9]+)))|(?:(?:\\$\\{)(?:[0-9]+):(?:\\/(?:upcase|downcase|capitalize|camelcase|pascalcase))\\}))|(?:(?:\\$\\{)(?:[0-9]+):\\+(?:(?:(?:(?:(?!\\\\)(?:(?:(?:\\\\\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:[^\\{\\$\"\\\\]|(?=\")))|(?:(?:(?:\\\\\\\\\\\\\\\\)+)(?:[^\\{\\$\"\\\\]|(?=\")))))|(?:(?<!\\\\)(?:(?:(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\$))|(?:\\\\\\$)))|(?:(?<!\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\}))|(?:(?:\\\\\\\\)(?:\\\\\\\\))|(?:\\\\(?:[\"\\\\\\/bfnrt]|(?:u[0-9a-fA-F]{4})))|(?:\\\\.)|[^\\\\\\n\\}\"])?)(?:(?:(?:(?!\\\\)(?:(?:(?:\\\\\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:[^\\{\\$\"\\\\]|(?=\")))|(?:(?:(?:\\\\\\\\\\\\\\\\)+)(?:[^\\{\\$\"\\\\]|(?=\")))))|(?:(?<!\\\\)(?:(?:(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\$))|(?:\\\\\\$)))|(?:(?<!\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\}))|(?:(?:\\\\\\\\)(?:\\\\\\\\))|(?:\\\\(?:[\"\\\\\\/bfnrt]|(?:u[0-9a-fA-F]{4})))|(?:\\\\.)|[^\\\\\\n\\}\"])*?))\\}))|(?:(?:\\$\\{)(?:[0-9]+):\\?(?:(?:(?:(?:(?!\\\\)(?:(?:(?:\\\\\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:[^\\{\\$\"\\\\]|(?=\")))|(?:(?:(?:\\\\\\\\\\\\\\\\)+)(?:[^\\{\\$\"\\\\]|(?=\")))))|(?:(?<!\\\\)(?:(?:(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\$))|(?:\\\\\\$)))|(?:(?<!\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\}))|(?:(?:\\\\\\\\)(?:\\\\\\\\))|(?:\\\\(?:[\"\\\\\\/bfnrt]|(?:u[0-9a-fA-F]{4})))|(?:\\\\.)|[^\\\\\\n\\}\"])?)(?:(?:(?:(?!\\\\)(?:(?:(?:\\\\\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:[^\\{\\$\"\\\\]|(?=\")))|(?:(?:(?:\\\\\\\\\\\\\\\\)+)(?:[^\\{\\$\"\\\\]|(?=\")))))|(?:(?<!\\\\)(?:(?:(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\$))|(?:\\\\\\$)))|(?:(?<!\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\}))|(?:(?:\\\\\\\\)(?:\\\\\\\\))|(?:\\\\(?:[\"\\\\\\/bfnrt]|(?:u[0-9a-fA-F]{4})))|(?:\\\\.)|[^\\\\\\n\\}\"])*?)):(?:(?:(?:(?:(?!\\\\)(?:(?:(?:\\\\\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:[^\\{\\$\"\\\\]|(?=\")))|(?:(?:(?:\\\\\\\\\\\\\\\\)+)(?:[^\\{\\$\"\\\\]|(?=\")))))|(?:(?<!\\\\)(?:(?:(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\$))|(?:\\\\\\$)))|(?:(?<!\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\}))|(?:(?:\\\\\\\\)(?:\\\\\\\\))|(?:\\\\(?:[\"\\\\\\/bfnrt]|(?:u[0-9a-fA-F]{4})))|(?:\\\\.)|[^\\\\\\n\\}\"])?)(?:(?:(?:(?!\\\\)(?:(?:(?:\\\\\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:[^\\{\\$\"\\\\]|(?=\")))|(?:(?:(?:\\\\\\\\\\\\\\\\)+)(?:[^\\{\\$\"\\\\]|(?=\")))))|(?:(?<!\\\\)(?:(?:(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\$))|(?:\\\\\\$)))|(?:(?<!\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\}))|(?:(?:\\\\\\\\)(?:\\\\\\\\))|(?:\\\\(?:[\"\\\\\\/bfnrt]|(?:u[0-9a-fA-F]{4})))|(?:\\\\.)|[^\\\\\\n\\}\"])*?))\\}))|(?:(?:\\$\\{)(?:[0-9]+):\\-(?:(?:(?:(?:(?!\\\\)(?:(?:(?:\\\\\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:[^\\{\\$\"\\\\]|(?=\")))|(?:(?:(?:\\\\\\\\\\\\\\\\)+)(?:[^\\{\\$\"\\\\]|(?=\")))))|(?:(?<!\\\\)(?:(?:(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\$))|(?:\\\\\\$)))|(?:(?<!\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\}))|(?:(?:\\\\\\\\)(?:\\\\\\\\))|(?:\\\\(?:[\"\\\\\\/bfnrt]|(?:u[0-9a-fA-F]{4})))|(?:\\\\.)|[^\\\\\\n\\}\"])?)(?:(?:(?:(?!\\\\)(?:(?:(?:\\\\\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:[^\\{\\$\"\\\\]|(?=\")))|(?:(?:(?:\\\\\\\\\\\\\\\\)+)(?:[^\\{\\$\"\\\\]|(?=\")))))|(?:(?<!\\\\)(?:(?:(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\$))|(?:\\\\\\$)))|(?:(?<!\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\}))|(?:(?:\\\\\\\\)(?:\\\\\\\\))|(?:\\\\(?:[\"\\\\\\/bfnrt]|(?:u[0-9a-fA-F]{4})))|(?:\\\\.)|[^\\\\\\n\\}\"])*?))\\}))|(?:(?:\\$\\{)(?:[0-9]+):(?:(?:(?:(?:(?!\\\\)(?:(?:(?:\\\\\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:[^\\{\\$\"\\\\]|(?=\")))|(?:(?:(?:\\\\\\\\\\\\\\\\)+)(?:[^\\{\\$\"\\\\]|(?=\")))))|(?:(?<!\\\\)(?:(?:(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\$))|(?:\\\\\\$)))|(?:(?<!\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\}))|(?:(?:\\\\\\\\)(?:\\\\\\\\))|(?:\\\\(?:[\"\\\\\\/bfnrt]|(?:u[0-9a-fA-F]{4})))|(?:\\\\.)|[^\\\\\\n\\}\"])?)(?:(?:(?:(?!\\\\)(?:(?:(?:\\\\\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:[^\\{\\$\"\\\\]|(?=\")))|(?:(?:(?:\\\\\\\\\\\\\\\\)+)(?:[^\\{\\$\"\\\\]|(?=\")))))|(?:(?<!\\\\)(?:(?:(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\$))|(?:\\\\\\$)))|(?:(?<!\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\}))|(?:(?:\\\\\\\\)(?:\\\\\\\\))|(?:\\\\(?:[\"\\\\\\/bfnrt]|(?:u[0-9a-fA-F]{4})))|(?:\\\\.)|[^\\\\\\n\\}\"])*?))\\}))|(?:(?:(?:\\\\(?:\\\\\\/))|(?:(?:\\\\\\\\\\\\)(?:\\\\\\/)))|(?:(?:(?:(?:(?!\\\\)(?:(?:(?:\\\\\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:[^\\{\\$\"\\\\]|(?=\")))|(?:(?:(?:\\\\\\\\\\\\\\\\)+)(?:[^\\{\\$\"\\\\]|(?=\")))))|(?:(?<!\\\\)(?:(?:(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\$))|(?:\\\\\\$)))|(?:(?<!\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\}))|(?:(?:\\\\\\\\)(?:\\\\\\\\))|(?:\\\\(?:[\"\\\\\\/bfnrt]|(?:u[0-9a-fA-F]{4})))|(?:\\\\.)|[^\\\\\\n\\}\"])|[^\\n\\r])*)))*))(\\/)([igmyu]{0,5})))(\\}))))", "captures": {"1": {"name": "meta.insertion.simple.numeric.json.comments.snippets meta.insertion.tabstop.simple.json.comments.snippets"}, "2": {"name": "punctuation.section.insertion.dollar.simple.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.section.insertion.dollar.simple.json.comments.snippets"}, "3": {"name": "variable.other.normal.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.variable.other.normal.numeric.json.comments.snippets"}, "4": {"name": "meta.insertion.brackets.json.comments.snippets meta.insertion.tabstop.bracket.json.comments.snippets"}, "5": {"name": "punctuation.section.insertion.dollar.brackets.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.section.insertion.dollar.brackets.json.comments.snippets"}, "6": {"name": "punctuation.section.insertion.bracket.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.section.insertion.bracket.json.comments.snippets"}, "7": {"name": "variable.other.normal.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.variable.other.normal.numeric.json.comments.snippets"}, "8": {"name": "punctuation.section.insertion.bracket.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.section.insertion.bracket.json.comments.snippets"}, "9": {"name": "meta.insertion.brackets.json.comments.snippets meta.insertion.tabstop.transform.json.comments.snippets"}, "10": {"name": "punctuation.section.insertion.dollar.brackets.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.section.insertion.dollar.brackets.json.comments.snippets"}, "11": {"name": "punctuation.section.insertion.bracket.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.section.insertion.bracket.json.comments.snippets"}, "12": {"name": "variable.other.normal.json.comments.snippets custom.variable.other.normal.numeric.json.comments.snippets"}, "13": {"name": "meta.insertion.transform.json.comments.snippets string.regexp.json.comments.snippets"}, "14": {"name": "punctuation.section.regexp.json.comments.snippets"}, "15": {"patterns": [{"include": "source.syntax.regexp.tmLanguage"}, {"match": "(?:(?:(\\\\)(\\\\\\/))|(?:(\\\\\\\\\\\\)(\\\\\\/)))", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}, "2": {"name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}, "3": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}, "4": {"name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}}}, {"include": "#simple_escape_context"}]}, "16": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}, "17": {"name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}, "18": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}, "19": {"name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}, "20": {"name": "punctuation.section.regexp.json.comments.snippets"}, "21": {"patterns": [{"match": "\\$\\d+", "name": "variable.language.capture.json.comments.snippets"}, {"match": "\\$\\{\\d+\\}", "name": "variable.language.capture.json.comments.snippets"}, {"include": "#bnf_format"}, {"include": "#regex_backslash_escape"}, {"include": "#bnf_text"}]}, "22": {"patterns": [{"match": "(?:(?:(?:(?:(?:(?:(?:(?:(\\$)((?<!\\w)(?:TM_SELECTED_TEXT|TM_CURRENT_LINE|TM_CURRENT_WORD|TM_LINE_INDEX|TM_LINE_NUMBER|TM_FILENAME|TM_FILENAME_BASE|TM_DIRECTORY|TM_FILEPATH|RELATIVE_FILEPATH|CLIPBOARD|WORKSPACE_NAME|WORKSPACE_FOLDER|CURSOR_INDEX|CURSOR_NUMBER|CURRENT_YEAR|CURRENT_YEAR_SHORT|CURRENT_MONTH|CURRENT_MONTH_NAME|CURRENT_MONTH_NAME_SHORT|CURRENT_DATE|CURRENT_DAY_NAME|CURRENT_DAY_NAME_SHORT|CURRENT_HOUR|CURRENT_MINUTE|CURRENT_SECOND|CURRENT_SECONDS_UNIX|CURRENT_TIMEZONE_OFFSET|RANDOM|RANDOM_HEX|UUID|BLOCK_COMMENT_START|BLOCK_COMMENT_END|LINE_COMMENT)(?!\\w)))|((?:(\\$)([0-9]+))))|((?:(?:(\\$)(\\{))([0-9]+)(:)(?:(\\/)(upcase|downcase|capitalize|camelcase|pascalcase))(\\}))))|((?:(?:(\\$)(\\{))([0-9]+)(:)(\\+)((?:(?:((?!\\\\)(?:(?:(?:\\\\\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:[^\\{\\$\"\\\\]|(?=\")))|(?:(?:(?:\\\\\\\\\\\\\\\\)+)(?:[^\\{\\$\"\\\\]|(?=\")))))|((?<!\\\\)(?:(?:(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\$))|(?:\\\\\\$)))|((?<!\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\}))|(?:(\\\\\\\\)(\\\\\\\\))|(\\\\(?:[\"\\\\\\/bfnrt]|(?:u[0-9a-fA-F]{4})))|((?:\\\\.))|([^\\\\\\n\\}\"]))?)(?:(?:((?!\\\\)(?:(?:(?:\\\\\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:[^\\{\\$\"\\\\]|(?=\")))|(?:(?:(?:\\\\\\\\\\\\\\\\)+)(?:[^\\{\\$\"\\\\]|(?=\")))))|((?<!\\\\)(?:(?:(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\$))|(?:\\\\\\$)))|((?<!\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\}))|(?:(\\\\\\\\)(\\\\\\\\))|(\\\\(?:[\"\\\\\\/bfnrt]|(?:u[0-9a-fA-F]{4})))|((?:\\\\.))|([^\\\\\\n\\}\"]))*?))(\\}))))|((?:(?:(\\$)(\\{))([0-9]+)(:)(\\?)((?:(?:((?!\\\\)(?:(?:(?:\\\\\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:[^\\{\\$\"\\\\]|(?=\")))|(?:(?:(?:\\\\\\\\\\\\\\\\)+)(?:[^\\{\\$\"\\\\]|(?=\")))))|((?<!\\\\)(?:(?:(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\$))|(?:\\\\\\$)))|((?<!\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\}))|(?:(\\\\\\\\)(\\\\\\\\))|(\\\\(?:[\"\\\\\\/bfnrt]|(?:u[0-9a-fA-F]{4})))|((?:\\\\.))|([^\\\\\\n\\}\"]))?)(?:(?:((?!\\\\)(?:(?:(?:\\\\\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:[^\\{\\$\"\\\\]|(?=\")))|(?:(?:(?:\\\\\\\\\\\\\\\\)+)(?:[^\\{\\$\"\\\\]|(?=\")))))|((?<!\\\\)(?:(?:(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\$))|(?:\\\\\\$)))|((?<!\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\}))|(?:(\\\\\\\\)(\\\\\\\\))|(\\\\(?:[\"\\\\\\/bfnrt]|(?:u[0-9a-fA-F]{4})))|((?:\\\\.))|([^\\\\\\n\\}\"]))*?))(:)((?:(?:((?!\\\\)(?:(?:(?:\\\\\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:[^\\{\\$\"\\\\]|(?=\")))|(?:(?:(?:\\\\\\\\\\\\\\\\)+)(?:[^\\{\\$\"\\\\]|(?=\")))))|((?<!\\\\)(?:(?:(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\$))|(?:\\\\\\$)))|((?<!\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\}))|(?:(\\\\\\\\)(\\\\\\\\))|(\\\\(?:[\"\\\\\\/bfnrt]|(?:u[0-9a-fA-F]{4})))|((?:\\\\.))|([^\\\\\\n\\}\"]))?)(?:(?:((?!\\\\)(?:(?:(?:\\\\\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:[^\\{\\$\"\\\\]|(?=\")))|(?:(?:(?:\\\\\\\\\\\\\\\\)+)(?:[^\\{\\$\"\\\\]|(?=\")))))|((?<!\\\\)(?:(?:(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\$))|(?:\\\\\\$)))|((?<!\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\}))|(?:(\\\\\\\\)(\\\\\\\\))|(\\\\(?:[\"\\\\\\/bfnrt]|(?:u[0-9a-fA-F]{4})))|((?:\\\\.))|([^\\\\\\n\\}\"]))*?))(\\}))))|((?:(?:(\\$)(\\{))([0-9]+)(:)(\\-)((?:(?:((?!\\\\)(?:(?:(?:\\\\\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:[^\\{\\$\"\\\\]|(?=\")))|(?:(?:(?:\\\\\\\\\\\\\\\\)+)(?:[^\\{\\$\"\\\\]|(?=\")))))|((?<!\\\\)(?:(?:(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\$))|(?:\\\\\\$)))|((?<!\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\}))|(?:(\\\\\\\\)(\\\\\\\\))|(\\\\(?:[\"\\\\\\/bfnrt]|(?:u[0-9a-fA-F]{4})))|((?:\\\\.))|([^\\\\\\n\\}\"]))?)(?:(?:((?!\\\\)(?:(?:(?:\\\\\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:[^\\{\\$\"\\\\]|(?=\")))|(?:(?:(?:\\\\\\\\\\\\\\\\)+)(?:[^\\{\\$\"\\\\]|(?=\")))))|((?<!\\\\)(?:(?:(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\$))|(?:\\\\\\$)))|((?<!\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\}))|(?:(\\\\\\\\)(\\\\\\\\))|(\\\\(?:[\"\\\\\\/bfnrt]|(?:u[0-9a-fA-F]{4})))|((?:\\\\.))|([^\\\\\\n\\}\"]))*?))(\\}))))|((?:(?:(\\$)(\\{))([0-9]+)(:)((?:(?:((?!\\\\)(?:(?:(?:\\\\\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:[^\\{\\$\"\\\\]|(?=\")))|(?:(?:(?:\\\\\\\\\\\\\\\\)+)(?:[^\\{\\$\"\\\\]|(?=\")))))|((?<!\\\\)(?:(?:(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\$))|(?:\\\\\\$)))|((?<!\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\}))|(?:(\\\\\\\\)(\\\\\\\\))|(\\\\(?:[\"\\\\\\/bfnrt]|(?:u[0-9a-fA-F]{4})))|((?:\\\\.))|([^\\\\\\n\\}\"]))?)(?:(?:((?!\\\\)(?:(?:(?:\\\\\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:[^\\{\\$\"\\\\]|(?=\")))|(?:(?:(?:\\\\\\\\\\\\\\\\)+)(?:[^\\{\\$\"\\\\]|(?=\")))))|((?<!\\\\)(?:(?:(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\$))|(?:\\\\\\$)))|((?<!\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\}))|(?:(\\\\\\\\)(\\\\\\\\))|(\\\\(?:[\"\\\\\\/bfnrt]|(?:u[0-9a-fA-F]{4})))|((?:\\\\.))|([^\\\\\\n\\}\"]))*?))(\\}))))|(?:(?:(?:(\\\\)(\\\\\\/))|(?:(\\\\\\\\\\\\)(\\\\\\/)))|((?:(?:(?:((?!\\\\)(?:(?:(?:\\\\\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:[^\\{\\$\"\\\\]|(?=\")))|(?:(?:(?:\\\\\\\\\\\\\\\\)+)(?:[^\\{\\$\"\\\\]|(?=\")))))|((?<!\\\\)(?:(?:(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\$))|(?:\\\\\\$)))|((?<!\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\}))|(?:(\\\\\\\\)(\\\\\\\\))|(\\\\(?:[\"\\\\\\/bfnrt]|(?:u[0-9a-fA-F]{4})))|((?:\\\\.))|([^\\\\\\n\\}\"]))|[^\\n\\r])*))))", "captures": {"1": {"name": "meta.insertion.simple.json.comments.snippets punctuation.section.insertion.dollar.simple.json.comments.snippets keyword.operator.insertion.json.comments.snippets variable.language.this.json.comments.snippets"}, "2": {"name": "meta.insertion.simple.json.comments.snippets keyword.operator.insertion.json.comments.snippets variable.language.this.json.comments.snippets"}, "3": {"name": "meta.insertion.simple.numeric.json.comments.snippets meta.insertion.format.simple.json.comments.snippets"}, "4": {"name": "punctuation.section.insertion.dollar.simple.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.section.insertion.dollar.simple.json.comments.snippets"}, "5": {"name": "variable.other.normal.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.variable.other.normal.numeric.json.comments.snippets"}, "6": {"name": "meta.insertion.brackets.json.comments.snippets meta.insertion.format.transform.json.comments.snippets"}, "7": {"name": "punctuation.section.insertion.dollar.brackets.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.section.insertion.dollar.brackets.json.comments.snippets"}, "8": {"name": "punctuation.section.insertion.bracket.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.section.insertion.bracket.json.comments.snippets"}, "9": {"name": "variable.other.normal.json.comments.snippets custom.variable.other.normal.numeric.json.comments.snippets"}, "10": {"name": "punctuation.section.insertion.json.comments.snippets punctuation.separator.colon.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.separator.colon.json.comments.snippets"}, "11": {"name": "punctuation.section.regexp.json.comments.snippets support.type.built-in.json.comments.snippets variable.language.special.transform.json.comments.snippets"}, "12": {"name": "support.type.built-in.json.comments.snippets variable.language.special.transform.json.comments.snippets"}, "13": {"name": "punctuation.section.insertion.bracket.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.section.insertion.bracket.json.comments.snippets"}, "14": {"name": "meta.insertion.brackets.json.comments.snippets meta.insertion.format.plus.json.comments.snippets"}, "15": {"name": "punctuation.section.insertion.dollar.brackets.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.section.insertion.dollar.brackets.json.comments.snippets"}, "16": {"name": "punctuation.section.insertion.bracket.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.section.insertion.bracket.json.comments.snippets"}, "17": {"name": "variable.other.normal.json.comments.snippets custom.variable.other.normal.numeric.json.comments.snippets"}, "18": {"name": "punctuation.section.insertion.json.comments.snippets punctuation.separator.colon.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.separator.colon.json.comments.snippets"}, "19": {"name": "punctuation.separator.plus.json.comments.snippets"}, "20": {"patterns": [{"include": "#special_variables"}, {"include": "#simple_escape_context"}]}, "21": {"patterns": [{"include": "#quad_backslash_match"}, {"include": "#dollar_sign_escape"}, {"include": "#bracket_escape"}, {"include": "#basic_escape"}, {"include": "#invalid_escape"}, {"include": "#normal_characters"}]}, "22": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\$", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}, {"include": "#invalid_escape"}]}, "23": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\}", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}]}, "24": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}, "25": {"name": "constant.character.escape.json.comments.snippets"}, "26": {"name": "constant.character.escape.json.comments.snippets"}, "27": {"name": "constant.character.escape.json.comments.snippets invalid.illegal.unrecognized-string-escape.json.comments.snippets"}, "28": {"name": "string.quoted.double.json.comments.snippets"}, "29": {"patterns": [{"include": "#quad_backslash_match"}, {"include": "#dollar_sign_escape"}, {"include": "#bracket_escape"}, {"include": "#basic_escape"}, {"include": "#invalid_escape"}, {"include": "#normal_characters"}]}, "30": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\$", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}, {"include": "#invalid_escape"}]}, "31": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\}", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}]}, "32": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}, "33": {"name": "constant.character.escape.json.comments.snippets"}, "34": {"name": "constant.character.escape.json.comments.snippets"}, "35": {"name": "constant.character.escape.json.comments.snippets invalid.illegal.unrecognized-string-escape.json.comments.snippets"}, "36": {"name": "string.quoted.double.json.comments.snippets"}, "37": {"name": "punctuation.section.insertion.bracket.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.section.insertion.bracket.json.comments.snippets"}, "38": {"name": "meta.insertion.brackets.json.comments.snippets meta.insertion.format.conditional.json.comments.snippets"}, "39": {"name": "punctuation.section.insertion.dollar.brackets.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.section.insertion.dollar.brackets.json.comments.snippets"}, "40": {"name": "punctuation.section.insertion.bracket.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.section.insertion.bracket.json.comments.snippets"}, "41": {"name": "variable.other.normal.json.comments.snippets custom.variable.other.normal.numeric.json.comments.snippets"}, "42": {"name": "punctuation.section.insertion.json.comments.snippets punctuation.separator.colon.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.separator.colon.json.comments.snippets"}, "43": {"name": "punctuation.separator.conditional.json.comments.snippets keyword.operator.ternary.json.comments.snippets"}, "44": {"patterns": [{"include": "#special_variables"}, {"include": "#simple_escape_context"}]}, "45": {"patterns": [{"include": "#quad_backslash_match"}, {"include": "#dollar_sign_escape"}, {"include": "#bracket_escape"}, {"include": "#basic_escape"}, {"include": "#invalid_escape"}, {"include": "#normal_characters"}]}, "46": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\$", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}, {"include": "#invalid_escape"}]}, "47": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\}", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}]}, "48": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}, "49": {"name": "constant.character.escape.json.comments.snippets"}, "50": {"name": "constant.character.escape.json.comments.snippets"}, "51": {"name": "constant.character.escape.json.comments.snippets invalid.illegal.unrecognized-string-escape.json.comments.snippets"}, "52": {"name": "string.quoted.double.json.comments.snippets"}, "53": {"patterns": [{"include": "#quad_backslash_match"}, {"include": "#dollar_sign_escape"}, {"include": "#bracket_escape"}, {"include": "#basic_escape"}, {"include": "#invalid_escape"}, {"include": "#normal_characters"}]}, "54": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\$", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}, {"include": "#invalid_escape"}]}, "55": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\}", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}]}, "56": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}, "57": {"name": "constant.character.escape.json.comments.snippets"}, "58": {"name": "constant.character.escape.json.comments.snippets"}, "59": {"name": "constant.character.escape.json.comments.snippets invalid.illegal.unrecognized-string-escape.json.comments.snippets"}, "60": {"name": "string.quoted.double.json.comments.snippets"}, "61": {"name": "keyword.operator.ternary.json.comments.snippets"}, "62": {"patterns": [{"include": "#special_variables"}, {"include": "#simple_escape_context"}]}, "63": {"patterns": [{"include": "#quad_backslash_match"}, {"include": "#dollar_sign_escape"}, {"include": "#bracket_escape"}, {"include": "#basic_escape"}, {"include": "#invalid_escape"}, {"include": "#normal_characters"}]}, "64": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\$", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}, {"include": "#invalid_escape"}]}, "65": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\}", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}]}, "66": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}, "67": {"name": "constant.character.escape.json.comments.snippets"}, "68": {"name": "constant.character.escape.json.comments.snippets"}, "69": {"name": "constant.character.escape.json.comments.snippets invalid.illegal.unrecognized-string-escape.json.comments.snippets"}, "70": {"name": "string.quoted.double.json.comments.snippets"}, "71": {"patterns": [{"include": "#quad_backslash_match"}, {"include": "#dollar_sign_escape"}, {"include": "#bracket_escape"}, {"include": "#basic_escape"}, {"include": "#invalid_escape"}, {"include": "#normal_characters"}]}, "72": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\$", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}, {"include": "#invalid_escape"}]}, "73": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\}", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}]}, "74": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}, "75": {"name": "constant.character.escape.json.comments.snippets"}, "76": {"name": "constant.character.escape.json.comments.snippets"}, "77": {"name": "constant.character.escape.json.comments.snippets invalid.illegal.unrecognized-string-escape.json.comments.snippets"}, "78": {"name": "string.quoted.double.json.comments.snippets"}, "79": {"name": "punctuation.section.insertion.bracket.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.section.insertion.bracket.json.comments.snippets"}, "80": {"name": "meta.insertion.brackets.json.comments.snippets meta.insertion.format.remove.json.comments.snippets"}, "81": {"name": "punctuation.section.insertion.dollar.brackets.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.section.insertion.dollar.brackets.json.comments.snippets"}, "82": {"name": "punctuation.section.insertion.bracket.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.section.insertion.bracket.json.comments.snippets"}, "83": {"name": "variable.other.normal.json.comments.snippets custom.variable.other.normal.numeric.json.comments.snippets"}, "84": {"name": "punctuation.section.insertion.json.comments.snippets punctuation.separator.colon.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.separator.colon.json.comments.snippets"}, "85": {"name": "punctuation.separator.dash.json.comments.snippets"}, "86": {"patterns": [{"include": "#special_variables"}, {"include": "#simple_escape_context"}]}, "87": {"patterns": [{"include": "#quad_backslash_match"}, {"include": "#dollar_sign_escape"}, {"include": "#bracket_escape"}, {"include": "#basic_escape"}, {"include": "#invalid_escape"}, {"include": "#normal_characters"}]}, "88": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\$", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}, {"include": "#invalid_escape"}]}, "89": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\}", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}]}, "90": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}, "91": {"name": "constant.character.escape.json.comments.snippets"}, "92": {"name": "constant.character.escape.json.comments.snippets"}, "93": {"name": "constant.character.escape.json.comments.snippets invalid.illegal.unrecognized-string-escape.json.comments.snippets"}, "94": {"name": "string.quoted.double.json.comments.snippets"}, "95": {"patterns": [{"include": "#quad_backslash_match"}, {"include": "#dollar_sign_escape"}, {"include": "#bracket_escape"}, {"include": "#basic_escape"}, {"include": "#invalid_escape"}, {"include": "#normal_characters"}]}, "96": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\$", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}, {"include": "#invalid_escape"}]}, "97": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\}", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}]}, "98": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}, "99": {"name": "constant.character.escape.json.comments.snippets"}, "100": {"name": "constant.character.escape.json.comments.snippets"}, "101": {"name": "constant.character.escape.json.comments.snippets invalid.illegal.unrecognized-string-escape.json.comments.snippets"}, "102": {"name": "string.quoted.double.json.comments.snippets"}, "103": {"name": "punctuation.section.insertion.bracket.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.section.insertion.bracket.json.comments.snippets"}, "104": {"name": "meta.insertion.brackets.json.comments.snippets meta.insertion.format.default.json.comments.snippets"}, "105": {"name": "punctuation.section.insertion.dollar.brackets.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.section.insertion.dollar.brackets.json.comments.snippets"}, "106": {"name": "punctuation.section.insertion.bracket.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.section.insertion.bracket.json.comments.snippets"}, "107": {"name": "variable.other.normal.json.comments.snippets custom.variable.other.normal.numeric.json.comments.snippets"}, "108": {"name": "punctuation.section.insertion.json.comments.snippets punctuation.separator.colon.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.separator.colon.json.comments.snippets"}, "109": {"patterns": [{"include": "#special_variables"}, {"include": "#simple_escape_context"}]}, "110": {"patterns": [{"include": "#quad_backslash_match"}, {"include": "#dollar_sign_escape"}, {"include": "#bracket_escape"}, {"include": "#basic_escape"}, {"include": "#invalid_escape"}, {"include": "#normal_characters"}]}, "111": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\$", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}, {"include": "#invalid_escape"}]}, "112": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\}", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}]}, "113": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}, "114": {"name": "constant.character.escape.json.comments.snippets"}, "115": {"name": "constant.character.escape.json.comments.snippets"}, "116": {"name": "constant.character.escape.json.comments.snippets invalid.illegal.unrecognized-string-escape.json.comments.snippets"}, "117": {"name": "string.quoted.double.json.comments.snippets"}, "118": {"patterns": [{"include": "#quad_backslash_match"}, {"include": "#dollar_sign_escape"}, {"include": "#bracket_escape"}, {"include": "#basic_escape"}, {"include": "#invalid_escape"}, {"include": "#normal_characters"}]}, "119": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\$", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}, {"include": "#invalid_escape"}]}, "120": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\}", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}]}, "121": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}, "122": {"name": "constant.character.escape.json.comments.snippets"}, "123": {"name": "constant.character.escape.json.comments.snippets"}, "124": {"name": "constant.character.escape.json.comments.snippets invalid.illegal.unrecognized-string-escape.json.comments.snippets"}, "125": {"name": "string.quoted.double.json.comments.snippets"}, "126": {"name": "punctuation.section.insertion.bracket.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.section.insertion.bracket.json.comments.snippets"}, "127": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}, "128": {"name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}, "129": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}, "130": {"name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}, "131": {"patterns": [{"include": "#special_variables"}, {"include": "#simple_escape_context"}]}, "132": {"patterns": [{"include": "#quad_backslash_match"}, {"include": "#dollar_sign_escape"}, {"include": "#bracket_escape"}, {"include": "#basic_escape"}, {"include": "#invalid_escape"}, {"include": "#normal_characters"}]}, "133": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\$", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}, {"include": "#invalid_escape"}]}, "134": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\}", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}]}, "135": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}, "136": {"name": "constant.character.escape.json.comments.snippets"}, "137": {"name": "constant.character.escape.json.comments.snippets"}, "138": {"name": "constant.character.escape.json.comments.snippets invalid.illegal.unrecognized-string-escape.json.comments.snippets"}, "139": {"name": "string.quoted.double.json.comments.snippets"}}}]}, "23": {"name": "punctuation.section.regexp.json.comments.snippets"}, "24": {"name": "keyword.other.flag.json.comments.snippets"}, "25": {"name": "punctuation.section.insertion.bracket.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.section.insertion.bracket.json.comments.snippets"}}}, "bnf_text": {"match": "(?:(?:((?!\\\\)(?:(?:(?:\\\\\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:[^\\{\\$\"\\\\]|(?=\")))|(?:(?:(?:\\\\\\\\\\\\\\\\)+)(?:[^\\{\\$\"\\\\]|(?=\")))))|((?<!\\\\)(?:(?:(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\$))|(?:\\\\\\$)))|((?<!\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\}))|(?:(\\\\\\\\)(\\\\\\\\))|(\\\\(?:[\"\\\\\\/bfnrt]|(?:u[0-9a-fA-F]{4})))|((?:\\\\.))|([^\\\\\\n\\}\"]))?)(?:(?:((?!\\\\)(?:(?:(?:\\\\\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:[^\\{\\$\"\\\\]|(?=\")))|(?:(?:(?:\\\\\\\\\\\\\\\\)+)(?:[^\\{\\$\"\\\\]|(?=\")))))|((?<!\\\\)(?:(?:(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\$))|(?:\\\\\\$)))|((?<!\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\}))|(?:(\\\\\\\\)(\\\\\\\\))|(\\\\(?:[\"\\\\\\/bfnrt]|(?:u[0-9a-fA-F]{4})))|((?:\\\\.))|([^\\\\\\n\\}\"]))*?)", "captures": {"0": {"patterns": [{"include": "#special_variables"}, {"include": "#simple_escape_context"}]}, "1": {"patterns": [{"include": "#quad_backslash_match"}, {"include": "#dollar_sign_escape"}, {"include": "#bracket_escape"}, {"include": "#basic_escape"}, {"include": "#invalid_escape"}, {"include": "#normal_characters"}]}, "2": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\$", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}, {"include": "#invalid_escape"}]}, "3": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\}", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}]}, "4": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}, "5": {"name": "constant.character.escape.json.comments.snippets"}, "6": {"name": "constant.character.escape.json.comments.snippets"}, "7": {"name": "constant.character.escape.json.comments.snippets invalid.illegal.unrecognized-string-escape.json.comments.snippets"}, "8": {"name": "string.quoted.double.json.comments.snippets"}, "9": {"patterns": [{"include": "#quad_backslash_match"}, {"include": "#dollar_sign_escape"}, {"include": "#bracket_escape"}, {"include": "#basic_escape"}, {"include": "#invalid_escape"}, {"include": "#normal_characters"}]}, "10": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\$", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}, {"include": "#invalid_escape"}]}, "11": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\}", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}]}, "12": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}, "13": {"name": "constant.character.escape.json.comments.snippets"}, "14": {"name": "constant.character.escape.json.comments.snippets"}, "15": {"name": "constant.character.escape.json.comments.snippets invalid.illegal.unrecognized-string-escape.json.comments.snippets"}, "16": {"name": "string.quoted.double.json.comments.snippets"}}}, "bnf_transform": {"match": "(?:(\\/)((?:(?:(?:(?:(\\\\)(\\\\\\/))|(?:(\\\\\\\\\\\\)(\\\\\\/)))|[^\\/\\n])+))(\\/)(((?:(?:(?:(?:(?:(?:(?:(?:\\$(?:(?<!\\w)(?:TM_SELECTED_TEXT|TM_CURRENT_LINE|TM_CURRENT_WORD|TM_LINE_INDEX|TM_LINE_NUMBER|TM_FILENAME|TM_FILENAME_BASE|TM_DIRECTORY|TM_FILEPATH|RELATIVE_FILEPATH|CLIPBOARD|WORKSPACE_NAME|WORKSPACE_FOLDER|CURSOR_INDEX|CURSOR_NUMBER|CURRENT_YEAR|CURRENT_YEAR_SHORT|CURRENT_MONTH|CURRENT_MONTH_NAME|CURRENT_MONTH_NAME_SHORT|CURRENT_DATE|CURRENT_DAY_NAME|CURRENT_DAY_NAME_SHORT|CURRENT_HOUR|CURRENT_MINUTE|CURRENT_SECOND|CURRENT_SECONDS_UNIX|CURRENT_TIMEZONE_OFFSET|RANDOM|RANDOM_HEX|UUID|BLOCK_COMMENT_START|BLOCK_COMMENT_END|LINE_COMMENT)(?!\\w)))|(?:\\$(?:[0-9]+)))|(?:(?:\\$\\{)(?:[0-9]+):(?:\\/(?:upcase|downcase|capitalize|camelcase|pascalcase))\\}))|(?:(?:\\$\\{)(?:[0-9]+):\\+(?:(?:(?:(?:(?!\\\\)(?:(?:(?:\\\\\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:[^\\{\\$\"\\\\]|(?=\")))|(?:(?:(?:\\\\\\\\\\\\\\\\)+)(?:[^\\{\\$\"\\\\]|(?=\")))))|(?:(?<!\\\\)(?:(?:(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\$))|(?:\\\\\\$)))|(?:(?<!\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\}))|(?:(?:\\\\\\\\)(?:\\\\\\\\))|(?:\\\\(?:[\"\\\\\\/bfnrt]|(?:u[0-9a-fA-F]{4})))|(?:\\\\.)|[^\\\\\\n\\}\"])?)(?:(?:(?:(?!\\\\)(?:(?:(?:\\\\\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:[^\\{\\$\"\\\\]|(?=\")))|(?:(?:(?:\\\\\\\\\\\\\\\\)+)(?:[^\\{\\$\"\\\\]|(?=\")))))|(?:(?<!\\\\)(?:(?:(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\$))|(?:\\\\\\$)))|(?:(?<!\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\}))|(?:(?:\\\\\\\\)(?:\\\\\\\\))|(?:\\\\(?:[\"\\\\\\/bfnrt]|(?:u[0-9a-fA-F]{4})))|(?:\\\\.)|[^\\\\\\n\\}\"])*?))\\}))|(?:(?:\\$\\{)(?:[0-9]+):\\?(?:(?:(?:(?:(?!\\\\)(?:(?:(?:\\\\\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:[^\\{\\$\"\\\\]|(?=\")))|(?:(?:(?:\\\\\\\\\\\\\\\\)+)(?:[^\\{\\$\"\\\\]|(?=\")))))|(?:(?<!\\\\)(?:(?:(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\$))|(?:\\\\\\$)))|(?:(?<!\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\}))|(?:(?:\\\\\\\\)(?:\\\\\\\\))|(?:\\\\(?:[\"\\\\\\/bfnrt]|(?:u[0-9a-fA-F]{4})))|(?:\\\\.)|[^\\\\\\n\\}\"])?)(?:(?:(?:(?!\\\\)(?:(?:(?:\\\\\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:[^\\{\\$\"\\\\]|(?=\")))|(?:(?:(?:\\\\\\\\\\\\\\\\)+)(?:[^\\{\\$\"\\\\]|(?=\")))))|(?:(?<!\\\\)(?:(?:(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\$))|(?:\\\\\\$)))|(?:(?<!\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\}))|(?:(?:\\\\\\\\)(?:\\\\\\\\))|(?:\\\\(?:[\"\\\\\\/bfnrt]|(?:u[0-9a-fA-F]{4})))|(?:\\\\.)|[^\\\\\\n\\}\"])*?)):(?:(?:(?:(?:(?!\\\\)(?:(?:(?:\\\\\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:[^\\{\\$\"\\\\]|(?=\")))|(?:(?:(?:\\\\\\\\\\\\\\\\)+)(?:[^\\{\\$\"\\\\]|(?=\")))))|(?:(?<!\\\\)(?:(?:(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\$))|(?:\\\\\\$)))|(?:(?<!\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\}))|(?:(?:\\\\\\\\)(?:\\\\\\\\))|(?:\\\\(?:[\"\\\\\\/bfnrt]|(?:u[0-9a-fA-F]{4})))|(?:\\\\.)|[^\\\\\\n\\}\"])?)(?:(?:(?:(?!\\\\)(?:(?:(?:\\\\\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:[^\\{\\$\"\\\\]|(?=\")))|(?:(?:(?:\\\\\\\\\\\\\\\\)+)(?:[^\\{\\$\"\\\\]|(?=\")))))|(?:(?<!\\\\)(?:(?:(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\$))|(?:\\\\\\$)))|(?:(?<!\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\}))|(?:(?:\\\\\\\\)(?:\\\\\\\\))|(?:\\\\(?:[\"\\\\\\/bfnrt]|(?:u[0-9a-fA-F]{4})))|(?:\\\\.)|[^\\\\\\n\\}\"])*?))\\}))|(?:(?:\\$\\{)(?:[0-9]+):\\-(?:(?:(?:(?:(?!\\\\)(?:(?:(?:\\\\\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:[^\\{\\$\"\\\\]|(?=\")))|(?:(?:(?:\\\\\\\\\\\\\\\\)+)(?:[^\\{\\$\"\\\\]|(?=\")))))|(?:(?<!\\\\)(?:(?:(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\$))|(?:\\\\\\$)))|(?:(?<!\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\}))|(?:(?:\\\\\\\\)(?:\\\\\\\\))|(?:\\\\(?:[\"\\\\\\/bfnrt]|(?:u[0-9a-fA-F]{4})))|(?:\\\\.)|[^\\\\\\n\\}\"])?)(?:(?:(?:(?!\\\\)(?:(?:(?:\\\\\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:[^\\{\\$\"\\\\]|(?=\")))|(?:(?:(?:\\\\\\\\\\\\\\\\)+)(?:[^\\{\\$\"\\\\]|(?=\")))))|(?:(?<!\\\\)(?:(?:(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\$))|(?:\\\\\\$)))|(?:(?<!\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\}))|(?:(?:\\\\\\\\)(?:\\\\\\\\))|(?:\\\\(?:[\"\\\\\\/bfnrt]|(?:u[0-9a-fA-F]{4})))|(?:\\\\.)|[^\\\\\\n\\}\"])*?))\\}))|(?:(?:\\$\\{)(?:[0-9]+):(?:(?:(?:(?:(?!\\\\)(?:(?:(?:\\\\\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:[^\\{\\$\"\\\\]|(?=\")))|(?:(?:(?:\\\\\\\\\\\\\\\\)+)(?:[^\\{\\$\"\\\\]|(?=\")))))|(?:(?<!\\\\)(?:(?:(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\$))|(?:\\\\\\$)))|(?:(?<!\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\}))|(?:(?:\\\\\\\\)(?:\\\\\\\\))|(?:\\\\(?:[\"\\\\\\/bfnrt]|(?:u[0-9a-fA-F]{4})))|(?:\\\\.)|[^\\\\\\n\\}\"])?)(?:(?:(?:(?!\\\\)(?:(?:(?:\\\\\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:[^\\{\\$\"\\\\]|(?=\")))|(?:(?:(?:\\\\\\\\\\\\\\\\)+)(?:[^\\{\\$\"\\\\]|(?=\")))))|(?:(?<!\\\\)(?:(?:(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\$))|(?:\\\\\\$)))|(?:(?<!\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\}))|(?:(?:\\\\\\\\)(?:\\\\\\\\))|(?:\\\\(?:[\"\\\\\\/bfnrt]|(?:u[0-9a-fA-F]{4})))|(?:\\\\.)|[^\\\\\\n\\}\"])*?))\\}))|(?:(?:(?:\\\\(?:\\\\\\/))|(?:(?:\\\\\\\\\\\\)(?:\\\\\\/)))|(?:(?:(?:(?:(?!\\\\)(?:(?:(?:\\\\\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:[^\\{\\$\"\\\\]|(?=\")))|(?:(?:(?:\\\\\\\\\\\\\\\\)+)(?:[^\\{\\$\"\\\\]|(?=\")))))|(?:(?<!\\\\)(?:(?:(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\$))|(?:\\\\\\$)))|(?:(?<!\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\}))|(?:(?:\\\\\\\\)(?:\\\\\\\\))|(?:\\\\(?:[\"\\\\\\/bfnrt]|(?:u[0-9a-fA-F]{4})))|(?:\\\\.)|[^\\\\\\n\\}\"])|[^\\n\\r])*)))*))(\\/)([igmyu]{0,5}))", "captures": {"1": {"name": "punctuation.section.regexp.json.comments.snippets"}, "2": {"patterns": [{"include": "source.syntax.regexp.tmLanguage"}, {"match": "(?:(?:(\\\\)(\\\\\\/))|(?:(\\\\\\\\\\\\)(\\\\\\/)))", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}, "2": {"name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}, "3": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}, "4": {"name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}}}, {"include": "#simple_escape_context"}]}, "3": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}, "4": {"name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}, "5": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}, "6": {"name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}, "7": {"name": "punctuation.section.regexp.json.comments.snippets"}, "8": {"patterns": [{"match": "\\$\\d+", "name": "variable.language.capture.json.comments.snippets"}, {"match": "\\$\\{\\d+\\}", "name": "variable.language.capture.json.comments.snippets"}, {"include": "#bnf_format"}, {"include": "#regex_backslash_escape"}, {"include": "#bnf_text"}]}, "9": {"patterns": [{"match": "(?:(?:(?:(?:(?:(?:(?:(?:(\\$)((?<!\\w)(?:TM_SELECTED_TEXT|TM_CURRENT_LINE|TM_CURRENT_WORD|TM_LINE_INDEX|TM_LINE_NUMBER|TM_FILENAME|TM_FILENAME_BASE|TM_DIRECTORY|TM_FILEPATH|RELATIVE_FILEPATH|CLIPBOARD|WORKSPACE_NAME|WORKSPACE_FOLDER|CURSOR_INDEX|CURSOR_NUMBER|CURRENT_YEAR|CURRENT_YEAR_SHORT|CURRENT_MONTH|CURRENT_MONTH_NAME|CURRENT_MONTH_NAME_SHORT|CURRENT_DATE|CURRENT_DAY_NAME|CURRENT_DAY_NAME_SHORT|CURRENT_HOUR|CURRENT_MINUTE|CURRENT_SECOND|CURRENT_SECONDS_UNIX|CURRENT_TIMEZONE_OFFSET|RANDOM|RANDOM_HEX|UUID|BLOCK_COMMENT_START|BLOCK_COMMENT_END|LINE_COMMENT)(?!\\w)))|((?:(\\$)([0-9]+))))|((?:(?:(\\$)(\\{))([0-9]+)(:)(?:(\\/)(upcase|downcase|capitalize|camelcase|pascalcase))(\\}))))|((?:(?:(\\$)(\\{))([0-9]+)(:)(\\+)((?:(?:((?!\\\\)(?:(?:(?:\\\\\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:[^\\{\\$\"\\\\]|(?=\")))|(?:(?:(?:\\\\\\\\\\\\\\\\)+)(?:[^\\{\\$\"\\\\]|(?=\")))))|((?<!\\\\)(?:(?:(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\$))|(?:\\\\\\$)))|((?<!\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\}))|(?:(\\\\\\\\)(\\\\\\\\))|(\\\\(?:[\"\\\\\\/bfnrt]|(?:u[0-9a-fA-F]{4})))|((?:\\\\.))|([^\\\\\\n\\}\"]))?)(?:(?:((?!\\\\)(?:(?:(?:\\\\\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:[^\\{\\$\"\\\\]|(?=\")))|(?:(?:(?:\\\\\\\\\\\\\\\\)+)(?:[^\\{\\$\"\\\\]|(?=\")))))|((?<!\\\\)(?:(?:(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\$))|(?:\\\\\\$)))|((?<!\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\}))|(?:(\\\\\\\\)(\\\\\\\\))|(\\\\(?:[\"\\\\\\/bfnrt]|(?:u[0-9a-fA-F]{4})))|((?:\\\\.))|([^\\\\\\n\\}\"]))*?))(\\}))))|((?:(?:(\\$)(\\{))([0-9]+)(:)(\\?)((?:(?:((?!\\\\)(?:(?:(?:\\\\\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:[^\\{\\$\"\\\\]|(?=\")))|(?:(?:(?:\\\\\\\\\\\\\\\\)+)(?:[^\\{\\$\"\\\\]|(?=\")))))|((?<!\\\\)(?:(?:(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\$))|(?:\\\\\\$)))|((?<!\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\}))|(?:(\\\\\\\\)(\\\\\\\\))|(\\\\(?:[\"\\\\\\/bfnrt]|(?:u[0-9a-fA-F]{4})))|((?:\\\\.))|([^\\\\\\n\\}\"]))?)(?:(?:((?!\\\\)(?:(?:(?:\\\\\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:[^\\{\\$\"\\\\]|(?=\")))|(?:(?:(?:\\\\\\\\\\\\\\\\)+)(?:[^\\{\\$\"\\\\]|(?=\")))))|((?<!\\\\)(?:(?:(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\$))|(?:\\\\\\$)))|((?<!\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\}))|(?:(\\\\\\\\)(\\\\\\\\))|(\\\\(?:[\"\\\\\\/bfnrt]|(?:u[0-9a-fA-F]{4})))|((?:\\\\.))|([^\\\\\\n\\}\"]))*?))(:)((?:(?:((?!\\\\)(?:(?:(?:\\\\\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:[^\\{\\$\"\\\\]|(?=\")))|(?:(?:(?:\\\\\\\\\\\\\\\\)+)(?:[^\\{\\$\"\\\\]|(?=\")))))|((?<!\\\\)(?:(?:(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\$))|(?:\\\\\\$)))|((?<!\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\}))|(?:(\\\\\\\\)(\\\\\\\\))|(\\\\(?:[\"\\\\\\/bfnrt]|(?:u[0-9a-fA-F]{4})))|((?:\\\\.))|([^\\\\\\n\\}\"]))?)(?:(?:((?!\\\\)(?:(?:(?:\\\\\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:[^\\{\\$\"\\\\]|(?=\")))|(?:(?:(?:\\\\\\\\\\\\\\\\)+)(?:[^\\{\\$\"\\\\]|(?=\")))))|((?<!\\\\)(?:(?:(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\$))|(?:\\\\\\$)))|((?<!\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\}))|(?:(\\\\\\\\)(\\\\\\\\))|(\\\\(?:[\"\\\\\\/bfnrt]|(?:u[0-9a-fA-F]{4})))|((?:\\\\.))|([^\\\\\\n\\}\"]))*?))(\\}))))|((?:(?:(\\$)(\\{))([0-9]+)(:)(\\-)((?:(?:((?!\\\\)(?:(?:(?:\\\\\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:[^\\{\\$\"\\\\]|(?=\")))|(?:(?:(?:\\\\\\\\\\\\\\\\)+)(?:[^\\{\\$\"\\\\]|(?=\")))))|((?<!\\\\)(?:(?:(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\$))|(?:\\\\\\$)))|((?<!\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\}))|(?:(\\\\\\\\)(\\\\\\\\))|(\\\\(?:[\"\\\\\\/bfnrt]|(?:u[0-9a-fA-F]{4})))|((?:\\\\.))|([^\\\\\\n\\}\"]))?)(?:(?:((?!\\\\)(?:(?:(?:\\\\\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:[^\\{\\$\"\\\\]|(?=\")))|(?:(?:(?:\\\\\\\\\\\\\\\\)+)(?:[^\\{\\$\"\\\\]|(?=\")))))|((?<!\\\\)(?:(?:(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\$))|(?:\\\\\\$)))|((?<!\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\}))|(?:(\\\\\\\\)(\\\\\\\\))|(\\\\(?:[\"\\\\\\/bfnrt]|(?:u[0-9a-fA-F]{4})))|((?:\\\\.))|([^\\\\\\n\\}\"]))*?))(\\}))))|((?:(?:(\\$)(\\{))([0-9]+)(:)((?:(?:((?!\\\\)(?:(?:(?:\\\\\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:[^\\{\\$\"\\\\]|(?=\")))|(?:(?:(?:\\\\\\\\\\\\\\\\)+)(?:[^\\{\\$\"\\\\]|(?=\")))))|((?<!\\\\)(?:(?:(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\$))|(?:\\\\\\$)))|((?<!\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\}))|(?:(\\\\\\\\)(\\\\\\\\))|(\\\\(?:[\"\\\\\\/bfnrt]|(?:u[0-9a-fA-F]{4})))|((?:\\\\.))|([^\\\\\\n\\}\"]))?)(?:(?:((?!\\\\)(?:(?:(?:\\\\\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:[^\\{\\$\"\\\\]|(?=\")))|(?:(?:(?:\\\\\\\\\\\\\\\\)+)(?:[^\\{\\$\"\\\\]|(?=\")))))|((?<!\\\\)(?:(?:(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\$))|(?:\\\\\\$)))|((?<!\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\}))|(?:(\\\\\\\\)(\\\\\\\\))|(\\\\(?:[\"\\\\\\/bfnrt]|(?:u[0-9a-fA-F]{4})))|((?:\\\\.))|([^\\\\\\n\\}\"]))*?))(\\}))))|(?:(?:(?:(\\\\)(\\\\\\/))|(?:(\\\\\\\\\\\\)(\\\\\\/)))|((?:(?:(?:((?!\\\\)(?:(?:(?:\\\\\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:[^\\{\\$\"\\\\]|(?=\")))|(?:(?:(?:\\\\\\\\\\\\\\\\)+)(?:[^\\{\\$\"\\\\]|(?=\")))))|((?<!\\\\)(?:(?:(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\$))|(?:\\\\\\$)))|((?<!\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\}))|(?:(\\\\\\\\)(\\\\\\\\))|(\\\\(?:[\"\\\\\\/bfnrt]|(?:u[0-9a-fA-F]{4})))|((?:\\\\.))|([^\\\\\\n\\}\"]))|[^\\n\\r])*))))", "captures": {"1": {"name": "meta.insertion.simple.json.comments.snippets punctuation.section.insertion.dollar.simple.json.comments.snippets keyword.operator.insertion.json.comments.snippets variable.language.this.json.comments.snippets"}, "2": {"name": "meta.insertion.simple.json.comments.snippets keyword.operator.insertion.json.comments.snippets variable.language.this.json.comments.snippets"}, "3": {"name": "meta.insertion.simple.numeric.json.comments.snippets meta.insertion.format.simple.json.comments.snippets"}, "4": {"name": "punctuation.section.insertion.dollar.simple.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.section.insertion.dollar.simple.json.comments.snippets"}, "5": {"name": "variable.other.normal.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.variable.other.normal.numeric.json.comments.snippets"}, "6": {"name": "meta.insertion.brackets.json.comments.snippets meta.insertion.format.transform.json.comments.snippets"}, "7": {"name": "punctuation.section.insertion.dollar.brackets.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.section.insertion.dollar.brackets.json.comments.snippets"}, "8": {"name": "punctuation.section.insertion.bracket.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.section.insertion.bracket.json.comments.snippets"}, "9": {"name": "variable.other.normal.json.comments.snippets custom.variable.other.normal.numeric.json.comments.snippets"}, "10": {"name": "punctuation.section.insertion.json.comments.snippets punctuation.separator.colon.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.separator.colon.json.comments.snippets"}, "11": {"name": "punctuation.section.regexp.json.comments.snippets support.type.built-in.json.comments.snippets variable.language.special.transform.json.comments.snippets"}, "12": {"name": "support.type.built-in.json.comments.snippets variable.language.special.transform.json.comments.snippets"}, "13": {"name": "punctuation.section.insertion.bracket.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.section.insertion.bracket.json.comments.snippets"}, "14": {"name": "meta.insertion.brackets.json.comments.snippets meta.insertion.format.plus.json.comments.snippets"}, "15": {"name": "punctuation.section.insertion.dollar.brackets.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.section.insertion.dollar.brackets.json.comments.snippets"}, "16": {"name": "punctuation.section.insertion.bracket.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.section.insertion.bracket.json.comments.snippets"}, "17": {"name": "variable.other.normal.json.comments.snippets custom.variable.other.normal.numeric.json.comments.snippets"}, "18": {"name": "punctuation.section.insertion.json.comments.snippets punctuation.separator.colon.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.separator.colon.json.comments.snippets"}, "19": {"name": "punctuation.separator.plus.json.comments.snippets"}, "20": {"patterns": [{"include": "#special_variables"}, {"include": "#simple_escape_context"}]}, "21": {"patterns": [{"include": "#quad_backslash_match"}, {"include": "#dollar_sign_escape"}, {"include": "#bracket_escape"}, {"include": "#basic_escape"}, {"include": "#invalid_escape"}, {"include": "#normal_characters"}]}, "22": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\$", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}, {"include": "#invalid_escape"}]}, "23": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\}", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}]}, "24": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}, "25": {"name": "constant.character.escape.json.comments.snippets"}, "26": {"name": "constant.character.escape.json.comments.snippets"}, "27": {"name": "constant.character.escape.json.comments.snippets invalid.illegal.unrecognized-string-escape.json.comments.snippets"}, "28": {"name": "string.quoted.double.json.comments.snippets"}, "29": {"patterns": [{"include": "#quad_backslash_match"}, {"include": "#dollar_sign_escape"}, {"include": "#bracket_escape"}, {"include": "#basic_escape"}, {"include": "#invalid_escape"}, {"include": "#normal_characters"}]}, "30": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\$", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}, {"include": "#invalid_escape"}]}, "31": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\}", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}]}, "32": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}, "33": {"name": "constant.character.escape.json.comments.snippets"}, "34": {"name": "constant.character.escape.json.comments.snippets"}, "35": {"name": "constant.character.escape.json.comments.snippets invalid.illegal.unrecognized-string-escape.json.comments.snippets"}, "36": {"name": "string.quoted.double.json.comments.snippets"}, "37": {"name": "punctuation.section.insertion.bracket.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.section.insertion.bracket.json.comments.snippets"}, "38": {"name": "meta.insertion.brackets.json.comments.snippets meta.insertion.format.conditional.json.comments.snippets"}, "39": {"name": "punctuation.section.insertion.dollar.brackets.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.section.insertion.dollar.brackets.json.comments.snippets"}, "40": {"name": "punctuation.section.insertion.bracket.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.section.insertion.bracket.json.comments.snippets"}, "41": {"name": "variable.other.normal.json.comments.snippets custom.variable.other.normal.numeric.json.comments.snippets"}, "42": {"name": "punctuation.section.insertion.json.comments.snippets punctuation.separator.colon.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.separator.colon.json.comments.snippets"}, "43": {"name": "punctuation.separator.conditional.json.comments.snippets keyword.operator.ternary.json.comments.snippets"}, "44": {"patterns": [{"include": "#special_variables"}, {"include": "#simple_escape_context"}]}, "45": {"patterns": [{"include": "#quad_backslash_match"}, {"include": "#dollar_sign_escape"}, {"include": "#bracket_escape"}, {"include": "#basic_escape"}, {"include": "#invalid_escape"}, {"include": "#normal_characters"}]}, "46": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\$", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}, {"include": "#invalid_escape"}]}, "47": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\}", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}]}, "48": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}, "49": {"name": "constant.character.escape.json.comments.snippets"}, "50": {"name": "constant.character.escape.json.comments.snippets"}, "51": {"name": "constant.character.escape.json.comments.snippets invalid.illegal.unrecognized-string-escape.json.comments.snippets"}, "52": {"name": "string.quoted.double.json.comments.snippets"}, "53": {"patterns": [{"include": "#quad_backslash_match"}, {"include": "#dollar_sign_escape"}, {"include": "#bracket_escape"}, {"include": "#basic_escape"}, {"include": "#invalid_escape"}, {"include": "#normal_characters"}]}, "54": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\$", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}, {"include": "#invalid_escape"}]}, "55": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\}", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}]}, "56": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}, "57": {"name": "constant.character.escape.json.comments.snippets"}, "58": {"name": "constant.character.escape.json.comments.snippets"}, "59": {"name": "constant.character.escape.json.comments.snippets invalid.illegal.unrecognized-string-escape.json.comments.snippets"}, "60": {"name": "string.quoted.double.json.comments.snippets"}, "61": {"name": "keyword.operator.ternary.json.comments.snippets"}, "62": {"patterns": [{"include": "#special_variables"}, {"include": "#simple_escape_context"}]}, "63": {"patterns": [{"include": "#quad_backslash_match"}, {"include": "#dollar_sign_escape"}, {"include": "#bracket_escape"}, {"include": "#basic_escape"}, {"include": "#invalid_escape"}, {"include": "#normal_characters"}]}, "64": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\$", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}, {"include": "#invalid_escape"}]}, "65": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\}", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}]}, "66": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}, "67": {"name": "constant.character.escape.json.comments.snippets"}, "68": {"name": "constant.character.escape.json.comments.snippets"}, "69": {"name": "constant.character.escape.json.comments.snippets invalid.illegal.unrecognized-string-escape.json.comments.snippets"}, "70": {"name": "string.quoted.double.json.comments.snippets"}, "71": {"patterns": [{"include": "#quad_backslash_match"}, {"include": "#dollar_sign_escape"}, {"include": "#bracket_escape"}, {"include": "#basic_escape"}, {"include": "#invalid_escape"}, {"include": "#normal_characters"}]}, "72": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\$", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}, {"include": "#invalid_escape"}]}, "73": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\}", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}]}, "74": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}, "75": {"name": "constant.character.escape.json.comments.snippets"}, "76": {"name": "constant.character.escape.json.comments.snippets"}, "77": {"name": "constant.character.escape.json.comments.snippets invalid.illegal.unrecognized-string-escape.json.comments.snippets"}, "78": {"name": "string.quoted.double.json.comments.snippets"}, "79": {"name": "punctuation.section.insertion.bracket.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.section.insertion.bracket.json.comments.snippets"}, "80": {"name": "meta.insertion.brackets.json.comments.snippets meta.insertion.format.remove.json.comments.snippets"}, "81": {"name": "punctuation.section.insertion.dollar.brackets.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.section.insertion.dollar.brackets.json.comments.snippets"}, "82": {"name": "punctuation.section.insertion.bracket.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.section.insertion.bracket.json.comments.snippets"}, "83": {"name": "variable.other.normal.json.comments.snippets custom.variable.other.normal.numeric.json.comments.snippets"}, "84": {"name": "punctuation.section.insertion.json.comments.snippets punctuation.separator.colon.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.separator.colon.json.comments.snippets"}, "85": {"name": "punctuation.separator.dash.json.comments.snippets"}, "86": {"patterns": [{"include": "#special_variables"}, {"include": "#simple_escape_context"}]}, "87": {"patterns": [{"include": "#quad_backslash_match"}, {"include": "#dollar_sign_escape"}, {"include": "#bracket_escape"}, {"include": "#basic_escape"}, {"include": "#invalid_escape"}, {"include": "#normal_characters"}]}, "88": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\$", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}, {"include": "#invalid_escape"}]}, "89": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\}", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}]}, "90": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}, "91": {"name": "constant.character.escape.json.comments.snippets"}, "92": {"name": "constant.character.escape.json.comments.snippets"}, "93": {"name": "constant.character.escape.json.comments.snippets invalid.illegal.unrecognized-string-escape.json.comments.snippets"}, "94": {"name": "string.quoted.double.json.comments.snippets"}, "95": {"patterns": [{"include": "#quad_backslash_match"}, {"include": "#dollar_sign_escape"}, {"include": "#bracket_escape"}, {"include": "#basic_escape"}, {"include": "#invalid_escape"}, {"include": "#normal_characters"}]}, "96": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\$", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}, {"include": "#invalid_escape"}]}, "97": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\}", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}]}, "98": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}, "99": {"name": "constant.character.escape.json.comments.snippets"}, "100": {"name": "constant.character.escape.json.comments.snippets"}, "101": {"name": "constant.character.escape.json.comments.snippets invalid.illegal.unrecognized-string-escape.json.comments.snippets"}, "102": {"name": "string.quoted.double.json.comments.snippets"}, "103": {"name": "punctuation.section.insertion.bracket.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.section.insertion.bracket.json.comments.snippets"}, "104": {"name": "meta.insertion.brackets.json.comments.snippets meta.insertion.format.default.json.comments.snippets"}, "105": {"name": "punctuation.section.insertion.dollar.brackets.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.section.insertion.dollar.brackets.json.comments.snippets"}, "106": {"name": "punctuation.section.insertion.bracket.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.section.insertion.bracket.json.comments.snippets"}, "107": {"name": "variable.other.normal.json.comments.snippets custom.variable.other.normal.numeric.json.comments.snippets"}, "108": {"name": "punctuation.section.insertion.json.comments.snippets punctuation.separator.colon.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.separator.colon.json.comments.snippets"}, "109": {"patterns": [{"include": "#special_variables"}, {"include": "#simple_escape_context"}]}, "110": {"patterns": [{"include": "#quad_backslash_match"}, {"include": "#dollar_sign_escape"}, {"include": "#bracket_escape"}, {"include": "#basic_escape"}, {"include": "#invalid_escape"}, {"include": "#normal_characters"}]}, "111": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\$", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}, {"include": "#invalid_escape"}]}, "112": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\}", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}]}, "113": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}, "114": {"name": "constant.character.escape.json.comments.snippets"}, "115": {"name": "constant.character.escape.json.comments.snippets"}, "116": {"name": "constant.character.escape.json.comments.snippets invalid.illegal.unrecognized-string-escape.json.comments.snippets"}, "117": {"name": "string.quoted.double.json.comments.snippets"}, "118": {"patterns": [{"include": "#quad_backslash_match"}, {"include": "#dollar_sign_escape"}, {"include": "#bracket_escape"}, {"include": "#basic_escape"}, {"include": "#invalid_escape"}, {"include": "#normal_characters"}]}, "119": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\$", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}, {"include": "#invalid_escape"}]}, "120": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\}", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}]}, "121": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}, "122": {"name": "constant.character.escape.json.comments.snippets"}, "123": {"name": "constant.character.escape.json.comments.snippets"}, "124": {"name": "constant.character.escape.json.comments.snippets invalid.illegal.unrecognized-string-escape.json.comments.snippets"}, "125": {"name": "string.quoted.double.json.comments.snippets"}, "126": {"name": "punctuation.section.insertion.bracket.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.section.insertion.bracket.json.comments.snippets"}, "127": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}, "128": {"name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}, "129": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}, "130": {"name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}, "131": {"patterns": [{"include": "#special_variables"}, {"include": "#simple_escape_context"}]}, "132": {"patterns": [{"include": "#quad_backslash_match"}, {"include": "#dollar_sign_escape"}, {"include": "#bracket_escape"}, {"include": "#basic_escape"}, {"include": "#invalid_escape"}, {"include": "#normal_characters"}]}, "133": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\$", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}, {"include": "#invalid_escape"}]}, "134": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\}", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}]}, "135": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}, "136": {"name": "constant.character.escape.json.comments.snippets"}, "137": {"name": "constant.character.escape.json.comments.snippets"}, "138": {"name": "constant.character.escape.json.comments.snippets invalid.illegal.unrecognized-string-escape.json.comments.snippets"}, "139": {"name": "string.quoted.double.json.comments.snippets"}}}]}, "10": {"name": "punctuation.section.regexp.json.comments.snippets"}, "11": {"name": "keyword.other.flag.json.comments.snippets"}}, "name": "meta.insertion.transform.json.comments.snippets string.regexp.json.comments.snippets"}, "bnf_var": {"match": "(?<!\\w)(?:[_a-zA-Z][_a-zA-Z0-9]*)(?!\\w)", "name": "variable.other.normal.json.comments.snippets custom.variable.other.normal.named.json.comments.snippets"}, "bnf_var_simple": {"match": "(?<!\\w)(?:[_a-zA-Z][_a-zA-Z0-9]*)(?!\\w)", "name": "variable.other.normal.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.variable.other.normal.named.json.comments.snippets"}, "body_array": {"begin": "\\[", "beginCaptures": {"0": {"name": "punctuation.definition.array.begin.json.comments.snippets"}}, "end": "\\]", "endCaptures": {"0": {"name": "punctuation.definition.array.end.json.comments.snippets"}}, "name": "meta.structure.array.json.comments.snippets", "patterns": [{"include": "#body_value"}, {"match": ",", "name": "punctuation.separator.array.json.comments.snippets"}, {"match": "[^\\s\\]]", "name": "invalid.illegal.expected-array-separator.json.comments.snippets"}]}, "body_comments": {"patterns": [{"begin": "/\\*\\*(?!/)", "captures": {"0": {"name": "punctuation.definition.comment.json.comments.snippets"}}, "end": "\\*/", "name": "comment.block.documentation.json.comments.snippets"}, {"begin": "/\\*", "captures": {"0": {"name": "punctuation.definition.comment.json.comments.snippets"}}, "end": "\\*/", "name": "comment.block.json.comments.snippets"}, {"captures": {"1": {"name": "punctuation.definition.comment.json.comments.snippets"}}, "match": "(//).*$\\n?", "name": "comment.line.double-slash.js"}]}, "body_constant": {"match": "\\b(?:true|false|null)\\b", "name": "constant.language.json.comments.snippets"}, "body_number": {"match": "(?x)        # turn on extended mode\n  -?        # an optional minus\n  (?:\n    0       # a zero\n    |       # ...or...\n    [1-9]   # a 1-9 character\n    \\d*     # followed by zero or more digits\n  )\n  (?:\n    (?:\n      \\.    # a period\n      \\d+   # followed by one or more digits\n    )?\n    (?:\n      [eE]  # an e character\n      [+-]? # followed by an option +/-\n      \\d+   # followed by one or more digits\n    )?      # make exponent optional\n  )?        # make decimal portion optional", "name": "constant.numeric.json.comments.snippets"}, "body_object": {"begin": "\\{", "beginCaptures": {"0": {"name": "punctuation.definition.dictionary.begin.json.comments.snippets"}}, "end": "\\}", "endCaptures": {"0": {"name": "punctuation.definition.dictionary.end.json.comments.snippets"}}, "name": "meta.structure.dictionary.json.comments.snippets", "patterns": [{"comment": "the JSON object key", "include": "#special_object_key"}, {"comment": "the JSON object key", "include": "#body_objectkey"}, {"include": "#body_comments"}, {"begin": ":", "beginCaptures": {"0": {"name": "punctuation.separator.dictionary.key-value.json.comments.snippets"}}, "end": "(,)|(?=\\})", "endCaptures": {"1": {"name": "punctuation.separator.dictionary.pair.json.comments.snippets"}}, "name": "meta.structure.dictionary.value.json.comments.snippets", "patterns": [{"comment": "the JSON object value", "include": "#body_value"}, {"match": "[^\\s,]", "name": "invalid.illegal.expected-dictionary-separator.json.comments.snippets"}]}, {"match": "[^\\s\\}]", "name": "invalid.illegal.expected-dictionary-separator.json.comments.snippets"}]}, "body_objectkey": {"begin": "\"", "beginCaptures": {"0": {"name": "punctuation.support.type.property-name.begin.json.comments.snippets"}}, "end": "\"", "endCaptures": {"0": {"name": "punctuation.support.type.property-name.end.json.comments.snippets"}}, "name": "string.json.comments.snippets support.type.property-name.json.comments.snippets", "patterns": [{"include": "#body_string_key_content"}]}, "body_string": {"begin": "\"", "beginCaptures": {"0": {"name": "punctuation.definition.string.begin.json.comments.snippets"}}, "end": "\"", "endCaptures": {"0": {"name": "punctuation.definition.string.end.json.comments.snippets"}}, "name": "string.quoted.double.json.comments.snippets", "patterns": [{"include": "#body_stringcontent"}]}, "body_stringcontent": {"patterns": [{"match": "(?:\\\\\\\\|\\\\\"|[^\"])++", "captures": {"0": {"patterns": [{"include": "#bnf_any"}, {"include": "#basic_escape"}]}}}]}, "body_value": {"patterns": [{"include": "#body_constant"}, {"include": "#body_number"}, {"include": "#body_string"}, {"include": "#body_array"}, {"include": "#body_object"}, {"include": "#body_comments"}]}, "bracket_escape": {"match": "(?<!\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\})", "captures": {"0": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\}", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}]}}}, "bracket_insertion_ender": {"match": "(\\})", "captures": {"1": {"name": "punctuation.section.insertion.bracket.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.section.insertion.bracket.json.comments.snippets"}}}, "bracket_insertion_starter": {"match": "(?:(\\$)(\\{))", "captures": {"1": {"name": "punctuation.section.insertion.dollar.brackets.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.section.insertion.dollar.brackets.json.comments.snippets"}, "2": {"name": "punctuation.section.insertion.bracket.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.section.insertion.bracket.json.comments.snippets"}}}, "choice_option": {"match": "(?:(?:(?:(\\\\\\\\)(\\\\\\\\))|(?:(\\/\\/)(?:\\,|\\|))|((?!\\\\)(?:(?:(?:\\\\\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:[^\\{\\$\"\\\\]|(?=\")))|(?:(?:(?:\\\\\\\\\\\\\\\\)+)(?:[^\\{\\$\"\\\\]|(?=\")))))|((?<!\\\\)(?:(?:(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\$))|(?:\\\\\\$)))|((?<!\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\}))|(\\\\(?:[\"\\\\\\/bfnrt]|(?:u[0-9a-fA-F]{4})))|[^,}\\|])+)", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}, "2": {"name": "constant.character.escape.json.comments.snippets"}, "3": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}, "4": {"patterns": [{"include": "#quad_backslash_match"}, {"include": "#dollar_sign_escape"}, {"include": "#bracket_escape"}, {"include": "#basic_escape"}, {"include": "#invalid_escape"}, {"include": "#normal_characters"}]}, "5": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\$", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}, {"include": "#invalid_escape"}]}, "6": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\}", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}]}, "7": {"name": "constant.character.escape.json.comments.snippets"}}, "name": "meta.insertion.choice.json.comments.snippets constant.other.option.json.comments.snippets"}, "choice_option_escape": {"match": "(?:(\\/\\/)(?:\\,|\\|))", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}}, "colon_separator": {"match": ":", "name": "punctuation.section.insertion.json.comments.snippets punctuation.separator.colon.json.comments.snippets keyword.operator.insertion.json.comments.snippets custom.punctuation.separator.colon.json.comments.snippets"}, "comments": {"patterns": [{"begin": "/\\*\\*(?!/)", "captures": {"0": {"name": "punctuation.definition.comment.json.comments.snippets"}}, "end": "\\*/", "name": "comment.block.documentation.json.comments.snippets"}, {"begin": "/\\*", "captures": {"0": {"name": "punctuation.definition.comment.json.comments.snippets"}}, "end": "\\*/", "name": "comment.block.json.comments.snippets"}, {"captures": {"1": {"name": "punctuation.definition.comment.json.comments.snippets"}}, "match": "(//).*$\\n?", "name": "comment.line.double-slash.js"}]}, "constant": {"match": "\\b(?:true|false|null)\\b", "name": "constant.language.json.comments.snippets"}, "dollar_sign_escape": {"match": "(?<!\\\\)(?:(?:(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\$))|(?:\\\\\\$))", "captures": {"0": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\$", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}, {"include": "#invalid_escape"}]}}}, "invalid_escape": {"match": "(?:\\\\.)", "name": "constant.character.escape.json.comments.snippets invalid.illegal.unrecognized-string-escape.json.comments.snippets"}, "normal_characters": {"match": "[^\\\\\\n\\}\"]", "name": "string.quoted.double.json.comments.snippets"}, "null_quad_backslash": {"match": "((?!\\\\)(?:(?:(?:\\\\\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:[^\\{\\$\"\\\\]|(?=\")))|(?:(?:(?:\\\\\\\\\\\\\\\\)+)(?:[^\\{\\$\"\\\\]|(?=\")))))", "captures": {"1": {"patterns": [{"include": "#quad_backslash_match"}, {"include": "#dollar_sign_escape"}, {"include": "#bracket_escape"}, {"include": "#basic_escape"}, {"include": "#invalid_escape"}, {"include": "#normal_characters"}]}}}, "number": {"match": "(?x)        # turn on extended mode\n  -?        # an optional minus\n  (?:\n    0       # a zero\n    |       # ...or...\n    [1-9]   # a 1-9 character\n    \\d*     # followed by zero or more digits\n  )\n  (?:\n    (?:\n      \\.    # a period\n      \\d+   # followed by one or more digits\n    )?\n    (?:\n      [eE]  # an e character\n      [+-]? # followed by an option +/-\n      \\d+   # followed by one or more digits\n    )?      # make exponent optional\n  )?        # make decimal portion optional", "name": "constant.numeric.json.comments.snippets"}, "object": {"begin": "\\{", "beginCaptures": {"0": {"name": "punctuation.definition.dictionary.begin.json.comments.snippets"}}, "end": "\\}", "endCaptures": {"0": {"name": "punctuation.definition.dictionary.end.json.comments.snippets"}}, "name": "meta.structure.dictionary.json.comments.snippets", "patterns": [{"comment": "the JSON object key", "include": "#special_object_key"}, {"comment": "the JSON object key", "include": "#objectkey"}, {"include": "#comments"}, {"begin": ":", "beginCaptures": {"0": {"name": "punctuation.separator.dictionary.key-value.json.comments.snippets"}}, "end": "(,)|(?=\\})", "endCaptures": {"1": {"name": "punctuation.separator.dictionary.pair.json.comments.snippets"}}, "name": "meta.structure.dictionary.value.json.comments.snippets", "patterns": [{"comment": "the JSON object value", "include": "#value"}, {"match": "[^\\s,]", "name": "invalid.illegal.expected-dictionary-separator.json.comments.snippets"}]}, {"match": "[^\\s\\}]", "name": "invalid.illegal.expected-dictionary-separator.json.comments.snippets"}]}, "objectkey": {"begin": "\"", "beginCaptures": {"0": {"name": "punctuation.support.type.property-name.begin.json.comments.snippets"}}, "end": "\"", "endCaptures": {"0": {"name": "punctuation.support.type.property-name.end.json.comments.snippets"}}, "name": "string.json.comments.snippets support.type.property-name.json.comments.snippets", "patterns": [{"include": "#string_key_content"}]}, "quad_backslash_match": {"match": "(?:(\\\\\\\\)(\\\\\\\\))", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}, "2": {"name": "constant.character.escape.json.comments.snippets"}}}, "regex_backslash_escape": {"match": "(?:(?:(\\\\)(\\\\\\/))|(?:(\\\\\\\\\\\\)(\\\\\\/)))", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}, "2": {"name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}, "3": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}, "4": {"name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}}}, "simple_escape_context": {"match": "(?:((?!\\\\)(?:(?:(?:\\\\\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:[^\\{\\$\"\\\\]|(?=\")))|(?:(?:(?:\\\\\\\\\\\\\\\\)+)(?:[^\\{\\$\"\\\\]|(?=\")))))|((?<!\\\\)(?:(?:(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\$))|(?:\\\\\\$)))|((?<!\\\\)(?:(?:\\\\\\\\\\\\\\\\)*)(?:(?:\\\\\\\\)\\}))|(?:(\\\\\\\\)(\\\\\\\\))|(\\\\(?:[\"\\\\\\/bfnrt]|(?:u[0-9a-fA-F]{4})))|((?:\\\\.))|([^\\\\\\n\\}\"]))", "captures": {"1": {"patterns": [{"include": "#quad_backslash_match"}, {"include": "#dollar_sign_escape"}, {"include": "#bracket_escape"}, {"include": "#basic_escape"}, {"include": "#invalid_escape"}, {"include": "#normal_characters"}]}, "2": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\$", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}, {"include": "#invalid_escape"}]}, "3": {"patterns": [{"include": "#quad_backslash_match"}, {"match": "(\\\\\\\\)\\}", "captures": {"1": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}}, "name": "punctuation.section.insertion.escape.escapee.json.comments.snippets string.regexp.insertion.escape.json.comments.snippets string.quoted.double.json.comments.snippets"}]}, "4": {"name": "punctuation.section.insertion.escape.escaper.json.comments.snippets comment.block.json.comments.snippets punctuation.definition.comment.insertion.escape.json.comments.snippets"}, "5": {"name": "constant.character.escape.json.comments.snippets"}, "6": {"name": "constant.character.escape.json.comments.snippets"}, "7": {"name": "constant.character.escape.json.comments.snippets invalid.illegal.unrecognized-string-escape.json.comments.snippets"}, "8": {"name": "string.quoted.double.json.comments.snippets"}}}, "special_object_key": {"begin": "(?:(\")(body)(\"))", "end": "(?:(?<=,)|(?=\\}))", "beginCaptures": {"1": {"name": "string.json.comments.snippets support.type.property-name.json.comments.snippets punctuation.support.type.property-name.begin.json.comments.snippets"}, "2": {"name": "string.json.comments.snippets support.type.property-name.json.comments.snippets"}, "3": {"name": "string.json.comments.snippets support.type.property-name.json.comments.snippets punctuation.support.type.property-name.begin.json.comments.snippets"}}, "endCaptures": {}, "patterns": [{"begin": "(:)", "end": "(?:(,)|(?=\\}))", "beginCaptures": {"1": {"name": "punctuation.separator.dictionary.key-value.json.comments.snippets"}}, "endCaptures": {"1": {"name": "punctuation.separator.dictionary.pair.json.comments.snippets"}}, "name": "meta.structure.dictionary.value.json.comments.snippets", "patterns": [{"include": "#body_value"}, {"match": "[^\\s,]", "name": "invalid.illegal.expected-dictionary-separator.json.comments.snippets"}]}]}, "special_variables": {"match": "(?:(\\$)((?<!\\w)(?:TM_SELECTED_TEXT|TM_CURRENT_LINE|TM_CURRENT_WORD|TM_LINE_INDEX|TM_LINE_NUMBER|TM_FILENAME|TM_FILENAME_BASE|TM_DIRECTORY|TM_FILEPATH|RELATIVE_FILEPATH|CLIPBOARD|WORKSPACE_NAME|WORKSPACE_FOLDER|CURSOR_INDEX|CURSOR_NUMBER|CURRENT_YEAR|CURRENT_YEAR_SHORT|CURRENT_MONTH|CURRENT_MONTH_NAME|CURRENT_MONTH_NAME_SHORT|CURRENT_DATE|CURRENT_DAY_NAME|CURRENT_DAY_NAME_SHORT|CURRENT_HOUR|CURRENT_MINUTE|CURRENT_SECOND|CURRENT_SECONDS_UNIX|CURRENT_TIMEZONE_OFFSET|RANDOM|RANDOM_HEX|UUID|BLOCK_COMMENT_START|BLOCK_COMMENT_END|LINE_COMMENT)(?!\\w)))", "captures": {"1": {"name": "meta.insertion.simple.json.comments.snippets punctuation.section.insertion.dollar.simple.json.comments.snippets keyword.operator.insertion.json.comments.snippets variable.language.this.json.comments.snippets"}, "2": {"name": "meta.insertion.simple.json.comments.snippets keyword.operator.insertion.json.comments.snippets variable.language.this.json.comments.snippets"}}}, "string": {"begin": "\"", "beginCaptures": {"0": {"name": "punctuation.definition.string.begin.json.comments.snippets"}}, "end": "\"", "endCaptures": {"0": {"name": "punctuation.definition.string.end.json.comments.snippets"}}, "name": "string.quoted.double.json.comments.snippets", "patterns": [{"include": "#stringcontent"}]}, "string_key_content": {"patterns": [{"include": "#basic_escape"}, {"include": "#invalid_escape"}]}, "stringcontent": {"patterns": [{"include": "#basic_escape"}, {"include": "#invalid_escape"}]}, "value": {"patterns": [{"include": "#constant"}, {"include": "#number"}, {"include": "#string"}, {"include": "#array"}, {"include": "#object"}, {"include": "#comments"}]}}}