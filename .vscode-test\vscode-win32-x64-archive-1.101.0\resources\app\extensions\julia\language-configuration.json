{"comments": {"lineComment": "#", "blockComment": ["#=", "=#"]}, "brackets": [["{", "}"], ["[", "]"], ["(", ")"]], "autoClosingPairs": [["{", "}"], ["[", "]"], ["(", ")"], ["`", "`"], {"open": "\"", "close": "\"", "notIn": ["string", "comment"]}], "surroundingPairs": [["{", "}"], ["[", "]"], ["(", ")"], ["\"", "\""], ["`", "`"]], "folding": {"markers": {"start": "^\\s*#region", "end": "^\\s*#endregion"}}, "indentationRules": {"increaseIndentPattern": "^(\\s*|.*=\\s*|.*@\\w*\\s*)[\\w\\s]*(?:[\"'`][^\"'`]*[\"'`])*[\\w\\s]*\\b(if|while|for|function|macro|(mutable\\s+)?struct|abstract\\s+type|primitive\\s+type|let|quote|try|begin|.*\\)\\s*do|else|elseif|catch|finally)\\b(?!(?:.*\\bend\\b[^\\]]*)|(?:[^\\[]*\\].*)$).*$", "decreaseIndentPattern": "^\\s*(end|else|elseif|catch|finally)\\b.*$"}}