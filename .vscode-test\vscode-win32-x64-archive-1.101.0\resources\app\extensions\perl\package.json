{"name": "perl", "displayName": "%displayName%", "description": "%description%", "version": "1.0.0", "publisher": "vscode", "license": "MIT", "engines": {"vscode": "*"}, "scripts": {"update-grammar": "node ../node_modules/vscode-grammar-updater/bin textmate/perl.tmbundle Syntaxes/Perl.plist ./syntaxes/perl.tmLanguage.json Syntaxes/Perl%206.tmLanguage ./syntaxes/perl6.tmLanguage.json"}, "categories": ["Programming Languages"], "contributes": {"languages": [{"id": "perl", "aliases": ["<PERSON><PERSON>", "perl"], "extensions": [".pl", ".pm", ".pod", ".t", ".PL", ".psgi"], "firstLine": "^#!.*\\bperl\\b", "configuration": "./perl.language-configuration.json"}, {"id": "raku", "aliases": ["<PERSON><PERSON>", "Perl6", "perl6"], "extensions": [".raku", ".rak<PERSON><PERSON>", ".rak<PERSON><PERSON>", ".rak<PERSON><PERSON>", ".nqp", ".p6", ".pl6", ".pm6"], "firstLine": "(^#!.*\\bperl6\\b)|use\\s+v6|raku|=begin\\spod|my\\sclass", "configuration": "./perl6.language-configuration.json"}], "grammars": [{"language": "perl", "scopeName": "source.perl", "path": "./syntaxes/perl.tmLanguage.json", "unbalancedBracketScopes": ["variable.other.predefined.perl"]}, {"language": "raku", "scopeName": "source.perl.6", "path": "./syntaxes/perl6.tmLanguage.json"}]}, "repository": {"type": "git", "url": "https://github.com/microsoft/vscode.git"}}