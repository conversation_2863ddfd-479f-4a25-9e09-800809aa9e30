{"comments": {"lineComment": "//", "blockComment": ["/*", "*/"]}, "brackets": [["{", "}"], ["[", "]"], ["(", ")"]], "autoClosingPairs": [{"open": "{", "close": "}", "notIn": ["string"]}, {"open": "[", "close": "]", "notIn": ["string"]}, {"open": "(", "close": ")", "notIn": ["string"]}, {"open": "'", "close": "'", "notIn": ["string", "comment"]}, {"open": "\"", "close": "\"", "notIn": ["string", "comment"]}, {"open": "/**", "close": " */", "notIn": ["string"]}], "surroundingPairs": [["{", "}"], ["[", "]"], ["(", ")"], ["'", "'"], ["\"", "\""], ["`", "`"]], "indentationRules": {"increaseIndentPattern": "({(?!.*}).*|\\(|\\[|((else(\\s)?)?if|else|for(each)?|while|switch|case).*:)\\s*((/[/*].*|)?$|\\?>)", "decreaseIndentPattern": "^(.*\\*\\/)?\\s*((\\})|(\\)+[;,])|(\\]\\)*[;,])|\\b(else:)|\\b((end(if|for(each)?|while|switch));))"}, "folding": {"markers": {"start": "^\\s*(#|//)region\\b", "end": "^\\s*(#|//)endregion\\b"}}, "wordPattern": "(-?\\d*\\.\\d\\w*)|([^\\-\\`\\~\\!\\@\\#\\%\\^\\&\\*\\(\\)\\=\\+\\[\\{\\]\\}\\\\\\|\\;\\:\\'\\\"\\,\\.\\<\\>\\/\\?\\s]+)", "onEnterRules": [{"beforeText": "^\\s*\\/\\*\\*(?!\\/)([^\\*]|\\*(?!\\/))*$", "afterText": "^\\s*\\*\\/$", "action": {"indent": "indentOutdent", "appendText": " * "}}, {"beforeText": "^\\s*\\/\\*\\*(?!\\/)([^\\*]|\\*(?!\\/))*$", "action": {"indent": "none", "appendText": " * "}}, {"beforeText": "^(\\t|(\\ \\ ))*\\ \\*(\\ ([^\\*]|\\*(?!\\/))*)?$", "action": {"indent": "none", "appendText": "* "}}, {"beforeText": "^(\\t|(\\ \\ ))*\\ \\*\\/\\s*$", "action": {"indent": "none", "removeText": 1}}, {"beforeText": "^(\\t|(\\ \\ ))*\\ \\*[^/]*\\*\\/\\s*$", "action": {"indent": "none", "removeText": 1}}, {"previousLineText": "^\\s*(((else ?)?if|for(each)?|while)\\s*\\(.*\\)\\s*|else\\s*)$", "beforeText": "^\\s+([^{i\\s]|i(?!f\\b))", "action": {"indent": "outdent"}}, {"beforeText": {"pattern": "//.*"}, "afterText": {"pattern": "^(?!\\s*$).+"}, "action": {"indent": "none", "appendText": "// "}}]}