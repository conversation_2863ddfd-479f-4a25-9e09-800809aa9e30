{"comments": {"lineComment": "//", "blockComment": ["/*", "*/"]}, "brackets": [["{", "}"], ["[", "]"], ["(", ")"]], "autoClosingPairs": [["{", "}"], ["[", "]"], ["(", ")"], {"open": "\"", "close": "\"", "notIn": ["string"]}], "surroundingPairs": [["{", "}"], ["[", "]"], ["(", ")"], ["\"", "\""], ["<", ">"]], "indentationRules": {"increaseIndentPattern": "^.*\\{[^}\"']*$|^.*\\([^\\)\"']*$", "decreaseIndentPattern": "^\\s*(\\s*\\/[*].*[*]\\/\\s*)*[})]"}, "folding": {"markers": {"start": "^\\s*//\\s*#?region\\b", "end": "^\\s*//\\s*#?endregion\\b"}}, "onEnterRules": [{"beforeText": {"pattern": "//.*"}, "afterText": {"pattern": "^(?!\\s*$).+"}, "action": {"indent": "none", "appendText": "// "}}]}