{"name": "rust", "displayName": "%displayName%", "description": "%description%", "version": "1.0.0", "publisher": "vscode", "license": "MIT", "engines": {"vscode": "*"}, "scripts": {"update-grammar": "node ./build/update-grammar.mjs"}, "categories": ["Programming Languages"], "contributes": {"languages": [{"id": "rust", "extensions": [".rs"], "aliases": ["Rust", "rust"], "configuration": "./language-configuration.json"}], "grammars": [{"language": "rust", "path": "./syntaxes/rust.tmLanguage.json", "scopeName": "source.rust"}]}, "repository": {"type": "git", "url": "https://github.com/microsoft/vscode.git"}}