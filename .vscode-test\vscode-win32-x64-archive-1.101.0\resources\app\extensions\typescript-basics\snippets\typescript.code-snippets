{"Constructor": {"prefix": "ctor", "body": ["/**", " *", " */", "constructor() {", "\tsuper();", "\t$0", "}"], "description": "<PERSON><PERSON><PERSON><PERSON>"}, "Class Definition": {"prefix": "class", "isFileTemplate": true, "body": ["class ${1:name} {", "\tconstructor(${2:parameters}) {", "\t\t$0", "\t}", "}"], "description": "Class Definition"}, "Public Method Definition": {"prefix": "public method", "body": ["/**", " * ${1:name}", " */", "public ${1:name}() {", "\t$0", "}"], "description": "Public Method Definition"}, "Private Method Definition": {"prefix": "private method", "body": ["private ${1:name}() {", "\t$0", "}"], "description": "Private Method Definition"}, "Import Statement": {"prefix": "import", "body": ["import { $0 } from \"${1:module}\";"], "description": "Import external module"}, "Property getter": {"prefix": "get", "body": ["", "public get ${1:value}() : ${2:string} {", "\t${3:return $0}", "}", ""], "description": "Property getter"}, "Log to the console": {"prefix": "log", "body": ["console.log($1);", "$0"], "description": "Log to the console"}, "Log warning to console": {"prefix": "warn", "body": ["console.warn($1);", "$0"], "description": "Log warning to the console"}, "Log error to console": {"prefix": "error", "body": ["console.error($1);", "$0"], "description": "Log error to the console"}, "Define a full property": {"prefix": "prop", "body": ["", "private _${1:value} : ${2:string};", "public get ${1:value}() : ${2:string} {", "\treturn this._${1:value};", "}", "public set ${1:value}(v : ${2:string}) {", "\tthis._${1:value} = v;", "}", ""], "description": "Define a full property"}, "Triple-slash reference": {"prefix": "ref", "body": ["/// <reference path=\"$1\" />", "$0"], "description": "Triple-slash reference"}, "Property setter": {"prefix": "set", "body": ["", "public set ${1:value}(v : ${2:string}) {", "\tthis.$3 = v;", "}", ""], "description": "Property setter"}, "Throw Exception": {"prefix": "throw", "body": ["throw new Error(\"$1\");", "$0"], "description": "Throw Exception"}, "For Loop": {"prefix": "for", "body": ["for (let ${1:index} = 0; ${1:index} < ${2:array}.length; ${1:index}++) {", "\tconst ${3:element} = ${2:array}[${1:index}];", "\t$TM_SELECTED_TEXT$0", "}"], "description": "For Loop"}, "For-Each Loop using =>": {"prefix": "foreach =>", "body": ["${1:array}.forEach(${2:element} => {", "\t$TM_SELECTED_TEXT$0", "});"], "description": "For-Each Loop using =>"}, "For-In Loop": {"prefix": "forin", "body": ["for (const ${1:key} in ${2:object}) {", "\tif (Object.prototype.hasOwnProperty.call(${2:object}, ${1:key})) {", "\t\tconst ${3:element} = ${2:object}[${1:key}];", "\t\t$TM_SELECTED_TEXT$0", "\t}", "}"], "description": "For-In Loop"}, "For-Of Loop": {"prefix": "forof", "body": ["for (const ${1:element} of ${2:object}) {", "\t$TM_SELECTED_TEXT$0", "}"], "description": "For-Of Loop"}, "For-Await-Of Loop": {"prefix": "for<PERSON><PERSON>f", "body": ["for await (const ${1:element} of ${2:object}) {", "\t$TM_SELECTED_TEXT$0", "}"], "description": "For-Await-Of Loop"}, "Function Statement": {"prefix": "function", "body": ["function ${1:name}(${2:params}:${3:type}) {", "\t$TM_SELECTED_TEXT$0", "}"], "description": "Function Statement"}, "If Statement": {"prefix": "if", "body": ["if (${1:condition}) {", "\t$TM_SELECTED_TEXT$0", "}"], "description": "If Statement"}, "If-Else Statement": {"prefix": "ifelse", "body": ["if (${1:condition}) {", "\t$TM_SELECTED_TEXT$0", "} else {", "\t", "}"], "description": "If-Else Statement"}, "New Statement": {"prefix": "new", "body": ["const ${1:name} = new ${2:type}(${3:arguments});$0"], "description": "New Statement"}, "Switch Statement": {"prefix": "switch", "body": ["switch (${1:key}) {", "\tcase ${2:value}:", "\t\t$0", "\t\tbreak;", "", "\tdefault:", "\t\tbreak;", "}"], "description": "Switch Statement"}, "While Statement": {"prefix": "while", "body": ["while (${1:condition}) {", "\t$TM_SELECTED_TEXT$0", "}"], "description": "While Statement"}, "Do-While Statement": {"prefix": "dowhile", "body": ["do {", "\t$TM_SELECTED_TEXT$0", "} while (${1:condition});"], "description": "Do-While Statement"}, "Try-Catch Statement": {"prefix": "trycatch", "body": ["try {", "\t$TM_SELECTED_TEXT$0", "} catch (${1:error}) {", "\t", "}"], "description": "Try-Catch Statement"}, "Set Timeout Function": {"prefix": "settimeout", "body": ["setTimeout(() => {", "\t$TM_SELECTED_TEXT$0", "}, ${1:timeout});"], "description": "Set Timeout Function"}, "Set Interval Function": {"prefix": "setinterval", "body": ["setInterval(() => {", "\t$TM_SELECTED_TEXT$0", "}, ${1:interval});"], "description": "Set Interval Function"}, "Region Start": {"prefix": "#region", "body": ["//#region $0"], "description": "Folding Region Start"}, "Region End": {"prefix": "#endregion", "body": ["//#endregion"], "description": "Folding Region End"}, "new Promise": {"prefix": "newpromise", "body": ["new Promise<${1:void}>((resolve, reject) => {", "\t$TM_SELECTED_TEXT$0", "})"], "description": "Create a new Promise"}, "Async Function Statement": {"prefix": "async function", "body": ["async function ${1:name}(${2:params}:${3:type}) {", "\t$TM_SELECTED_TEXT$0", "}"], "description": "Async Function Statement"}, "Async Function Expression": {"prefix": "async arrow function", "body": ["async (${1:params}:${2:type}) => {", "\t$TM_SELECTED_TEXT$0", "}"], "description": "Async Function Expression"}}