# 🧪 Manual Testing Guide - Smart AI Generation Alert Extension

## 🚀 Quick Start Testing

### **Step 1: Launch Development Environment**

```bash
# Navigate to project directory
cd d:\Project\codeAlarm

# Start development mode (keeps TypeScript compiling)
npm run watch
```

### **Step 2: Open Extension in VSCode Development Host**

1. **In VSCode**: Press `F5` or go to Run and Debug view (`Ctrl+Shift+D`)
2. **Select**: "Run Extension" configuration
3. **Click**: Play button (▶️)
4. **Result**: New VSCode window opens with your extension loaded

## ✅ **Test Scenarios**

### **Test 1: Extension Activation**

**What to check:**
- Extension appears in Extensions view
- Extension is marked as "Development"
- No error messages in Developer Console

**How to verify:**
1. In Extension Development Host window
2. Go to Extensions view (`Ctrl+Shift+X`)
3. Search for "Smart AI Generation Alert"
4. Should show as enabled with "Development" label

### **Test 2: Command Registration**

**What to check:**
- Commands are available in Command Palette
- Commands execute without errors

**How to test:**
1. Open Command Palette (`Ctrl+Shift+P`)
2. Type "AI Alert"
3. Should see:
   - "AI Alert: Test Alert Sound"
   - "AI Alert: Toggle Extension On/Off"
4. Try executing each command

**Expected results:**
- Test Sound: Shows info message "Test sound functionality will be implemented..."
- Toggle: Shows status message and changes enabled state

### **Test 3: Configuration Management**

**What to check:**
- Settings are visible and editable
- Changes persist correctly
- Default values are correct

**How to test:**
1. Open Settings (`Ctrl+,` or `File > Preferences > Settings`)
2. Search for "AI Alert"
3. Should see these settings:
   - ✅ Enable/disable extension (default: true)
   - ✅ Countdown seconds (default: 15, range: 5-60)
   - ✅ Terminal use threshold (default: 10, range: 1-30)
   - ✅ Recent terminal threshold (default: 1, range: 0.5-10)

**Test modifications:**
1. Change "Countdown seconds" to 20
2. Toggle "Enable/disable extension" off and on
3. Verify changes are saved (close/reopen settings)

### **Test 4: Toggle Command Functionality**

**What to check:**
- Toggle command changes configuration
- Status messages are accurate

**How to test:**
1. Note current enabled state in settings
2. Run "AI Alert: Toggle Extension On/Off" command
3. Check settings - enabled state should be opposite
4. Run command again - should return to original state

### **Test 5: Extension Lifecycle**

**What to check:**
- Extension activates properly
- No errors during startup
- Clean disposal when closing

**How to test:**
1. Close Extension Development Host window
2. Press `F5` again to relaunch
3. Verify extension loads without errors
4. Check Developer Console for any error messages

## 🔍 **Debugging and Troubleshooting**

### **View Developer Console**
1. In Extension Development Host window
2. `Help > Toggle Developer Tools`
3. Check Console tab for errors

### **Common Issues and Solutions**

**Issue**: Extension doesn't appear in Extensions view
- **Solution**: Ensure you're in the Extension Development Host window (not main VSCode)

**Issue**: Commands not found in Command Palette
- **Solution**: Check Developer Console for activation errors

**Issue**: Settings don't appear
- **Solution**: Verify package.json configuration section is correct

**Issue**: Changes don't take effect
- **Solution**: Restart Extension Development Host (`F5` again)

## 📊 **Test Results Checklist**

Use this checklist to verify all functionality:

### **Extension Infrastructure**
- [ ] Extension appears in Extensions view
- [ ] Extension marked as "Development"
- [ ] No errors in Developer Console
- [ ] Extension activates on startup

### **Commands**
- [ ] "Test Alert Sound" command available
- [ ] "Toggle Extension On/Off" command available
- [ ] Test Sound shows placeholder message
- [ ] Toggle command changes enabled state
- [ ] Toggle command shows status message

### **Configuration**
- [ ] Settings appear in VSCode Settings
- [ ] All 4 configuration options visible
- [ ] Default values are correct
- [ ] Settings can be modified
- [ ] Changes persist after restart

### **User Experience**
- [ ] Commands have clear, descriptive names
- [ ] Status messages are informative
- [ ] No unexpected error dialogs
- [ ] Extension behaves predictably

## 🎯 **Next Steps After Manual Testing**

### **If All Tests Pass**
✅ Your extension infrastructure is working correctly!
- Continue implementing core alarm logic
- Add sound management functionality
- Implement event handlers for text changes

### **If Tests Fail**
❌ Check the specific failure points:
- Review error messages in Developer Console
- Verify package.json configuration
- Check TypeScript compilation errors
- Ensure all dependencies are installed

### **Development Workflow**
1. **Make code changes** in src/ directory
2. **Save files** (TypeScript auto-compiles with `npm run watch`)
3. **Reload Extension Development Host** (`Ctrl+R` or restart with `F5`)
4. **Test changes** using this manual testing guide
5. **Repeat** until functionality works as expected

---

**🎉 Your extension is ready for development and testing!**

Focus on implementing the core alarm logic while using this manual testing approach to verify functionality as you build it.
