/**
 * Alarm Manager for Smart AI Generation Alert Extension
 * Handles core alarm logic, state management, and timer operations
 */

import { AlarmState, AlarmConfig } from './types';

/**
 * AlarmManager class handles the core business logic
 * TODO: Implement in subsequent task "Implement Core Alarm Management Logic"
 */
export class AlarmManager {
    private state: AlarmState;
    private config: AlarmConfig;

    constructor(config: AlarmConfig) {
        this.config = config;
        this.state = {
            lastCodeChangeTimestamp: null,
            lastTerminalFocusTimestamp: null,
            alarmTimerId: null,
            isExtensionActive: true
        };
    }

    /**
     * Handle code change events
     */
    public handleCodeChange(): void {
        if (!this.config.enabled) {
            return;
        }

        console.log('[AlarmManager] Code change detected');

        // Clear existing timer
        this.clearExistingTimer();

        // Update timestamp
        this.state.lastCodeChangeTimestamp = Date.now();

        // Start alarm countdown
        this.startAlarmCountdown();
    }

    /**
     * Handle user activity events (cursor movement, additional edits)
     */
    public handleUserActivity(): void {
        console.log('[AlarmManager] User activity detected - clearing timer');
        this.clearExistingTimer();
    }

    /**
     * Handle terminal focus events
     */
    public handleTerminalFocus(): void {
        console.log('[AlarmManager] Terminal focus detected');
        this.state.lastTerminalFocusTimestamp = Date.now();
        this.clearExistingTimer(); // Terminal focus is user activity
    }

    /**
     * Start alarm countdown timer
     */
    private startAlarmCountdown(): void {
        const countdownMs = this.config.countdownSeconds * 1000;
        console.log(`[AlarmManager] Starting ${this.config.countdownSeconds}s countdown`);

        this.state.alarmTimerId = setTimeout(() => {
            this.runAlarmChecks();
        }, countdownMs);
    }

    /**
     * Clear existing alarm timer
     */
    private clearExistingTimer(): void {
        if (this.state.alarmTimerId) {
            clearTimeout(this.state.alarmTimerId);
            this.state.alarmTimerId = null;
            console.log('[AlarmManager] Timer cleared');
        }
    }

    /**
     * Run alarm checks when timer expires
     */
    private runAlarmChecks(): void {
        console.log('[AlarmManager] Timer expired - checking alarm conditions');

        if (!this.config.enabled) {
            console.log('[AlarmManager] Extension disabled - no alarm');
            return;
        }

        // Check suppression conditions
        if (this.isImmediateTerminalUse()) {
            console.log('[AlarmManager] Immediate terminal use detected - suppressing alarm');
            return;
        }

        if (this.isRecentTerminalUse()) {
            console.log('[AlarmManager] Recent terminal use detected - suppressing alarm');
            return;
        }

        // Trigger alarm
        this.triggerAlarm();
    }

    /**
     * Check if immediate terminal use should suppress alarm
     */
    private isImmediateTerminalUse(): boolean {
        if (!this.state.lastTerminalFocusTimestamp || !this.state.lastCodeChangeTimestamp) {
            return false;
        }

        const timeSinceCodeChange = Date.now() - this.state.lastCodeChangeTimestamp;
        const timeSinceTerminalFocus = this.state.lastTerminalFocusTimestamp - this.state.lastCodeChangeTimestamp;

        const thresholdMs = this.config.terminalUseThresholdSeconds * 1000;

        return timeSinceTerminalFocus >= 0 && timeSinceTerminalFocus <= thresholdMs;
    }

    /**
     * Check if recent terminal use should suppress alarm
     */
    private isRecentTerminalUse(): boolean {
        if (!this.state.lastTerminalFocusTimestamp) {
            return false;
        }

        const timeSinceTerminalFocus = Date.now() - this.state.lastTerminalFocusTimestamp;
        const thresholdMs = this.config.recentTerminalThresholdMinutes * 60 * 1000;

        return timeSinceTerminalFocus <= thresholdMs;
    }

    /**
     * Trigger the alarm
     */
    private triggerAlarm(): void {
        console.log('[AlarmManager] 🚨 ALARM TRIGGERED! AI code generation likely complete');

        // Show notification
        import('vscode').then(vscode => {
            vscode.window.showInformationMessage(
                '🚨 AI Code Generation Complete!',
                'Dismiss'
            );
        });

        // Play system sound (basic implementation)
        this.playSystemSound();
    }

    /**
     * Play system sound notification
     */
    private playSystemSound(): void {
        try {
            // Use process to play system sound
            const { exec } = require('child_process');
            const os = require('os');

            if (os.platform() === 'win32') {
                exec('powershell -c "[console]::beep(800,500)"');
            } else if (os.platform() === 'darwin') {
                exec('afplay /System/Library/Sounds/Glass.aiff');
            } else {
                exec('paplay /usr/share/sounds/alsa/Front_Left.wav || aplay /usr/share/sounds/alsa/Front_Left.wav');
            }
        } catch (error) {
            console.log('[AlarmManager] Could not play system sound:', error);
        }
    }

    /**
     * Update configuration
     */
    public updateConfiguration(config: AlarmConfig): void {
        this.config = config;
    }

    /**
     * Dispose and cleanup
     */
    public dispose(): void {
        this.clearExistingTimer();
        this.state.isExtensionActive = false;
    }
}
