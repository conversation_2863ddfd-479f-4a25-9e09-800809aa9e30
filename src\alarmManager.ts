/**
 * Alarm Manager for Smart AI Generation Alert Extension
 * Handles core alarm logic, state management, and timer operations
 */

import { AlarmState, AlarmConfig } from './types';

/**
 * AlarmManager class handles the core business logic
 * TODO: Implement in subsequent task "Implement Core Alarm Management Logic"
 */
export class AlarmManager {
    private state: AlarmState;
    private config: AlarmConfig;

    constructor(config: AlarmConfig) {
        this.config = config;
        this.state = {
            lastCodeChangeTimestamp: null,
            lastTerminalFocusTimestamp: null,
            alarmTimerId: null,
            isExtensionActive: true
        };
    }

    /**
     * Handle code change events
     */
    public handleCodeChange(): void {
        if (!this.config.enabled) {
            return;
        }

        console.log('[AlarmManager] Code change detected');

        // Clear existing timer
        this.clearExistingTimer();

        // Update timestamp
        this.state.lastCodeChangeTimestamp = Date.now();

        // Start alarm countdown
        this.startAlarmCountdown();
    }

    /**
     * Handle user activity events (cursor movement, additional edits)
     */
    public handleUserActivity(): void {
        console.log('[AlarmManager] User activity detected - restarting countdown');
        this.clearExistingTimer();

        // Restart countdown for user activity (like cursor movement)
        if (this.state.lastCodeChangeTimestamp &&
            (Date.now() - this.state.lastCodeChangeTimestamp) < (this.config.countdownSeconds * 2 * 1000)) {
            console.log('[AlarmManager] Restarting countdown due to user activity');
            this.startAlarmCountdown();
        }
    }

    /**
     * Handle terminal focus events
     */
    public handleTerminalFocus(): void {
        console.log('[AlarmManager] Terminal focus detected');
        this.state.lastTerminalFocusTimestamp = Date.now();

        // Terminal focus is user activity - clear timer and restart countdown
        this.clearExistingTimer();

        // If we had a recent code change, restart the countdown
        if (this.state.lastCodeChangeTimestamp &&
            (Date.now() - this.state.lastCodeChangeTimestamp) < (this.config.countdownSeconds * 2 * 1000)) {
            console.log('[AlarmManager] Restarting countdown due to terminal activity');
            this.startAlarmCountdown();
        }
    }

    /**
     * Start alarm countdown timer
     */
    private startAlarmCountdown(): void {
        const countdownMs = this.config.countdownSeconds * 1000;
        const now = Date.now();

        console.log(`[AlarmManager] Starting ${this.config.countdownSeconds}s countdown at ${new Date(now).toLocaleTimeString()}`);
        console.log(`[AlarmManager] State - Last code change: ${this.state.lastCodeChangeTimestamp ? new Date(this.state.lastCodeChangeTimestamp).toLocaleTimeString() : 'none'}, Last terminal focus: ${this.state.lastTerminalFocusTimestamp ? new Date(this.state.lastTerminalFocusTimestamp).toLocaleTimeString() : 'none'}`);

        this.state.alarmTimerId = setTimeout(() => {
            this.runAlarmChecks();
        }, countdownMs);
    }

    /**
     * Clear existing alarm timer
     */
    private clearExistingTimer(): void {
        if (this.state.alarmTimerId) {
            clearTimeout(this.state.alarmTimerId);
            this.state.alarmTimerId = null;
            console.log('[AlarmManager] Timer cleared');
        }
    }

    /**
     * Run alarm checks when timer expires
     */
    private runAlarmChecks(): void {
        console.log('[AlarmManager] Timer expired - checking alarm conditions');

        if (!this.config.enabled) {
            console.log('[AlarmManager] Extension disabled - no alarm');
            return;
        }

        // Verify we still have a valid code change timestamp
        if (!this.state.lastCodeChangeTimestamp) {
            console.log('[AlarmManager] No code change timestamp - no alarm');
            return;
        }

        // Check if too much time has passed since code change (safety check)
        const timeSinceCodeChange = Date.now() - this.state.lastCodeChangeTimestamp;
        const maxWaitTime = this.config.countdownSeconds * 1.5 * 1000; // 1.5x the countdown time

        if (timeSinceCodeChange > maxWaitTime) {
            console.log(`[AlarmManager] Too much time passed since code change (${Math.round(timeSinceCodeChange/1000)}s) - no alarm`);
            return;
        }

        // Check suppression conditions
        if (this.isImmediateTerminalUse()) {
            console.log('[AlarmManager] Immediate terminal use detected - suppressing alarm');
            return;
        }

        if (this.isRecentTerminalUse()) {
            console.log('[AlarmManager] Recent terminal use detected - suppressing alarm');
            return;
        }

        // Trigger alarm
        this.triggerAlarm();
    }

    /**
     * Check if immediate terminal use should suppress alarm
     */
    private isImmediateTerminalUse(): boolean {
        if (!this.state.lastTerminalFocusTimestamp || !this.state.lastCodeChangeTimestamp) {
            console.log('[AlarmManager] No terminal focus or code change timestamp - no immediate suppression');
            return false;
        }

        const now = Date.now();
        const timeSinceCodeChange = now - this.state.lastCodeChangeTimestamp;
        const timeSinceTerminalFocus = now - this.state.lastTerminalFocusTimestamp;

        // Check if terminal was used AFTER the code change and within threshold
        const terminalAfterCode = this.state.lastTerminalFocusTimestamp > this.state.lastCodeChangeTimestamp;
        const terminalWithinThreshold = (this.state.lastTerminalFocusTimestamp - this.state.lastCodeChangeTimestamp) <= (this.config.terminalUseThresholdSeconds * 1000);

        const thresholdMs = this.config.terminalUseThresholdSeconds * 1000;

        console.log(`[AlarmManager] Terminal check - Time since code change: ${timeSinceCodeChange}ms, Time since terminal focus: ${timeSinceTerminalFocus}ms, Terminal after code: ${terminalAfterCode}, Within threshold: ${terminalWithinThreshold}, Threshold: ${thresholdMs}ms`);

        const isImmediate = terminalAfterCode && terminalWithinThreshold;
        console.log(`[AlarmManager] Immediate terminal use: ${isImmediate}`);

        return isImmediate;
    }

    /**
     * Check if recent terminal use should suppress alarm
     */
    private isRecentTerminalUse(): boolean {
        if (!this.state.lastTerminalFocusTimestamp) {
            console.log('[AlarmManager] No terminal focus timestamp - no recent suppression');
            return false;
        }

        const timeSinceTerminalFocus = Date.now() - this.state.lastTerminalFocusTimestamp;
        const thresholdMs = this.config.recentTerminalThresholdMinutes * 60 * 1000;

        console.log(`[AlarmManager] Recent terminal check - Time since terminal focus: ${timeSinceTerminalFocus}ms (${Math.round(timeSinceTerminalFocus/1000)}s), Threshold: ${thresholdMs}ms (${this.config.recentTerminalThresholdMinutes}min)`);

        const isRecent = timeSinceTerminalFocus <= thresholdMs;
        console.log(`[AlarmManager] Recent terminal use: ${isRecent}`);

        return isRecent;
    }

    /**
     * Trigger the alarm
     */
    private triggerAlarm(): void {
        console.log('[AlarmManager] 🚨 ALARM TRIGGERED! AI code generation likely complete');

        // Show prominent notification
        import('vscode').then(vscode => {
            vscode.window.showWarningMessage(
                '🚨 AI Code Generation Complete! 🚨',
                'Dismiss',
                'Settings'
            ).then(selection => {
                if (selection === 'Settings') {
                    vscode.commands.executeCommand('workbench.action.openSettings', 'aiAlert');
                }
            });
        });

        // Play multiple system sounds for longer alarm
        this.playExtendedAlarm();
    }

    /**
     * Play extended alarm sound
     */
    private playExtendedAlarm(): void {
        try {
            const { exec } = require('child_process');
            const os = require('os');

            if (os.platform() === 'win32') {
                // Play multiple beeps for Windows
                exec('powershell -c "[console]::beep(800,300); Start-Sleep -Milliseconds 200; [console]::beep(1000,300); Start-Sleep -Milliseconds 200; [console]::beep(800,300)"');
            } else if (os.platform() === 'darwin') {
                // Play system sound multiple times for macOS
                exec('afplay /System/Library/Sounds/Glass.aiff & sleep 0.5 && afplay /System/Library/Sounds/Glass.aiff & sleep 0.5 && afplay /System/Library/Sounds/Glass.aiff');
            } else {
                // Play system sound multiple times for Linux
                exec('(paplay /usr/share/sounds/alsa/Front_Left.wav || aplay /usr/share/sounds/alsa/Front_Left.wav) && sleep 0.5 && (paplay /usr/share/sounds/alsa/Front_Left.wav || aplay /usr/share/sounds/alsa/Front_Left.wav)');
            }

            console.log('[AlarmManager] Extended alarm sound played');
        } catch (error) {
            console.log('[AlarmManager] Could not play extended alarm sound:', error);
        }
    }

    /**
     * Update configuration
     */
    public updateConfiguration(config: AlarmConfig): void {
        this.config = config;
    }

    /**
     * Dispose and cleanup
     */
    public dispose(): void {
        this.clearExistingTimer();
        this.state.isExtensionActive = false;
    }
}
