/**
 * Event Handlers for Smart AI Generation Alert Extension
 * Manages VSCode event listeners and routing
 */

import * as vscode from 'vscode';
import { EventDisposables } from './types';

/**
 * EventHandlers class manages all VSCode event listeners
 * TODO: Implement in subsequent task "Implement Core Event Handling System"
 */
export class EventHandlers {
    private disposables: EventDisposables;
    private alarmManager: any; // Will be properly typed when AlarmManager is implemented

    constructor(alarmManager: any) {
        this.alarmManager = alarmManager;
        this.disposables = {
            textDocumentChange: null,
            editorSelectionChange: null,
            terminalFocusChange: null,
            configurationChange: null
        };
    }

    /**
     * Set up all event listeners
     */
    public setupEventListeners(): void {
        console.log('[EventHandlers] Setting up event listeners');

        // Register text document change listener
        this.disposables.textDocumentChange = vscode.workspace.onDidChangeTextDocument((event) => {
            this.handleTextDocumentChange(event);
        });

        // Register editor selection change listener
        this.disposables.editorSelectionChange = vscode.window.onDidChangeTextEditorSelection((event) => {
            this.handleEditorSelectionChange(event);
        });

        // Register terminal focus change listener
        this.disposables.terminalFocusChange = vscode.window.onDidChangeActiveTerminal((terminal) => {
            this.handleTerminalFocusChange(terminal);
        });

        console.log('[EventHandlers] Event listeners registered successfully');
    }

    /**
     * Handle text document change events
     */
    private handleTextDocumentChange(event: vscode.TextDocumentChangeEvent): void {
        // Filter out non-relevant document changes
        if (!this.isRelevantDocumentChange(event.document)) {
            return;
        }

        // Check if changes are substantial (not just cursor movement)
        if (event.contentChanges.length === 0) {
            return;
        }

        // Check for substantial changes (more than just whitespace)
        const hasSubstantialChanges = event.contentChanges.some(change =>
            change.text.trim().length > 0 || change.rangeLength > 0
        );

        if (hasSubstantialChanges) {
            console.log('[EventHandlers] Substantial text change detected in:', event.document.fileName);
            this.alarmManager.handleCodeChange();
        }
    }

    /**
     * Handle editor selection change events
     */
    private handleEditorSelectionChange(event: vscode.TextEditorSelectionChangeEvent): void {
        // Only treat as user activity if it's a manual selection change
        if (event.kind === vscode.TextEditorSelectionChangeKind.Mouse ||
            event.kind === vscode.TextEditorSelectionChangeKind.Keyboard) {
            console.log('[EventHandlers] User selection change detected');
            this.alarmManager.handleUserActivity();
        }
    }

    /**
     * Handle terminal focus change events
     */
    private handleTerminalFocusChange(terminal: vscode.Terminal | undefined): void {
        if (terminal) {
            console.log('[EventHandlers] Terminal focus change detected');
            this.alarmManager.handleTerminalFocus();
        }
    }

    /**
     * Filter document changes to exclude non-relevant changes
     */
    private isRelevantDocumentChange(document: vscode.TextDocument): boolean {
        // Exclude output channels and special documents
        if (document.uri.scheme !== 'file') {
            return false;
        }

        // Exclude certain file types
        const fileName = document.fileName.toLowerCase();
        const excludePatterns = [
            '.git',
            'node_modules',
            '.vscode',
            'package-lock.json',
            '.log'
        ];

        if (excludePatterns.some(pattern => fileName.includes(pattern))) {
            return false;
        }

        // Include code files
        const codeExtensions = [
            '.js', '.ts', '.jsx', '.tsx', '.py', '.java', '.cpp', '.c', '.cs',
            '.php', '.rb', '.go', '.rs', '.swift', '.kt', '.scala', '.html',
            '.css', '.scss', '.less', '.vue', '.svelte', '.md', '.json', '.xml'
        ];

        return codeExtensions.some(ext => fileName.endsWith(ext));
    }

    /**
     * Dispose all event listeners
     */
    public dispose(): void {
        Object.values(this.disposables).forEach(disposable => {
            if (disposable) {
                disposable.dispose();
            }
        });

        // Reset disposables
        this.disposables = {
            textDocumentChange: null,
            editorSelectionChange: null,
            terminalFocusChange: null,
            configurationChange: null
        };
    }
}
