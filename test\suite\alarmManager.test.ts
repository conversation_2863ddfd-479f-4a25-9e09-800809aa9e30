/**
 * AlarmManager unit tests
 */

import * as assert from 'assert';
import { AlarmManager } from '../../src/alarmManager';
import { DEFAULT_CONFIG } from '../../src/types';

suite('AlarmManager Test Suite', () => {
    let alarmManager: AlarmManager;

    setup(() => {
        alarmManager = new AlarmManager(DEFAULT_CONFIG);
    });

    teardown(() => {
        alarmManager.dispose();
    });

    test('Should initialize with provided configuration', () => {
        // Test that AlarmManager can be instantiated without error
        assert.ok(alarmManager, 'AlarmManager should be instantiated');
    });

    test('Should handle code change events without error', () => {
        try {
            alarmManager.handleCodeChange();
            assert.ok(true, 'handleCodeChange should not throw error');
        } catch (error) {
            assert.fail(`handleCodeChange should not throw error: ${error}`);
        }
    });

    test('Should handle user activity events without error', () => {
        try {
            alarmManager.handleUserActivity();
            assert.ok(true, 'handleUserActivity should not throw error');
        } catch (error) {
            assert.fail(`handleUserActivity should not throw error: ${error}`);
        }
    });

    test('Should handle terminal focus events without error', () => {
        try {
            alarmManager.handleTerminalFocus();
            assert.ok(true, 'handleTerminalFocus should not throw error');
        } catch (error) {
            assert.fail(`handleTerminalFocus should not throw error: ${error}`);
        }
    });

    test('Should update configuration without error', () => {
        const newConfig = {
            ...DEFAULT_CONFIG,
            countdownSeconds: 20
        };

        try {
            alarmManager.updateConfiguration(newConfig);
            assert.ok(true, 'updateConfiguration should not throw error');
        } catch (error) {
            assert.fail(`updateConfiguration should not throw error: ${error}`);
        }
    });

    test('Should dispose without error', () => {
        try {
            alarmManager.dispose();
            assert.ok(true, 'dispose should not throw error');
        } catch (error) {
            assert.fail(`dispose should not throw error: ${error}`);
        }
    });
});
