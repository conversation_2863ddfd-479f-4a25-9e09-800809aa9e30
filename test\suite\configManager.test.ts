/**
 * ConfigManager unit tests
 */

import * as assert from 'assert';
import { ConfigManager } from '../../src/configManager';
import { DEFAULT_CONFIG } from '../../src/types';

suite('ConfigManager Test Suite', () => {
    let configManager: ConfigManager;

    setup(() => {
        configManager = new ConfigManager();
    });

    teardown(() => {
        configManager.dispose();
    });

    test('Should initialize with default configuration', () => {
        const config = configManager.getConfiguration();
        
        assert.strictEqual(config.enabled, DEFAULT_CONFIG.enabled, 'Should have default enabled value');
        assert.strictEqual(config.countdownSeconds, DEFAULT_CONFIG.countdownSeconds, 'Should have default countdown seconds');
        assert.strictEqual(config.terminalUseThresholdSeconds, DEFAULT_CONFIG.terminalUseThresholdSeconds, 'Should have default terminal threshold');
        assert.strictEqual(config.recentTerminalThresholdMinutes, DEFAULT_CONFIG.recentTerminalThresholdMinutes, 'Should have default recent terminal threshold');
    });

    test('Should return correct countdown seconds', () => {
        const countdownSeconds = configManager.getCountdownSeconds();
        assert.strictEqual(countdownSeconds, DEFAULT_CONFIG.countdownSeconds, 'Should return default countdown seconds');
    });

    test('Should return correct terminal threshold seconds', () => {
        const thresholdSeconds = configManager.getTerminalThresholdSeconds();
        assert.strictEqual(thresholdSeconds, DEFAULT_CONFIG.terminalUseThresholdSeconds, 'Should return default terminal threshold seconds');
    });

    test('Should return correct recent terminal threshold minutes', () => {
        const thresholdMinutes = configManager.getRecentTerminalThresholdMinutes();
        assert.strictEqual(thresholdMinutes, DEFAULT_CONFIG.recentTerminalThresholdMinutes, 'Should return default recent terminal threshold minutes');
    });

    test('Should return correct enabled state', () => {
        const enabled = configManager.isEnabled();
        assert.strictEqual(enabled, DEFAULT_CONFIG.enabled, 'Should return default enabled state');
    });

    test('Should handle configuration change callback registration', () => {
        // This tests that the method exists and doesn't throw
        // Full implementation will be tested when the method is implemented
        try {
            configManager.onConfigurationChanged(() => {
                // Test callback
            });
            assert.ok(true, 'Configuration change callback registration should not throw');
        } catch (error) {
            assert.fail(`Configuration change callback registration should not throw: ${error}`);
        }
    });

    test('Should dispose without error', () => {
        try {
            configManager.dispose();
            assert.ok(true, 'Dispose should not throw error');
        } catch (error) {
            assert.fail(`Dispose should not throw error: ${error}`);
        }
    });
});
