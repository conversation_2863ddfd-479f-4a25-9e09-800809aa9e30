/**
 * EventHandlers unit tests
 */

import * as assert from 'assert';
import { EventHandlers } from '../../src/eventHandlers';
import { AlarmManager } from '../../src/alarmManager';
import { DEFAULT_CONFIG } from '../../src/types';

// Import Mocha globals
const { suite, test, setup, teardown } = require('mocha');

suite('EventHandlers Test Suite', () => {
    let eventHandlers: EventHandlers;
    let alarmManager: AlarmManager;

    setup(() => {
        alarmManager = new AlarmManager(DEFAULT_CONFIG);
        eventHandlers = new EventHandlers(alarmManager);
    });

    teardown(() => {
        eventHandlers.dispose();
        alarmManager.dispose();
    });

    test('Should initialize with alarm manager', () => {
        assert.ok(eventHandlers, 'EventHandlers should be instantiated');
    });

    test('Should setup event listeners without error', () => {
        try {
            eventHandlers.setupEventListeners();
            assert.ok(true, 'setupEventListeners should not throw error');
        } catch (error) {
            assert.fail(`setupEventListeners should not throw error: ${error}`);
        }
    });

    test('Should dispose without error', () => {
        try {
            eventHandlers.dispose();
            assert.ok(true, 'dispose should not throw error');
        } catch (error) {
            assert.fail(`dispose should not throw error: ${error}`);
        }
    });

    test('Should handle multiple dispose calls', () => {
        try {
            eventHandlers.dispose();
            eventHandlers.dispose(); // Should not throw on second call
            assert.ok(true, 'Multiple dispose calls should not throw error');
        } catch (error) {
            assert.fail(`Multiple dispose calls should not throw error: ${error}`);
        }
    });
});
