/**
 * Extension activation and basic functionality tests
 */

import * as assert from 'assert';
import * as vscode from 'vscode';

suite('Extension Test Suite', () => {
    before(() => {
        vscode.window.showInformationMessage('Start all tests.');
    });

    after(() => {
        vscode.window.showInformationMessage('All tests done!');
    });

    test('Extension should be present', () => {
        const extension = vscode.extensions.getExtension('smart-ai-alert.smart-ai-generation-alert');
        assert.ok(extension, 'Extension should be found');
    });

    test('Extension should activate', async () => {
        const extension = vscode.extensions.getExtension('smart-ai-alert.smart-ai-generation-alert');
        assert.ok(extension, 'Extension should be found');
        
        if (!extension.isActive) {
            await extension.activate();
        }
        
        assert.ok(extension.isActive, 'Extension should be active');
    });

    test('Commands should be registered', async () => {
        const commands = await vscode.commands.getCommands(true);
        
        const expectedCommands = [
            'aiAlert.testSound',
            'aiAlert.toggleEnabled'
        ];
        
        for (const command of expectedCommands) {
            assert.ok(
                commands.includes(command),
                `Command ${command} should be registered`
            );
        }
    });

    test('Configuration should have default values', () => {
        const config = vscode.workspace.getConfiguration('aiAlert');
        
        // Test default configuration values
        assert.strictEqual(config.get('enabled'), true, 'Extension should be enabled by default');
        assert.strictEqual(config.get('countdownSeconds'), 15, 'Default countdown should be 15 seconds');
        assert.strictEqual(config.get('terminalUseThresholdSeconds'), 10, 'Default terminal threshold should be 10 seconds');
        assert.strictEqual(config.get('recentTerminalThresholdMinutes'), 1, 'Default recent terminal threshold should be 1 minute');
    });

    test('Toggle enabled command should work', async () => {
        const config = vscode.workspace.getConfiguration('aiAlert');
        const initialEnabled = config.get<boolean>('enabled', true);
        
        // Execute toggle command
        await vscode.commands.executeCommand('aiAlert.toggleEnabled');
        
        // Check if value was toggled
        const newEnabled = config.get<boolean>('enabled', true);
        assert.strictEqual(newEnabled, !initialEnabled, 'Enabled state should be toggled');
        
        // Toggle back to original state
        await vscode.commands.executeCommand('aiAlert.toggleEnabled');
        const finalEnabled = config.get<boolean>('enabled', true);
        assert.strictEqual(finalEnabled, initialEnabled, 'Should return to original state');
    });

    test('Test sound command should execute without error', async () => {
        // This should not throw an error, even though it's not fully implemented
        try {
            await vscode.commands.executeCommand('aiAlert.testSound');
            assert.ok(true, 'Test sound command executed successfully');
        } catch (error) {
            assert.fail(`Test sound command should not throw error: ${error}`);
        }
    });
});
