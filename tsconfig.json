{"compilerOptions": {"module": "commonjs", "target": "ES2020", "outDir": "out", "lib": ["ES2020"], "sourceMap": true, "rootDir": ".", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declaration": true, "declarationMap": true, "experimentalDecorators": true, "emitDecoratorMetadata": true}, "exclude": ["node_modules", ".vscode-test", "out"], "include": ["src/**/*", "test/**/*"]}